# Form Administration Panel

The Form Administration Panel allows administrators to manage form field configurations, their options, order, multilingual text content, and visual themes without requiring code changes.

## Features

### 🎛️ Form Field Configuration Management
- Create, edit, and delete form field configurations
- Support for different field types: select, multiselect, checkbox, radio
- Multilingual titles, placeholders, and descriptions (Greek/English)
- Active/inactive status management

### 📝 Option Management
- Add, edit, and delete options for each form field
- Drag-and-drop reordering of options
- Multilingual labels (Greek/English)
- Individual option activation/deactivation

### 🎨 Color Scheme & Theme Management
- Create and manage custom form themes
- Customize progress bar colors and gradients
- Configure background colors and text colors
- Button styling and hover states
- Step navigation colors
- Real-time theme preview
- Pre-built theme templates (Ocean, Sunset, Forest, Purple, Dark)
- Theme switching without application restart

### 🌐 Multilingual Support
- Full Greek and English language support
- Separate labels for each language
- Consistent text management across the application

### 🔄 Dynamic Form Loading
- Forms automatically load options from the database
- Fallback to hardcoded options for reliability
- Real-time updates without application restart
- Dynamic theme application

## Access

The Form Administration Panel is accessible through:
1. Navigate to **Settings** (Ρυθμίσεις)
2. Click on the **Forms** (Φόρμες) tab
3. Use the tabs to switch between:
   - **Field Configurations** - Manage form fields and options
   - **Color Themes** - Customize visual appearance
   - **Overview & Quick Actions** - Summary and bulk operations
4. Requires authenticated user access

## Database Schema

### Tables Created

#### `form_field_configs`
Stores form field configuration metadata:
- `id` - Unique identifier
- `name` - Internal field name (e.g., 'musicalInstruments')
- `title_el` - Greek title
- `title_en` - English title
- `placeholder_el` - Greek placeholder text
- `placeholder_en` - English placeholder text
- `description_el` - Greek description
- `description_en` - English description
- `field_type` - Type: select, multiselect, checkbox, radio
- `is_active` - Active status
- `created_at` / `updated_at` - Timestamps

#### `form_field_options`
Stores individual options for each form field:
- `id` - Unique identifier
- `field_config_id` - Reference to form_field_configs
- `label_el` - Greek label
- `label_en` - English label
- `order_index` - Display order
- `is_active` - Active status
- `created_at` / `updated_at` - Timestamps

#### `form_themes`
Stores form theme configurations:
- `id` - Unique identifier
- `name` - Internal theme name (e.g., 'ocean', 'sunset')
- `title_el` - Greek title
- `title_en` - English title
- `is_active` - Active status
- `is_default` - Default theme flag (only one can be default)
- `colors` - JSON object containing all theme colors
- `created_at` / `updated_at` - Timestamps

## Pre-configured Form Fields

The system comes with the following pre-configured form fields:

### Musical & Educational
- **musicalInstruments** - Musical instruments selection
- **musicTheory** - Music theory subjects
- **lessonFormat** - Lesson delivery formats
- **education** - Education levels
- **specialization** - Professional specializations

### Professional & Personal
- **firstAid** - First aid certifications
- **childrenAge** - Children age groups
- **position** - Job positions
- **schedule** - Work schedules
- **duration** - Employment duration
- **candidateType** - Candidate types
- **startDate** - Start date preferences
- **socialMediaPlatform** - Social media platforms

## Usage in Forms

### Dynamic Form Fields
Replace hardcoded form fields with dynamic ones:

```tsx
// Before (hardcoded)
<FormField
  control={control}
  name="musicalInstruments"
  render={({ field }) => (
    <FormItem>
      <FormLabel>Musical Instruments</FormLabel>
      <div className="grid grid-cols-2 gap-2">
        {musicalInstrumentOptions.map((option) => (
          <Checkbox key={option.id} ... />
        ))}
      </div>
    </FormItem>
  )}
/>

// After (dynamic)
<DynamicFormField
  control={control}
  name="musicalInstruments"
  configName="musicalInstruments"
  language="en"
/>
```

### Fallback Support
For reliability, use fallback fields:

```tsx
<FallbackFormField
  control={control}
  name="musicalInstruments"
  title="Musical Instruments"
  options={musicalInstrumentOptions}
  fieldType="multiselect"
  language="en"
/>
```

### Hooks Available

#### `useFormConfigs()`
Load all form configurations:
```tsx
const { configs, loading, error, refetch } = useFormConfigs();
```

#### `useFormConfig(name)`
Load specific form configuration:
```tsx
const { config, loading, error, refetch } = useFormConfig('musicalInstruments');
```

#### `useFormOptions(configName)`
Get formatted options for a form field:
```tsx
const { options, loading, error, config } = useFormOptions('musicalInstruments');
```

### Themed Form Components
Use themed components for consistent styling:

```tsx
import {
  ThemedFormLayout,
  ThemedButton,
  ThemedText,
  FormThemeProvider
} from "@/components/forms/ThemedFormLayout";

function MyForm() {
  return (
    <FormThemeProvider>
      <ThemedFormLayout
        title="My Form"
        currentStep={1}
        totalSteps={3}
        stepTitles={["Step 1", "Step 2", "Step 3"]}
        showProgress={true}
      >
        <ThemedText variant="primary">Form content</ThemedText>
        <ThemedButton variant="primary">Submit</ThemedButton>
      </ThemedFormLayout>
    </FormThemeProvider>
  );
}
```

### Theme Hooks Available

#### `useFormThemes()`
Load all available themes:
```tsx
const { themes, loading, error, refetch } = useFormThemes();
```

#### `useThemeSelection()`
Manage theme selection:
```tsx
const {
  currentTheme,
  selectTheme,
  resetToDefault,
  isUsingDefault
} = useThemeSelection();
```

#### `useThemeClasses()`
Get theme-aware CSS classes:
```tsx
const themeClasses = useThemeClasses();
// Returns: { progressBar, progressFill, formBackground, etc. }
```

## Administration Tasks

### Adding New Form Fields

1. **Create Configuration**:
   - Go to Settings → Forms → Field Configurations
   - Click "Add Configuration"
   - Fill in name, titles, and field type
   - Save the configuration

2. **Add Options**:
   - Click "Manage Options" on the created configuration
   - Add individual options with Greek and English labels
   - Set the display order
   - Activate/deactivate as needed

3. **Update Forms**:
   - Replace hardcoded form fields with `DynamicFormField`
   - Use the configuration name as `configName` prop

### Modifying Existing Options

1. **Reorder Options**:
   - Use the up/down arrows in the options manager
   - Changes are saved automatically

2. **Edit Labels**:
   - Click the edit button on any option
   - Update Greek and English labels
   - Save changes

3. **Add/Remove Options**:
   - Use "Add Option" button to add new choices
   - Use delete button to remove (soft delete)

### Managing Translations

1. **Field Titles**:
   - Edit configuration to update titles
   - Separate fields for Greek and English

2. **Option Labels**:
   - Each option has Greek and English labels
   - Consistent translation management

3. **Placeholders & Descriptions**:
   - Optional placeholder and description text
   - Multilingual support for better UX

### Managing Color Themes

1. **Create New Theme**:
   - Go to Settings → Forms → Color Themes
   - Click "Create Theme"
   - Choose a template or start from scratch
   - Fill in theme name and titles
   - Customize colors using the color picker
   - Set as default if desired

2. **Edit Existing Theme**:
   - Click the edit button on any theme
   - Modify colors, titles, or status
   - Use the preview feature to see changes
   - Save updates

3. **Theme Customization**:
   - **Progress Bar**: Background and fill colors/gradients
   - **Backgrounds**: Form, card, and step backgrounds
   - **Buttons**: Primary and secondary button colors
   - **Text**: Primary, secondary, success, and error text colors
   - **Steps**: Active, inactive, and completed step indicators

4. **Set Default Theme**:
   - Click the star icon on any theme
   - Only one theme can be default at a time
   - Default theme applies to all new forms

5. **Theme Preview**:
   - Click the eye icon to preview any theme
   - See real-time changes as you customize colors
   - Test different color combinations

### Pre-built Themes

The system includes several pre-configured themes:
- **Default**: Standard application colors
- **Ocean**: Blue and cyan color scheme
- **Sunset**: Orange and pink gradients
- **Forest**: Green and emerald tones
- **Purple**: Purple and violet colors
- **Dark**: Dark mode with gray tones

## Migration & Data Import

### Initial Setup
The migration script automatically:
- Creates required database tables
- Sets up indexes for performance
- Configures Row Level Security (RLS)
- Imports existing hardcoded options

### Importing Existing Data
Use the "Import Existing Options" button to:
- Migrate hardcoded form options to database
- Preserve existing functionality
- Enable dynamic management

## Security

### Row Level Security (RLS)
- Read access: Public (for form rendering)
- Write access: Authenticated users only
- Admin-level permissions recommended for modifications

### Data Validation
- Server-side validation for all inputs
- Type checking for field types
- Order index validation
- Multilingual content requirements

## Performance Considerations

### Caching
- Form configurations are cached in React state
- Minimal database queries during form rendering
- Efficient loading with proper indexing

### Optimization
- Lazy loading of form configurations
- Debounced search and filtering
- Optimized database queries with indexes

## Troubleshooting

### Common Issues

1. **Options Not Loading**:
   - Check database connection
   - Verify configuration name matches
   - Check RLS policies

2. **Missing Translations**:
   - Ensure both Greek and English labels are provided
   - Check language prop in components

3. **Order Not Updating**:
   - Verify order_index values
   - Check for database transaction issues

### Fallback Behavior
- Dynamic fields automatically fall back to hardcoded options
- Error boundaries prevent form breakage
- Graceful degradation for better UX

## Future Enhancements

### Planned Features
- Bulk import/export of configurations
- Version control for form changes
- Advanced validation rules
- Custom field types
- Form analytics and usage tracking

### Integration Opportunities
- CMS integration for content management
- API endpoints for external systems
- Webhook notifications for changes
- Audit logging for compliance
