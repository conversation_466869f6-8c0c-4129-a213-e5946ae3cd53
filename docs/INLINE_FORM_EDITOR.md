# Inline Form Editor

The Inline Form Editor provides a live, real-time editing interface for form configurations with instant preview capabilities.

## Features

### 🎯 **Live Form Preview**
- Real-time form rendering as you make changes
- Support for both Candidate and Nanny Request forms
- Language switching (Greek/English)
- Automatic refresh and state management

### ⚡ **Inline Editing**
- Edit form field configurations directly without dialogs
- Live database updates with debouncing (1-second delay)
- Visual feedback for unsaved changes
- Drag-and-drop option reordering

### 🔧 **Configurable Fields**
- **Basic Configuration**: Field name, type, titles, placeholders, descriptions
- **Field Properties**: Active/inactive status, required/optional
- **Options Management**: Add, edit, delete, and reorder field options
- **Multi-language Support**: Greek and English labels for all fields

### 🎨 **User Interface**
- Split-screen layout: Editor on left, Preview on right
- Top navigation bar for form type and language selection
- Tabbed interface for Fields, Labels, and Themes
- Responsive design that works on different screen sizes

## How to Use

### 1. Access the Inline Editor
Navigate to the Form Administration panel and click the "Inline Editor" button in the top-right corner.

### 2. Select Form Type
Use the dropdown in the top navigation to choose between:
- **Candidate Form**: For nanny/tutor applications
- **Nanny Request Form**: For family requests

### 3. Choose Language
Switch between Greek (el) and English (en) to see how forms appear in different languages.

### 4. Edit Fields
1. Select a field from the dropdown in the Fields tab
2. Edit any property in the left panel:
   - Field name and type
   - Greek and English titles
   - Placeholders and descriptions
   - Active/required status
3. Changes are automatically saved after 1 second of inactivity

### 5. Manage Options
For select, multiselect, radio, and checkbox fields:
- **Add Options**: Click "Add Option" button
- **Edit Options**: Modify Greek and English labels inline
- **Reorder Options**: Drag and drop using the grip handle
- **Delete Options**: Click the trash icon
- **Toggle Status**: Use the switch to activate/deactivate options

## Technical Implementation

### Components
- **`InlineFormEditor`**: Main container with navigation and layout
- **`InlineFieldEditor`**: Field-specific editing interface
- **`FormPreview`**: Live form rendering component
- **`useDebounce`**: Custom hook for delayed database updates

### Database Integration
- **Live Updates**: Changes are automatically saved to Supabase
- **Debouncing**: Prevents excessive API calls during rapid typing
- **Error Handling**: Toast notifications for success/failure states
- **Optimistic Updates**: UI updates immediately, syncs with database

### Performance Features
- **Debounced Updates**: 1-second delay before saving changes
- **Selective Rendering**: Only re-renders changed components
- **Efficient Queries**: Uses specific database queries for updates
- **Memory Management**: Proper cleanup of event listeners and timers

## Future Enhancements

### Planned Features
- **Labels Editor**: Inline editing for form labels and text content
- **Theme Editor**: Live color scheme and styling customization
- **Bulk Operations**: Multi-select and batch editing capabilities
- **Version History**: Track and revert changes
- **Import/Export**: Configuration backup and sharing

### Technical Improvements
- **Real-time Collaboration**: Multiple users editing simultaneously
- **Advanced Validation**: Field dependency and constraint checking
- **Performance Optimization**: Virtual scrolling for large option lists
- **Accessibility**: Enhanced keyboard navigation and screen reader support

## Troubleshooting

### Common Issues
1. **Changes not saving**: Check network connection and database permissions
2. **Preview not updating**: Try clicking the refresh button in the preview panel
3. **Drag and drop not working**: Ensure react-beautiful-dnd is properly installed
4. **Performance issues**: Reduce debounce delay or limit concurrent edits

### Error Messages
- **"Failed to update field configuration"**: Database connection or permission issue
- **"Failed to add new option"**: Check field configuration and database schema
- **"Unsaved Changes"**: Indicates pending changes that haven't been saved yet

## API Reference

### Key Functions
- `updateFormConfig(config)`: Updates field configuration
- `updateFormOption(option)`: Updates individual option
- `createFormOption(option)`: Creates new option
- `deleteFormOption(optionId)`: Soft deletes option
- `useDebounce(value, delay)`: Debounces value changes

### Data Structures
- `FormFieldConfig`: Complete field configuration object
- `FormFieldOption`: Individual option within a field
- `FormType`: "candidate" | "nannyRequest"
- `Language`: "el" | "en"
