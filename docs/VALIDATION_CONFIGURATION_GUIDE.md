# Validation Rules and Custom Messages Configuration Guide

## 🎯 **Where to Add Validation Rules and Custom Messages**

I've implemented a complete validation system that allows you to configure validation rules and custom error messages directly in the FormConfigAdmin interface. Here's exactly where and how to use them:

### 📍 **Location 1: FormConfigAdmin Interface**

#### **Access Path:**
1. Navigate to **Form Administration**
2. Click **"Traditional"** mode (if in inline editor)
3. Go to **"Field Configurations"** tab
4. Click **"Add Configuration"** or **Edit** an existing configuration

#### **Validation Configuration Sections:**

##### **1. Conditional Field Settings**
Located in the create/edit dialog, you'll find:
```
┌─ Conditional Field Settings ─────────────────────────┐
│ Conditional Field Key: [startAvailability        ]  │
│ Conditional Value:     [other                    ]  │
│ Operator:              [Equals ▼                 ]  │
└─────────────────────────────────────────────────────┘
```

**Purpose:** Configure when this field should be visible
- **Conditional Field Key:** The field that controls this field's visibility
- **Conditional Value:** The value that triggers this field to show
- **Operator:** How to compare the values (equals, contains, greater_than, etc.)

##### **2. Validation Settings**
Located below the conditional settings:
```
┌─ Validation Settings ────────────────────────────────┐
│ Greek Error Message:   [Αυτό το πεδίο είναι υποχρεωτικό] │
│ English Error Message: [This field is required        ] │
└─────────────────────────────────────────────────────────┘
```

**Purpose:** Set custom error messages for validation failures

### 📍 **Location 2: Database Configuration**

#### **Direct Database Fields:**
The validation rules are stored in the `form_field_configs` table:

```sql
-- Conditional field columns
conditional_field_key VARCHAR(255),     -- Field that controls visibility
conditional_value TEXT,                 -- Value that triggers visibility  
conditional_operator VARCHAR(20),       -- Comparison operator

-- Validation columns
validation_rules JSONB DEFAULT '{}',    -- Custom validation rules
custom_validation_message_el TEXT,      -- Greek error message
custom_validation_message_en TEXT       -- English error message
```

### 🎯 **Practical Examples:**

#### **Example 1: Start Date Selection**
**Scenario:** Show date picker when "Other" is selected in start availability

**Configuration in FormConfigAdmin:**
1. **Create the trigger field:**
   - Field Key: `startAvailability`
   - Field Type: `select`
   - Options: "Immediately", "Within 1 month", "Other"

2. **Create the conditional field:**
   - Field Key: `customStartDate`
   - Field Type: `date`
   - **Conditional Field Key:** `startAvailability`
   - **Conditional Value:** `other`
   - **Operator:** `equals`
   - **Required:** ✓ Yes
   - **Greek Error Message:** `Παρακαλώ επιλέξτε ημερομηνία έναρξης`
   - **English Error Message:** `Please select a start date`

#### **Example 2: Experience-Based Validation**
**Scenario:** Require certifications for experienced candidates

**Configuration:**
1. **Experience field:**
   - Field Key: `yearsOfExperience`
   - Field Type: `number`

2. **Certifications field:**
   - Field Key: `certifications`
   - Field Type: `multiselect`
   - **Conditional Field Key:** `yearsOfExperience`
   - **Conditional Value:** `2`
   - **Operator:** `greater_than`
   - **Required:** ✓ Yes
   - **Greek Error Message:** `Απαιτούνται πιστοποιήσεις για έμπειρους υποψηφίους`
   - **English Error Message:** `Certifications required for experienced candidates`

#### **Example 3: Age-Specific Requirements**
**Scenario:** Show baby care skills for young children

**Configuration:**
1. **Child age field:**
   - Field Key: `childAge`
   - Field Type: `select`
   - Options: "0-2 years", "3-5 years", "6+ years"

2. **Baby care skills field:**
   - Field Key: `babyCareSkills`
   - Field Type: `checkbox`
   - **Conditional Field Key:** `childAge`
   - **Conditional Value:** `0-2 years`
   - **Operator:** `equals`
   - **Required:** ✓ Yes
   - **Greek Error Message:** `Απαιτούνται δεξιότητες φροντίδας βρεφών`
   - **English Error Message:** `Baby care skills are required`

### 🔧 **Available Validation Operators:**

#### **Comparison Operators:**
- **`equals`** - Field value equals target value
- **`not_equals`** - Field value does not equal target value
- **`contains`** - Field value contains target string
- **`not_contains`** - Field value does not contain target string
- **`greater_than`** - Numeric field value > target value
- **`less_than`** - Numeric field value < target value
- **`in`** - Field value is in comma-separated list
- **`not_in`** - Field value is not in comma-separated list

#### **Usage Examples:**
```
Operator: equals
Value: "other"
→ Shows field when selection equals "other"

Operator: greater_than  
Value: "5"
→ Shows field when number > 5

Operator: in
Value: "option1,option2,option3"
→ Shows field when value is one of the listed options

Operator: contains
Value: "nanny"
→ Shows field when selection contains "nanny"
```

### 🎨 **Validation Rules Object:**

#### **Supported Validation Types:**
The `validation_rules` JSONB field supports:

```json
{
  "required": true,
  "minLength": 5,
  "maxLength": 100,
  "min": 0,
  "max": 50,
  "pattern": "^[A-Za-z]+$",
  "email": true,
  "url": true
}
```

#### **How Validation Works:**
1. **Field Visibility:** Conditional logic determines if field is shown
2. **Value Validation:** Validation rules check field values
3. **Error Display:** Custom messages show when validation fails
4. **Form Progression:** Required fields must be valid to proceed

### 🚀 **Step-by-Step Configuration:**

#### **Step 1: Run the Migration**
```sql
-- Run the corrected migration from docs/MIGRATION_INSTRUCTIONS.md
-- This adds all the conditional and validation columns
```

#### **Step 2: Configure Field Dependencies**
1. Open FormConfigAdmin
2. Create or edit a field configuration
3. Scroll to "Conditional Field Settings"
4. Set the field key, value, and operator

#### **Step 3: Set Validation Messages**
1. In the same dialog, scroll to "Validation Settings"
2. Enter Greek and English error messages
3. Save the configuration

#### **Step 4: Test the Behavior**
1. Go to the Inline Editor or form preview
2. Test the conditional field visibility
3. Test validation by submitting invalid data
4. Verify error messages appear correctly

### 📊 **Benefits of This System:**

#### **For Administrators:**
- **Visual Configuration:** No coding required for complex form logic
- **Real-time Preview:** See conditional fields work immediately
- **Multilingual Support:** Error messages in both languages
- **Complete Control:** Configure every aspect of field behavior

#### **For Users:**
- **Dynamic Forms:** Only see relevant fields
- **Clear Guidance:** Helpful error messages
- **Logical Flow:** Forms adapt to their selections
- **Better UX:** Reduced cognitive load

#### **For Developers:**
- **Database-Driven:** All logic stored in database
- **Type-Safe:** Full TypeScript support
- **Maintainable:** Easy to modify without code changes
- **Extensible:** Simple to add new operators or validation types

### 🔍 **Testing Your Configuration:**

#### **Test Conditional Fields:**
1. Create a field with conditional logic
2. Go to form preview
3. Change the controlling field value
4. Verify the conditional field appears/disappears

#### **Test Validation:**
1. Set a field as required with custom message
2. Try to submit form without filling it
3. Verify custom error message appears
4. Fill the field and verify error disappears

The validation system is now fully functional and ready to use! You can configure complex form logic entirely through the admin interface without writing any code.
