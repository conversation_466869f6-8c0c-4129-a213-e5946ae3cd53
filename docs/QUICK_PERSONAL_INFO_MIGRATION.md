# Quick Personal Info Migration - Immediate Solution

## 🚀 **Immediate Fix for Column Error**

The error you encountered is because the `config_name` column doesn't exist yet. I've created two solutions for you:

### ✅ **Option 1: Simple Migration (Recommended for immediate use)**

**Run this SQL in your Supabase dashboard:**

```sql
-- Simple migration to add Personal Info fields to existing form_field_configs table
-- This works with the current database structure

-- Insert Personal Information fields using existing columns only
INSERT INTO form_field_configs (name, title_el, title_en, field_type, is_active) VALUES

-- Basic Personal Information
('firstName', 'Όνομα', 'First Name', 'text', true),
('lastName', 'Επίθετο', 'Last Name', 'text', true),
('birthDate', 'Ημερομηνία Γέννησης', 'Birth Date', 'text', true),
('contactNumber', 'Τηλέφωνο', 'Contact Number', 'text', true),
('email', 'E-mail', 'E-mail', 'text', true),
('gender', 'Φύλο', 'Gender', 'radio', true),
('nationality', 'Εθνικότητα-Υπηκοότητα', 'Nationality-Citizenship', 'text', true),
('workDocuments', 'Έγγραφα Εργασίας', 'Work Documents', 'radio', true),

-- Address Information
('address', 'Διεύθυνση', 'Address', 'text', true),
('addressNumber', 'Αριθμός', 'Number', 'text', true),
('area', 'Περιοχή', 'Area', 'text', true),
('postalCode', 'Ταχυδρομικός Κώδικας', 'Postal Code', 'text', true),
('city', 'Πόλη', 'City', 'select', true),
('country', 'Χώρα', 'Country', 'text', true),

-- Optional Personal Information
('hasAllergies', 'Αλλεργίες', 'Allergies', 'radio', true),
('allergyDetails', 'Λεπτομέρειες Αλλεργιών', 'Allergy Details', 'textarea', true),
('socialMediaPlatform', 'Πλατφόρμα Social Media', 'Social Media Platform', 'select', true),
('socialMediaHandle', 'Όνομα Χρήστη Social Media', 'Social Media Handle', 'text', true),
('passportActive', 'Ενεργό Διαβατήριο', 'Active Passport', 'radio', true);

-- Insert options for radio button fields
INSERT INTO form_field_options (field_config_id, label_el, label_en, order_index, is_active) VALUES

-- Gender options
((SELECT id FROM form_field_configs WHERE name = 'gender'), 'Άνδρας', 'Male', 0, true),
((SELECT id FROM form_field_configs WHERE name = 'gender'), 'Γυναίκα', 'Female', 1, true),

-- Work Documents options
((SELECT id FROM form_field_configs WHERE name = 'workDocuments'), 'Ναι', 'Yes', 0, true),
((SELECT id FROM form_field_configs WHERE name = 'workDocuments'), 'Όχι', 'No', 1, true),

-- Allergies options
((SELECT id FROM form_field_configs WHERE name = 'hasAllergies'), 'Ναι', 'Yes', 0, true),
((SELECT id FROM form_field_configs WHERE name = 'hasAllergies'), 'Όχι', 'No', 1, true),

-- Passport options
((SELECT id FROM form_field_configs WHERE name = 'passportActive'), 'Ναι', 'Yes', 0, true),
((SELECT id FROM form_field_configs WHERE name = 'passportActive'), 'Όχι', 'No', 1, true),

-- Social Media Platform options
((SELECT id FROM form_field_configs WHERE name = 'socialMediaPlatform'), 'Facebook', 'Facebook', 0, true),
((SELECT id FROM form_field_configs WHERE name = 'socialMediaPlatform'), 'Instagram', 'Instagram', 1, true),
((SELECT id FROM form_field_configs WHERE name = 'socialMediaPlatform'), 'LinkedIn', 'LinkedIn', 2, true),
((SELECT id FROM form_field_configs WHERE name = 'socialMediaPlatform'), 'TikTok', 'TikTok', 3, true),
((SELECT id FROM form_field_configs WHERE name = 'socialMediaPlatform'), 'YouTube', 'YouTube', 4, true),
((SELECT id FROM form_field_configs WHERE name = 'socialMediaPlatform'), 'Twitter/X', 'Twitter/X', 5, true);

-- Add basic city options
INSERT INTO form_field_options (field_config_id, label_el, label_en, order_index, is_active) VALUES
((SELECT id FROM form_field_configs WHERE name = 'city'), 'Αθήνα', 'Athens', 0, true),
((SELECT id FROM form_field_configs WHERE name = 'city'), 'Θεσσαλονίκη', 'Thessaloniki', 1, true),
((SELECT id FROM form_field_configs WHERE name = 'city'), 'Πάτρα', 'Patras', 2, true),
((SELECT id FROM form_field_configs WHERE name = 'city'), 'Ηράκλειο', 'Heraklion', 3, true),
((SELECT id FROM form_field_configs WHERE name = 'city'), 'Λάρισα', 'Larissa', 4, true),
((SELECT id FROM form_field_configs WHERE name = 'city'), 'Βόλος', 'Volos', 5, true),
((SELECT id FROM form_field_configs WHERE name = 'city'), 'Ιωάννινα', 'Ioannina', 6, true),
((SELECT id FROM form_field_configs WHERE name = 'city'), 'Καβάλα', 'Kavala', 7, true),
((SELECT id FROM form_field_configs WHERE name = 'city'), 'Χανιά', 'Chania', 8, true),
((SELECT id FROM form_field_configs WHERE name = 'city'), 'Άλλη', 'Other', 99, true);
```

### ✅ **What This Gives You:**

#### **Immediate Benefits:**
- ✅ **All personal info fields** added to FormConfigAdmin
- ✅ **Works with current database** - no column errors
- ✅ **Basic field types** - text, radio, select, textarea
- ✅ **Multilingual labels** - Greek and English
- ✅ **Field options** - gender, work documents, allergies, cities, social media

#### **Fields Added:**
**Personal Information:**
- First Name, Last Name, Birth Date
- Contact Number, Email, Gender
- Nationality, Work Documents
- Allergies, Allergy Details
- Active Passport

**Address Information:**
- Address, Address Number, Area
- Postal Code, City, Country

**Contact Information:**
- Social Media Platform, Handle

### 🔧 **How to Use:**

#### **Step 1: Run the SQL**
1. Go to your **Supabase dashboard**
2. Open **SQL Editor**
3. **Copy and paste** the SQL above
4. **Run** the query

#### **Step 2: Check FormConfigAdmin**
1. Go to **Form Administration**
2. Click **"Traditional" mode**
3. Go to **"Field Configurations" tab**
4. **Verify** all personal info fields are present

#### **Step 3: Test the Fields**
1. Go to **Inline Editor** or form preview
2. **Test** the dynamic fields
3. **Verify** radio buttons and select options work

### 🚀 **Option 2: Full Migration (Later)**

When you're ready for advanced features, you can run the full migration that includes:
- ✅ **Conditional field logic** (allergy details only when allergies = yes)
- ✅ **Field validation rules** (email, phone, postal code validation)
- ✅ **Custom error messages** in Greek and English
- ✅ **Advanced field configuration** options

**To upgrade later:**
1. **Run the main migration** (adds missing columns)
2. **Run the full personal info migration** (adds advanced features)

### ✅ **Immediate Solution Summary:**

The simple migration gives you:
- ✅ **No database errors** - works with current structure
- ✅ **All personal info fields** - complete field coverage
- ✅ **Basic functionality** - text inputs, radio buttons, selects
- ✅ **Multilingual support** - Greek and English labels
- ✅ **Ready to use** - test immediately in FormConfigAdmin

This gets you up and running with dynamic personal info fields right away, and you can upgrade to advanced features later when you run the full migration system.

### 🎯 **Next Steps:**

1. **Run the simple migration** above
2. **Test FormConfigAdmin** to see all fields
3. **Verify dynamic forms** work correctly
4. **Plan full migration** for advanced features when ready

You now have a working solution that adds all PersonalInfoStep fields to your dynamic form system!
