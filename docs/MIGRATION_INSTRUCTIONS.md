# Database Migration Instructions

## 🎯 **Migration Overview**

This migration will update your existing form configuration tables and add the new form steps system without losing any existing data.

### **What the migration does:**
1. **Updates existing `form_field_configs` table** - Adds missing `label_el` and `label_en` columns
2. **Migrates existing data** - Populates missing fields with sensible defaults
3. **Creates new tables** - Adds `form_steps` and `form_step_categories` tables
4. **Sets up relationships** - Links categories to steps for proper organization

## 🚀 **Option 1: Run Migration via Supabase CLI (Recommended)**

If you have Docker running and Supabase CLI set up:

```bash
# Navigate to your project directory
cd /Users/<USER>/Documents/Oh-Nanny-Blue-UI

# Run the migration
npx supabase db reset

# Or if you want to apply just this migration
npx supabase db push
```

## 🔧 **Option 2: Manual Migration via Supabase Dashboard**

If Docker is not available, you can run the migration manually:

### **Step 1: Access Supabase Dashboard**
1. Go to [supabase.com](https://supabase.com)
2. Sign in to your account
3. Select your project
4. Go to **SQL Editor** in the left sidebar

### **Step 2: Run the Migration SQL**
Copy and paste the following SQL script into the SQL Editor and run it:

```sql
-- Migration to update existing form_field_configs table and add form steps
-- This migration adds missing fields and migrates existing data

-- First, add missing columns to form_field_configs table
ALTER TABLE form_field_configs 
ADD COLUMN IF NOT EXISTS label_el TEXT,
ADD COLUMN IF NOT EXISTS label_en TEXT;

-- Update existing records to have label values (copy from title if not exists)
UPDATE form_field_configs 
SET 
    label_el = COALESCE(label_el, title_el),
    label_en = COALESCE(label_en, title_en)
WHERE label_el IS NULL OR label_en IS NULL;

-- Make label columns NOT NULL after populating them
ALTER TABLE form_field_configs 
ALTER COLUMN label_el SET NOT NULL,
ALTER COLUMN label_en SET NOT NULL;

-- Update existing records to have proper field_key and config_name if missing
UPDATE form_field_configs 
SET 
    config_name = COALESCE(config_name, name),
    field_key = COALESCE(field_key, name)
WHERE config_name IS NULL OR field_key IS NULL OR config_name = '' OR field_key = '';

-- Add default categories for existing records that don't have them
UPDATE form_field_configs 
SET category = CASE 
    WHEN name IN ('musicalInstruments', 'musicTheory', 'lessonFormat') THEN 'skills'
    WHEN name IN ('education', 'specialization') THEN 'education'
    WHEN name IN ('firstAid', 'childrenAge') THEN 'experience'
    WHEN name IN ('position', 'schedule', 'duration') THEN 'preferences'
    WHEN name IN ('candidateType') THEN 'personalInfo'
    WHEN name IN ('startDate') THEN 'availability'
    WHEN name IN ('socialMediaPlatform') THEN 'contact'
    ELSE 'general'
END
WHERE category IS NULL OR category = '';
```

### **Step 3: Create Form Steps Tables**
Run this second part of the migration:

```sql
-- Create form_steps table for configurable form steps
CREATE TABLE IF NOT EXISTS form_steps (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    step_key VARCHAR(255) NOT NULL,
    title_el TEXT NOT NULL,
    title_en TEXT NOT NULL,
    description_el TEXT,
    description_en TEXT,
    form_type VARCHAR(50) NOT NULL CHECK (form_type IN ('candidate', 'nannyRequest', 'both')),
    step_order INTEGER NOT NULL DEFAULT 0,
    icon_name VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create form_step_categories table to link categories to steps
CREATE TABLE IF NOT EXISTS form_step_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    step_id UUID NOT NULL REFERENCES form_steps(id) ON DELETE CASCADE,
    category_name VARCHAR(255) NOT NULL,
    category_order INTEGER NOT NULL DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_form_steps_form_type ON form_steps(form_type);
CREATE INDEX IF NOT EXISTS idx_form_steps_step_order ON form_steps(step_order);
CREATE INDEX IF NOT EXISTS idx_form_steps_is_active ON form_steps(is_active);
CREATE INDEX IF NOT EXISTS idx_form_step_categories_step_id ON form_step_categories(step_id);
CREATE INDEX IF NOT EXISTS idx_form_step_categories_category_name ON form_step_categories(category_name);

-- Create unique constraint to prevent duplicate step keys per form type
CREATE UNIQUE INDEX IF NOT EXISTS idx_form_steps_unique_key_form_type 
ON form_steps(step_key, form_type) WHERE is_active = true;

-- Create triggers to update updated_at timestamp for form_steps
CREATE TRIGGER update_form_steps_updated_at 
    BEFORE UPDATE ON form_steps 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create triggers to update updated_at timestamp for form_step_categories
CREATE TRIGGER update_form_step_categories_updated_at 
    BEFORE UPDATE ON form_step_categories 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
```

### **Step 4: Insert Default Steps and Categories**
Run this final part to set up the default configuration:

```sql
-- Insert default steps for candidate form
INSERT INTO form_steps (step_key, title_el, title_en, description_el, description_en, form_type, step_order, icon_name) VALUES
('personal-info', 'Προσωπικά Στοιχεία & Διεύθυνση', 'Personal Info & Address', 'Βασικές πληροφορίες και στοιχεία επικοινωνίας', 'Basic information and contact details', 'candidate', 0, 'User'),
('career-profile', 'Επαγγελματικό Προφίλ', 'Career Profile', 'Εμπειρία, δεξιότητες και προτιμήσεις εργασίας', 'Experience, skills and work preferences', 'candidate', 1, 'Briefcase'),
('emergency-documents', 'Επαφή Έκτακτης Ανάγκης & Έγγραφα', 'Emergency Contact & Documents', 'Στοιχεία επικοινωνίας έκτακτης ανάγκης και απαραίτητα έγγραφα', 'Emergency contact information and required documents', 'candidate', 2, 'FileText');

-- Insert default steps for nanny request form
INSERT INTO form_steps (step_key, title_el, title_en, description_el, description_en, form_type, step_order, icon_name) VALUES
('family-info', 'Στοιχεία Οικογένειας', 'Family Information', 'Πληροφορίες για την οικογένεια και τα παιδιά', 'Information about the family and children', 'nannyRequest', 0, 'Home'),
('position-details', 'Λεπτομέρειες Θέσης', 'Position Details', 'Λεπτομέρειες για τη θέση εργασίας και τις απαιτήσεις', 'Details about the job position and requirements', 'nannyRequest', 1, 'Info'),
('candidate-preferences', 'Προτιμήσεις Υποψηφίου', 'Candidate Preferences', 'Προτιμήσεις για τον ιδανικό υποψήφιο', 'Preferences for the ideal candidate', 'nannyRequest', 2, 'Heart');

-- Link categories to steps for candidate form
INSERT INTO form_step_categories (step_id, category_name, category_order) VALUES
-- Personal Info step categories
((SELECT id FROM form_steps WHERE step_key = 'personal-info' AND form_type = 'candidate'), 'personalInfo', 0),
((SELECT id FROM form_steps WHERE step_key = 'personal-info' AND form_type = 'candidate'), 'address', 1),
((SELECT id FROM form_steps WHERE step_key = 'personal-info' AND form_type = 'candidate'), 'contact', 2),

-- Career Profile step categories  
((SELECT id FROM form_steps WHERE step_key = 'career-profile' AND form_type = 'candidate'), 'experience', 0),
((SELECT id FROM form_steps WHERE step_key = 'career-profile' AND form_type = 'candidate'), 'skills', 1),
((SELECT id FROM form_steps WHERE step_key = 'career-profile' AND form_type = 'candidate'), 'preferences', 2),
((SELECT id FROM form_steps WHERE step_key = 'career-profile' AND form_type = 'candidate'), 'education', 3),

-- Emergency & Documents step categories
((SELECT id FROM form_steps WHERE step_key = 'emergency-documents' AND form_type = 'candidate'), 'emergency', 0),
((SELECT id FROM form_steps WHERE step_key = 'emergency-documents' AND form_type = 'candidate'), 'documents', 1);

-- Link categories to steps for nanny request form
INSERT INTO form_step_categories (step_id, category_name, category_order) VALUES
-- Family Info step categories
((SELECT id FROM form_steps WHERE step_key = 'family-info' AND form_type = 'nannyRequest'), 'personalInfo', 0),
((SELECT id FROM form_steps WHERE step_key = 'family-info' AND form_type = 'nannyRequest'), 'children', 1),
((SELECT id FROM form_steps WHERE step_key = 'family-info' AND form_type = 'nannyRequest'), 'address', 2),

-- Position Details step categories
((SELECT id FROM form_steps WHERE step_key = 'position-details' AND form_type = 'nannyRequest'), 'jobDetails', 0),
((SELECT id FROM form_steps WHERE step_key = 'position-details' AND form_type = 'nannyRequest'), 'schedule', 1),
((SELECT id FROM form_steps WHERE step_key = 'position-details' AND form_type = 'nannyRequest'), 'availability', 2),

-- Candidate Preferences step categories
((SELECT id FROM form_steps WHERE step_key = 'candidate-preferences' AND form_type = 'nannyRequest'), 'preferences', 0),
((SELECT id FROM form_steps WHERE step_key = 'candidate-preferences' AND form_type = 'nannyRequest'), 'requirements', 1);

-- Update display_order for existing form field configs to have reasonable defaults
UPDATE form_field_configs 
SET display_order = CASE 
    WHEN name = 'candidateType' THEN 1
    WHEN name = 'musicalInstruments' THEN 10
    WHEN name = 'musicTheory' THEN 11
    WHEN name = 'lessonFormat' THEN 12
    WHEN name = 'education' THEN 20
    WHEN name = 'specialization' THEN 21
    WHEN name = 'firstAid' THEN 30
    WHEN name = 'childrenAge' THEN 31
    WHEN name = 'position' THEN 40
    WHEN name = 'schedule' THEN 41
    WHEN name = 'duration' THEN 42
    WHEN name = 'startDate' THEN 50
    WHEN name = 'socialMediaPlatform' THEN 60
    ELSE 999
END
WHERE display_order IS NULL;
```

## ✅ **Verification Steps**

After running the migration, verify it worked correctly:

### **1. Check Tables Exist**
```sql
-- Check if new tables were created
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('form_steps', 'form_step_categories');
```

### **2. Check Data Migration**
```sql
-- Check if existing form configs have the new fields
SELECT name, label_el, label_en, category, field_key 
FROM form_field_configs 
LIMIT 5;
```

### **3. Check Steps Configuration**
```sql
-- Check if default steps were created
SELECT step_key, title_en, form_type, step_order 
FROM form_steps 
ORDER BY form_type, step_order;
```

### **4. Check Step Categories**
```sql
-- Check if categories are linked to steps
SELECT fs.step_key, fs.title_en, fsc.category_name, fsc.category_order
FROM form_steps fs
JOIN form_step_categories fsc ON fs.id = fsc.step_id
ORDER BY fs.form_type, fs.step_order, fsc.category_order;
```

## 🎉 **After Migration**

Once the migration is complete:

1. **Test the FormConfigAdmin** - Go to Form Administration → Traditional → Field Configurations
2. **Test the Inline Editor** - Go to Form Administration → Inline Editor
3. **Test Dynamic Forms** - Try creating a new candidate or nanny request
4. **Verify Data Integrity** - Check that all existing configurations still work

## 🚨 **Rollback (If Needed)**

If something goes wrong, you can rollback by:

```sql
-- Remove new tables
DROP TABLE IF EXISTS form_step_categories;
DROP TABLE IF EXISTS form_steps;

-- Remove new columns (optional, won't break anything)
ALTER TABLE form_field_configs 
DROP COLUMN IF EXISTS label_el,
DROP COLUMN IF EXISTS label_en;
```

## 📞 **Support**

If you encounter any issues during migration:
1. Check the Supabase logs for error details
2. Verify all SQL statements ran successfully
3. Test the application functionality after migration
4. Contact support if data appears corrupted or missing
