# Dynamic Forms Integration Guide

This guide explains how to integrate dynamic form fields and labels into existing forms so that any changes made in the admin panel will be automatically reflected in the forms.

## Overview

The dynamic forms system consists of:
1. **Dynamic Form Fields** - Fields that can be added/removed/modified through the admin panel
2. **Dynamic Labels** - Text content (labels, placeholders, descriptions) that can be managed through the admin panel
3. **Form Themes** - Color schemes and styling that can be customized

## Key Components

### 1. Dynamic Form Field Component
```typescript
import { DynamicFormField } from "@/components/forms/DynamicFormField";

// Usage in form steps
<DynamicFormField
  control={form.control}
  name="fieldName"
  configName="configurationName"
  language={language}
  required={true}
/>
```

### 2. Dynamic Label Components
```typescript
import { 
  DynamicFormLabel, 
  DynamicDescription, 
  DynamicTitle,
  useDynamicPlaceholder 
} from "@/components/forms/DynamicLabel";

// Dynamic label
<DynamicFormLabel 
  labelKey="fieldName.label" 
  language={language} 
  fallback="Default Label Text"
  required={true}
/>

// Dynamic description
<DynamicDescription 
  descriptionKey="fieldName.description" 
  language={language} 
  fallback="Default description"
/>

// Dynamic placeholder
const placeholder = useDynamicPlaceholder({
  placeholderKey: "fieldName.placeholder",
  language,
  fallback: "Default placeholder"
});
```

### 3. Hooks for Dynamic Content
```typescript
import { useFormLabels, useFormConfigs } from "@/hooks/useFormLabels";

const { getLabel } = useFormLabels();
const { getFieldsByCategory } = useFormConfigs();

// Get dynamic label
const label = getLabel("fieldKey.label", language, "fallback");

// Get fields for a category
const personalInfoFields = getFieldsByCategory("personalInfo", "nannyRequest");
```

## Integration Steps

### Step 1: Add Required Imports
```typescript
// Add these imports to your form step component
import { DynamicFormField } from "@/components/forms/DynamicFormField";
import { DynamicFormLabel, DynamicDescription, useDynamicPlaceholder } from "@/components/forms/DynamicLabel";
import { useFormLabels, useFormConfigs } from "@/hooks/useFormLabels";
import { getDynamicLabel, filterFieldsByCategory } from "@/utils/dynamic-form-utils";
```

### Step 2: Set Up Hooks
```typescript
export const YourFormStep: React.FC = () => {
  const { language } = useFormContext(); // Your form context
  const form = useRHFFormContext();
  const { getLabel } = useFormLabels();
  const { getFieldsByCategory } = useFormConfigs();

  // Get dynamic fields for categories
  const personalInfoFields = getFieldsByCategory("personalInfo", "nannyRequest");
  const addressFields = getFieldsByCategory("address", "nannyRequest");
  
  // ... rest of component
};
```

### Step 3: Replace Hardcoded Titles
```typescript
// Before
<h2 className="text-xl font-semibold mb-4">
  {getText("Προσωπικά Στοιχεία / Personal Information", language)}
</h2>

// After
<h2 className="text-xl font-semibold mb-4">
  {getLabel("personalInfo.title", language, getText("Προσωπικά Στοιχεία / Personal Information", language))}
</h2>
```

### Step 4: Replace Hardcoded Form Fields
```typescript
// Before - Hardcoded field
<FormField
  control={form.control}
  name="fatherName"
  render={({ field }) => (
    <FormItem>
      <FormLabel>Father's Name</FormLabel>
      <FormControl>
        <Input placeholder="Enter father's name" {...field} />
      </FormControl>
    </FormItem>
  )}
/>

// After - Dynamic field
<DynamicFormField
  control={form.control}
  name="fatherName"
  configName="personalInfo"
  language={language}
  required={true}
/>
```

### Step 5: Replace Hardcoded Labels and Placeholders
```typescript
// Before
<FormLabel>Father's Name</FormLabel>
<Input placeholder="Enter father's name" {...field} />

// After
<DynamicFormLabel 
  labelKey="fatherName.label" 
  language={language} 
  fallback="Father's Name"
  required={true}
/>
<Input 
  placeholder={useDynamicPlaceholder({
    placeholderKey: "fatherName.placeholder",
    language,
    fallback: "Enter father's name"
  })}
  {...field} 
/>
```

### Step 6: Add Dynamic Sections
```typescript
// Render all dynamic fields for a category
<div className="grid gap-6 md:grid-cols-2">
  {personalInfoFields.map((fieldConfig) => (
    <DynamicFormField
      key={fieldConfig.fieldKey}
      control={form.control}
      name={fieldConfig.fieldKey as any}
      configName={fieldConfig.configName}
      language={language}
      required={fieldConfig.isRequired}
    />
  ))}
</div>
```

## Best Practices

### 1. Always Provide Fallbacks
```typescript
// Good - with fallback
const label = getLabel("fieldKey.label", language, "Default Label");

// Bad - no fallback
const label = getLabel("fieldKey.label", language);
```

### 2. Use Consistent Key Naming
```typescript
// Field keys should follow this pattern:
// fieldName.label - for labels
// fieldName.placeholder - for placeholders  
// fieldName.description - for descriptions
// categoryName.title - for section titles
```

### 3. Group Related Fields
```typescript
// Group fields by category for better organization
const personalInfoFields = getFieldsByCategory("personalInfo", formType);
const addressFields = getFieldsByCategory("address", formType);
const skillsFields = getFieldsByCategory("skills", formType);
```

### 4. Handle Loading States
```typescript
const { getLabel, loading } = useFormLabels();

if (loading) {
  return <div>Loading form...</div>;
}
```

### 5. Validate Dynamic Fields
```typescript
import { validateDynamicField } from "@/utils/dynamic-form-utils";

// Validate field based on its configuration
const validation = validateDynamicField(value, fieldConfig);
if (!validation.isValid) {
  // Handle validation error
}
```

## Form Categories

The system supports these categories:
- `personalInfo` - Personal information fields
- `address` - Address and location fields
- `children` - Children-related information (nanny request)
- `skills` - Skills and qualifications (candidate)
- `education` - Education information (candidate)
- `experience` - Work experience (candidate)
- `availability` - Availability and schedule
- `socialMedia` - Social media profiles
- `jobDetails` - Job-specific details

## Label Contexts

Labels support different contexts:
- `label` - Field labels
- `placeholder` - Input placeholders
- `description` - Help text and descriptions
- `title` - Section titles
- `button` - Button text
- `message` - Error/success messages

## Example: Complete Form Step Integration

```typescript
export const DynamicPersonalInfoStep: React.FC = () => {
  const { language } = useNannyRequestFormContext();
  const form = useRHFFormContext();
  const { getLabel } = useFormLabels();
  const { getFieldsByCategory } = useFormConfigs();

  const personalInfoFields = getFieldsByCategory("personalInfo", "nannyRequest");

  return (
    <Card className="mb-6">
      <CardContent className="pt-6">
        <h2 className="text-xl font-semibold mb-4">
          {getLabel("personalInfo.title", language, "Personal Information")}
        </h2>

        <div className="grid gap-6 md:grid-cols-2">
          {/* Render all dynamic fields */}
          {personalInfoFields.map((fieldConfig) => (
            <DynamicFormField
              key={fieldConfig.fieldKey}
              control={form.control}
              name={fieldConfig.fieldKey as any}
              configName={fieldConfig.configName}
              language={language}
              required={fieldConfig.isRequired}
            />
          ))}

          {/* Custom fields with dynamic labels */}
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <DynamicFormLabel 
                  labelKey="email.label" 
                  language={language} 
                  fallback="Email"
                  required={true}
                />
                <DynamicDescription 
                  descriptionKey="email.description" 
                  language={language} 
                  fallback="We'll send your information here"
                />
                <FormControl>
                  <Input
                    type="email"
                    placeholder={useDynamicPlaceholder({
                      placeholderKey: "email.placeholder",
                      language,
                      fallback: "<EMAIL>"
                    })}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </CardContent>
    </Card>
  );
};
```

## Migration Checklist

When migrating existing forms:

- [ ] Add dynamic imports
- [ ] Set up hooks for labels and configs
- [ ] Replace hardcoded titles with dynamic titles
- [ ] Replace hardcoded labels with dynamic labels
- [ ] Replace hardcoded placeholders with dynamic placeholders
- [ ] Replace hardcoded descriptions with dynamic descriptions
- [ ] Add dynamic field rendering for configurable fields
- [ ] Test with admin panel changes
- [ ] Verify fallbacks work when dynamic content is not available
- [ ] Check both languages (Greek and English)

## Testing Dynamic Integration

1. **Test with Admin Panel**: Make changes in the admin panel and verify they appear in forms
2. **Test Fallbacks**: Disable dynamic loading and ensure fallbacks work
3. **Test Languages**: Switch between Greek and English
4. **Test Field Types**: Verify all field types render correctly
5. **Test Validation**: Ensure dynamic fields validate properly
6. **Test Form Submission**: Verify dynamic fields are included in form data
