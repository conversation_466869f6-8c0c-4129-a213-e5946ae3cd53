import { useState } from "react";
import { useF<PERSON>, FormProvider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { AutoDetectFormStep } from "@/components/forms/steps/AutoDetectFormStep";
import { AutoFormRenderer } from "@/components/forms/AutoFormRenderer";
import { DynamicContentPreview } from "@/components/forms/AutoDynamicForm";
import { useFormStats } from "@/components/forms/AutoFormRenderer";
import { FormThemeProvider } from "@/contexts/FormThemeContext";
import { 
  <PERSON><PERSON><PERSON>, 
  Eye, 
  Code, 
  <PERSON><PERSON>hart3, 
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  CheckCircle,
  AlertCircle
} from "lucide-react";

// Create a flexible schema that can handle any dynamic fields
const dynamicFormSchema = z.object({}).catchall(z.any());

type DynamicFormData = z.infer<typeof dynamicFormSchema>;

export default function DynamicFormDemo() {
  const [selectedFormType, setSelectedFormType] = useState<"candidate" | "nannyRequest">("nannyRequest");
  const [selectedLanguage, setSelectedLanguage] = useState<"el" | "en">("en");
  const [showPreview, setShowPreview] = useState(false);

  const candidateStats = useFormStats("candidate");
  const nannyRequestStats = useFormStats("nannyRequest");

  const form = useForm<DynamicFormData>({
    resolver: zodResolver(dynamicFormSchema),
    defaultValues: {},
  });

  const onSubmit = (data: DynamicFormData) => {
    console.log("Form submitted with data:", data);
    alert("Form submitted! Check console for data.");
  };

  const currentStats = selectedFormType === "candidate" ? candidateStats : nannyRequestStats;

  return (
    <FormThemeProvider>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 p-4">
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Header */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2 text-2xl">
                    <Settings className="h-6 w-6" />
                    Dynamic Forms Demo
                  </CardTitle>
                  <p className="text-muted-foreground mt-1">
                    Automatically detects and renders all available dynamic fields and labels
                  </p>
                </div>
                <div className="flex items-center gap-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="preview-mode"
                      checked={showPreview}
                      onCheckedChange={setShowPreview}
                    />
                    <Label htmlFor="preview-mode">Debug Mode</Label>
                  </div>
                  <Button variant="outline" onClick={() => window.location.reload()}>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Refresh
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{currentStats.totalFields}</div>
                  <div className="text-sm text-blue-600">Dynamic Fields</div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{currentStats.totalCategories}</div>
                  <div className="text-sm text-green-600">Categories</div>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">{currentStats.totalLabels}</div>
                  <div className="text-sm text-purple-600">Labels</div>
                </div>
                <div className="text-center p-4 bg-orange-50 rounded-lg">
                  <div className="text-2xl font-bold text-orange-600">{currentStats.requiredFields}</div>
                  <div className="text-sm text-orange-600">Required</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Controls */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="space-y-2">
                    <Label>Form Type</Label>
                    <div className="flex gap-2">
                      <Button
                        variant={selectedFormType === "candidate" ? "default" : "outline"}
                        size="sm"
                        onClick={() => setSelectedFormType("candidate")}
                      >
                        Candidate Form
                      </Button>
                      <Button
                        variant={selectedFormType === "nannyRequest" ? "default" : "outline"}
                        size="sm"
                        onClick={() => setSelectedFormType("nannyRequest")}
                      >
                        Nanny Request Form
                      </Button>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Language</Label>
                    <div className="flex gap-2">
                      <Button
                        variant={selectedLanguage === "en" ? "default" : "outline"}
                        size="sm"
                        onClick={() => setSelectedLanguage("en")}
                      >
                        English
                      </Button>
                      <Button
                        variant={selectedLanguage === "el" ? "default" : "outline"}
                        size="sm"
                        onClick={() => setSelectedLanguage("el")}
                      >
                        Ελληνικά
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="flex items-center gap-1">
                    <CheckCircle className="h-3 w-3" />
                    Auto-Detection Active
                  </Badge>
                  <Badge variant="secondary" className="flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    No Hardcoded Fields
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Main Content */}
          <Tabs defaultValue="auto-detect" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="auto-detect" className="flex items-center gap-2">
                <Eye className="h-4 w-4" />
                Auto-Detect
              </TabsTrigger>
              <TabsTrigger value="form-renderer" className="flex items-center gap-2">
                <Code className="h-4 w-4" />
                Form Renderer
              </TabsTrigger>
              <TabsTrigger value="statistics" className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4" />
                Statistics
              </TabsTrigger>
              <TabsTrigger value="preview" className="flex items-center gap-2">
                <Palette className="h-4 w-4" />
                Content Preview
              </TabsTrigger>
            </TabsList>

            <TabsContent value="auto-detect" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Auto-Detected Form Step</CardTitle>
                  <p className="text-sm text-muted-foreground">
                    This component automatically detects and renders all available dynamic fields 
                    and labels without any hardcoded field names.
                  </p>
                </CardHeader>
                <CardContent>
                  <FormProvider {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                      <AutoDetectFormStep
                        formType={selectedFormType}
                        language={selectedLanguage}
                      />
                      
                      <div className="flex justify-end pt-6 border-t">
                        <Button type="submit" size="lg">
                          Submit Form
                        </Button>
                      </div>
                    </form>
                  </FormProvider>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="form-renderer" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Auto Form Renderer</CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Complete form renderer that automatically organizes fields by categories.
                  </p>
                </CardHeader>
                <CardContent>
                  <FormProvider {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                      <AutoFormRenderer
                        formType={selectedFormType}
                        language={selectedLanguage}
                        showPreview={showPreview}
                      />
                      
                      <div className="flex justify-end pt-6 border-t">
                        <Button type="submit" size="lg">
                          Submit Form
                        </Button>
                      </div>
                    </form>
                  </FormProvider>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="statistics" className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Candidate Form Stats</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex justify-between">
                        <span>Total Fields:</span>
                        <Badge>{candidateStats.totalFields}</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span>Categories:</span>
                        <Badge>{candidateStats.totalCategories}</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span>Labels:</span>
                        <Badge>{candidateStats.totalLabels}</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span>Required Fields:</span>
                        <Badge variant="destructive">{candidateStats.requiredFields}</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span>Optional Fields:</span>
                        <Badge variant="secondary">{candidateStats.optionalFields}</Badge>
                      </div>
                    </div>
                    
                    <div className="mt-6">
                      <h4 className="font-medium mb-2">Field Types:</h4>
                      <div className="space-y-2">
                        {Object.entries(candidateStats.fieldsByType).map(([type, count]) => (
                          <div key={type} className="flex justify-between text-sm">
                            <span className="capitalize">{type}:</span>
                            <span>{count}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Nanny Request Form Stats</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex justify-between">
                        <span>Total Fields:</span>
                        <Badge>{nannyRequestStats.totalFields}</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span>Categories:</span>
                        <Badge>{nannyRequestStats.totalCategories}</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span>Labels:</span>
                        <Badge>{nannyRequestStats.totalLabels}</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span>Required Fields:</span>
                        <Badge variant="destructive">{nannyRequestStats.requiredFields}</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span>Optional Fields:</span>
                        <Badge variant="secondary">{nannyRequestStats.optionalFields}</Badge>
                      </div>
                    </div>
                    
                    <div className="mt-6">
                      <h4 className="font-medium mb-2">Field Types:</h4>
                      <div className="space-y-2">
                        {Object.entries(nannyRequestStats.fieldsByType).map(([type, count]) => (
                          <div key={type} className="flex justify-between text-sm">
                            <span className="capitalize">{type}:</span>
                            <span>{count}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="preview" className="space-y-6">
              <DynamicContentPreview />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </FormThemeProvider>
  );
}
