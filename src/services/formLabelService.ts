import { supabase } from "@/lib/supabase";
import { FormLabelCategory, FormLabelConfig } from "@/schemas/FormSchema";

export interface FormLabelDB {
  id: string;
  key: string;
  category: string;
  context: string;
  label_el: string;
  label_en: string;
  is_active: boolean;
  form_type?: string;
  created_at?: string;
  updated_at?: string;
}

export interface FormLabelCategoryDB {
  id: string;
  name: string;
  title_el: string;
  title_en: string;
  form_type: string;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

/**
 * Fetches all form label categories with their labels
 * @returns Array of form label categories with their labels
 */
export const fetchFormLabelCategories = async (): Promise<FormLabelCategory[]> => {
  try {
    // Fetch categories
    const { data: categoriesData, error: categoriesError } = await supabase
      .from("form_label_categories")
      .select("*")
      .eq("is_active", true)
      .order("name");

    if (categoriesError) {
      throw new Error(categoriesError.message);
    }

    // Fetch all labels for these categories
    const { data: labelsData, error: labelsError } = await supabase
      .from("form_labels")
      .select("*")
      .eq("is_active", true)
      .order("key");

    if (labelsError) {
      throw new Error(labelsError.message);
    }

    // Transform and combine the data
    const categories: FormLabelCategory[] = categoriesData.map((category: FormLabelCategoryDB) => ({
      id: category.id,
      name: category.name,
      titleEl: category.title_el,
      titleEn: category.title_en,
      formType: category.form_type,
      isActive: category.is_active,
      labels: labelsData
        .filter((label: FormLabelDB) => label.category === category.name)
        .map((label: FormLabelDB) => ({
          id: label.id,
          key: label.key,
          category: label.category,
          context: label.context,
          labelEl: label.label_el,
          labelEn: label.label_en,
          isActive: label.is_active,
          formType: label.form_type,
        }))
        .sort((a, b) => a.key.localeCompare(b.key)),
    }));

    return categories;
  } catch (error) {
    console.error("Error fetching form label categories:", error);
    return [];
  }
};

/**
 * Fetches form labels by category
 * @param category The category name
 * @returns Array of form labels for the category
 */
export const fetchFormLabelsByCategory = async (category: string): Promise<FormLabelConfig[]> => {
  try {
    const { data, error } = await supabase
      .from("form_labels")
      .select("*")
      .eq("category", category)
      .eq("is_active", true)
      .order("key");

    if (error) {
      throw new Error(error.message);
    }

    const labels: FormLabelConfig[] = data.map((label: FormLabelDB) => ({
      id: label.id,
      key: label.key,
      category: label.category,
      context: label.context,
      labelEl: label.label_el,
      labelEn: label.label_en,
      isActive: label.is_active,
      formType: label.form_type,
    }));

    return labels;
  } catch (error) {
    console.error("Error fetching form labels by category:", error);
    return [];
  }
};

/**
 * Fetches a specific form label by key
 * @param key The label key
 * @returns The form label or null if not found
 */
export const fetchFormLabelByKey = async (key: string): Promise<FormLabelConfig | null> => {
  try {
    const { data, error } = await supabase
      .from("form_labels")
      .select("*")
      .eq("key", key)
      .eq("is_active", true)
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return {
      id: data.id,
      key: data.key,
      category: data.category,
      context: data.context,
      labelEl: data.label_el,
      labelEn: data.label_en,
      isActive: data.is_active,
      formType: data.form_type,
    };
  } catch (error) {
    console.error("Error fetching form label by key:", error);
    return null;
  }
};

/**
 * Creates a new form label category
 * @param category The form label category to create
 * @returns The ID of the created category or null if failed
 */
export const createFormLabelCategory = async (category: Omit<FormLabelCategory, 'id' | 'labels'>): Promise<string | null> => {
  try {
    const { data, error } = await supabase
      .from("form_label_categories")
      .insert({
        name: category.name,
        title_el: category.titleEl,
        title_en: category.titleEn,
        form_type: category.formType,
        is_active: category.isActive,
      })
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return data.id;
  } catch (error) {
    console.error("Error creating form label category:", error);
    return null;
  }
};

/**
 * Updates an existing form label category
 * @param category The form label category to update
 * @returns True if successful, false otherwise
 */
export const updateFormLabelCategory = async (category: Omit<FormLabelCategory, 'labels'>): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from("form_label_categories")
      .update({
        name: category.name,
        title_el: category.titleEl,
        title_en: category.titleEn,
        form_type: category.formType,
        is_active: category.isActive,
      })
      .eq("id", category.id);

    if (error) {
      throw new Error(error.message);
    }

    return true;
  } catch (error) {
    console.error("Error updating form label category:", error);
    return false;
  }
};

/**
 * Creates a new form label
 * @param label The form label to create
 * @returns The ID of the created label or null if failed
 */
export const createFormLabel = async (label: Omit<FormLabelConfig, 'id'>): Promise<string | null> => {
  try {
    const { data, error } = await supabase
      .from("form_labels")
      .insert({
        key: label.key,
        category: label.category,
        context: label.context,
        label_el: label.labelEl,
        label_en: label.labelEn,
        is_active: label.isActive,
        form_type: label.formType,
      })
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return data.id;
  } catch (error) {
    console.error("Error creating form label:", error);
    return null;
  }
};

/**
 * Updates an existing form label
 * @param label The form label to update
 * @returns True if successful, false otherwise
 */
export const updateFormLabel = async (label: FormLabelConfig): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from("form_labels")
      .update({
        key: label.key,
        category: label.category,
        context: label.context,
        label_el: label.labelEl,
        label_en: label.labelEn,
        is_active: label.isActive,
        form_type: label.formType,
      })
      .eq("id", label.id);

    if (error) {
      throw new Error(error.message);
    }

    return true;
  } catch (error) {
    console.error("Error updating form label:", error);
    return false;
  }
};

/**
 * Deletes a form label (soft delete by setting is_active to false)
 * @param labelId The ID of the form label to delete
 * @returns True if successful, false otherwise
 */
export const deleteFormLabel = async (labelId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from("form_labels")
      .update({ is_active: false })
      .eq("id", labelId);

    if (error) {
      throw new Error(error.message);
    }

    return true;
  } catch (error) {
    console.error("Error deleting form label:", error);
    return false;
  }
};

/**
 * Bulk update form labels
 * @param labels Array of form labels to update
 * @returns True if successful, false otherwise
 */
export const bulkUpdateFormLabels = async (labels: FormLabelConfig[]): Promise<boolean> => {
  try {
    const updates = labels.map(label => ({
      id: label.id,
      key: label.key,
      category: label.category,
      context: label.context,
      label_el: label.labelEl,
      label_en: label.labelEn,
      is_active: label.isActive,
      form_type: label.formType,
    }));

    const { error } = await supabase
      .from("form_labels")
      .upsert(updates);

    if (error) {
      throw new Error(error.message);
    }

    return true;
  } catch (error) {
    console.error("Error bulk updating form labels:", error);
    return false;
  }
};
