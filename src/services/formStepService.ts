import { supabase } from "@/lib/supabase";

export interface FormStep {
  id: string;
  stepKey: string;
  titleEl: string;
  titleEn: string;
  descriptionEl?: string;
  descriptionEn?: string;
  formType: 'candidate' | 'nannyRequest' | 'both';
  stepOrder: number;
  iconName?: string;
  isActive: boolean;
  categories: FormStepCategory[];
}

export interface FormStepCategory {
  id: string;
  stepId: string;
  categoryName: string;
  categoryOrder: number;
  isActive: boolean;
}

export interface FormStepDB {
  id: string;
  step_key: string;
  title_el: string;
  title_en: string;
  description_el?: string;
  description_en?: string;
  form_type: 'candidate' | 'nannyRequest' | 'both';
  step_order: number;
  icon_name?: string;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface FormStepCategoryDB {
  id: string;
  step_id: string;
  category_name: string;
  category_order: number;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

/**
 * Fetches all form steps for a specific form type
 * @param formType The form type to fetch steps for
 * @returns Array of form steps with their categories
 */
export const fetchFormSteps = async (formType: 'candidate' | 'nannyRequest' | 'both' = 'both'): Promise<FormStep[]> => {
  try {
    // Fetch form steps
    const stepsQuery = supabase
      .from("form_steps")
      .select("*")
      .eq("is_active", true)
      .order("step_order");

    if (formType !== 'both') {
      stepsQuery.in("form_type", [formType, 'both']);
    }

    const { data: stepsData, error: stepsError } = await stepsQuery;

    if (stepsError) {
      throw new Error(stepsError.message);
    }

    // Fetch step categories
    const { data: categoriesData, error: categoriesError } = await supabase
      .from("form_step_categories")
      .select("*")
      .eq("is_active", true)
      .order("category_order");

    if (categoriesError) {
      throw new Error(categoriesError.message);
    }

    // Transform and combine the data
    const steps: FormStep[] = stepsData.map((step: FormStepDB) => ({
      id: step.id,
      stepKey: step.step_key,
      titleEl: step.title_el,
      titleEn: step.title_en,
      descriptionEl: step.description_el,
      descriptionEn: step.description_en,
      formType: step.form_type,
      stepOrder: step.step_order,
      iconName: step.icon_name,
      isActive: step.is_active,
      categories: categoriesData
        .filter((category: FormStepCategoryDB) => category.step_id === step.id)
        .map((category: FormStepCategoryDB) => ({
          id: category.id,
          stepId: category.step_id,
          categoryName: category.category_name,
          categoryOrder: category.category_order,
          isActive: category.is_active,
        }))
        .sort((a, b) => a.categoryOrder - b.categoryOrder),
    }));

    return steps.sort((a, b) => a.stepOrder - b.stepOrder);
  } catch (error) {
    console.error("Error fetching form steps:", error);
    return [];
  }
};

/**
 * Creates a new form step
 * @param step The form step to create
 * @returns The ID of the created step or null if failed
 */
export const createFormStep = async (step: Omit<FormStep, 'id' | 'categories'>): Promise<string | null> => {
  try {
    const { data, error } = await supabase
      .from("form_steps")
      .insert({
        step_key: step.stepKey,
        title_el: step.titleEl,
        title_en: step.titleEn,
        description_el: step.descriptionEl,
        description_en: step.descriptionEn,
        form_type: step.formType,
        step_order: step.stepOrder,
        icon_name: step.iconName,
        is_active: step.isActive,
      })
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return data.id;
  } catch (error) {
    console.error("Error creating form step:", error);
    return null;
  }
};

/**
 * Updates an existing form step
 * @param step The form step to update
 * @returns True if successful, false otherwise
 */
export const updateFormStep = async (step: Omit<FormStep, 'categories'>): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from("form_steps")
      .update({
        step_key: step.stepKey,
        title_el: step.titleEl,
        title_en: step.titleEn,
        description_el: step.descriptionEl,
        description_en: step.descriptionEn,
        form_type: step.formType,
        step_order: step.stepOrder,
        icon_name: step.iconName,
        is_active: step.isActive,
      })
      .eq("id", step.id);

    if (error) {
      throw new Error(error.message);
    }

    return true;
  } catch (error) {
    console.error("Error updating form step:", error);
    return false;
  }
};

/**
 * Deletes a form step (soft delete by setting is_active to false)
 * @param stepId The ID of the form step to delete
 * @returns True if successful, false otherwise
 */
export const deleteFormStep = async (stepId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from("form_steps")
      .update({ is_active: false })
      .eq("id", stepId);

    if (error) {
      throw new Error(error.message);
    }

    return true;
  } catch (error) {
    console.error("Error deleting form step:", error);
    return false;
  }
};

/**
 * Creates a new step category
 * @param category The step category to create
 * @returns The ID of the created category or null if failed
 */
export const createStepCategory = async (category: Omit<FormStepCategory, 'id'>): Promise<string | null> => {
  try {
    const { data, error } = await supabase
      .from("form_step_categories")
      .insert({
        step_id: category.stepId,
        category_name: category.categoryName,
        category_order: category.categoryOrder,
        is_active: category.isActive,
      })
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return data.id;
  } catch (error) {
    console.error("Error creating step category:", error);
    return null;
  }
};

/**
 * Updates an existing step category
 * @param category The step category to update
 * @returns True if successful, false otherwise
 */
export const updateStepCategory = async (category: FormStepCategory): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from("form_step_categories")
      .update({
        step_id: category.stepId,
        category_name: category.categoryName,
        category_order: category.categoryOrder,
        is_active: category.isActive,
      })
      .eq("id", category.id);

    if (error) {
      throw new Error(error.message);
    }

    return true;
  } catch (error) {
    console.error("Error updating step category:", error);
    return false;
  }
};

/**
 * Deletes a step category (soft delete by setting is_active to false)
 * @param categoryId The ID of the step category to delete
 * @returns True if successful, false otherwise
 */
export const deleteStepCategory = async (categoryId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from("form_step_categories")
      .update({ is_active: false })
      .eq("id", categoryId);

    if (error) {
      throw new Error(error.message);
    }

    return true;
  } catch (error) {
    console.error("Error deleting step category:", error);
    return false;
  }
};

/**
 * Reorders form steps
 * @param stepIds Array of step IDs in the new order
 * @returns True if successful, false otherwise
 */
export const reorderFormSteps = async (stepIds: string[]): Promise<boolean> => {
  try {
    const updates = stepIds.map((stepId, index) => ({
      id: stepId,
      step_order: index,
    }));

    const { error } = await supabase
      .from("form_steps")
      .upsert(updates);

    if (error) {
      throw new Error(error.message);
    }

    return true;
  } catch (error) {
    console.error("Error reordering form steps:", error);
    return false;
  }
};
