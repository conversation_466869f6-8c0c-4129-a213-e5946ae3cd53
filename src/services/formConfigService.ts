import { supabase } from "@/lib/supabase";
import { FormFieldConfig, FormFieldOption } from "@/schemas/FormSchema";

export interface FormConfigDB {
  id: string;
  name: string;
  config_name: string;
  field_key: string;
  category: string;
  title_el: string;
  title_en: string;
  label_el: string;
  label_en: string;
  placeholder_el?: string;
  placeholder_en?: string;
  description_el?: string;
  description_en?: string;
  field_type: 'text' | 'email' | 'tel' | 'number' | 'textarea' | 'select' | 'multiselect' | 'checkbox' | 'radio' | 'switch';
  is_active: boolean;
  is_required: boolean;
  form_type: 'candidate' | 'nannyRequest' | 'both';
  display_order?: number;
  created_at?: string;
  updated_at?: string;
}

export interface FormOptionDB {
  id: string;
  field_config_id: string;
  label_el: string;
  label_en: string;
  order_index: number;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

/**
 * Fetches all form field configurations from Supabase
 * @returns Array of form field configurations with their options
 */
export const fetchFormConfigs = async (): Promise<FormFieldConfig[]> => {
  try {
    // Fetch form configs
    const { data: configsData, error: configsError } = await supabase
      .from("form_field_configs")
      .select("*")
      .eq("is_active", true)
      .order("name");

    if (configsError) {
      throw new Error(configsError.message);
    }

    // Fetch all options for these configs
    const { data: optionsData, error: optionsError } = await supabase
      .from("form_field_options")
      .select("*")
      .eq("is_active", true)
      .order("order_index");

    if (optionsError) {
      throw new Error(optionsError.message);
    }

    // Transform and combine the data
    const configs: FormFieldConfig[] = configsData.map((config: FormConfigDB) => ({
      id: config.id,
      name: config.name,
      configName: config.config_name,
      fieldKey: config.field_key,
      category: config.category,
      titleEl: config.title_el,
      titleEn: config.title_en,
      labelEl: config.label_el,
      labelEn: config.label_en,
      placeholderEl: config.placeholder_el,
      placeholderEn: config.placeholder_en,
      descriptionEl: config.description_el,
      descriptionEn: config.description_en,
      fieldType: config.field_type,
      isActive: config.is_active,
      isRequired: config.is_required,
      formType: config.form_type as any,
      displayOrder: config.display_order,
      options: optionsData
        .filter((option: FormOptionDB) => option.field_config_id === config.id)
        .map((option: FormOptionDB) => ({
          id: option.id,
          labelEl: option.label_el,
          labelEn: option.label_en,
          order: option.order_index,
          isActive: option.is_active,
        }))
        .sort((a, b) => a.order - b.order),
    }));

    return configs;
  } catch (error) {
    console.error("Error fetching form configs:", error);
    return [];
  }
};

/**
 * Fetches a specific form field configuration by name
 * @param name The name of the form field configuration
 * @returns The form field configuration or null if not found
 */
export const fetchFormConfigByName = async (name: string): Promise<FormFieldConfig | null> => {
  try {
    const configs = await fetchFormConfigs();
    return configs.find(config => config.name === name) || null;
  } catch (error) {
    console.error("Error fetching form config by name:", error);
    return null;
  }
};

/**
 * Creates a new form field configuration
 * @param config The form field configuration to create
 * @returns True if successful, false otherwise
 */
export const createFormConfig = async (config: Omit<FormFieldConfig, 'id' | 'options'>): Promise<string | null> => {
  try {
    const { data, error } = await supabase
      .from("form_field_configs")
      .insert({
        name: config.name,
        title_el: config.titleEl,
        title_en: config.titleEn,
        placeholder_el: config.placeholderEl,
        placeholder_en: config.placeholderEn,
        description_el: config.descriptionEl,
        description_en: config.descriptionEn,
        field_type: config.fieldType,
        is_active: config.isActive,
      })
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return data.id;
  } catch (error) {
    console.error("Error creating form config:", error);
    return null;
  }
};

/**
 * Updates an existing form field configuration
 * @param config The form field configuration to update
 * @returns True if successful, false otherwise
 */
export const updateFormConfig = async (config: Omit<FormFieldConfig, 'options'>): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from("form_field_configs")
      .update({
        name: config.name,
        title_el: config.titleEl,
        title_en: config.titleEn,
        placeholder_el: config.placeholderEl,
        placeholder_en: config.placeholderEn,
        description_el: config.descriptionEl,
        description_en: config.descriptionEn,
        field_type: config.fieldType,
        is_active: config.isActive,
      })
      .eq("id", config.id);

    if (error) {
      throw new Error(error.message);
    }

    return true;
  } catch (error) {
    console.error("Error updating form config:", error);
    return false;
  }
};

/**
 * Deletes a form field configuration (soft delete by setting is_active to false)
 * @param configId The ID of the form field configuration to delete
 * @returns True if successful, false otherwise
 */
export const deleteFormConfig = async (configId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from("form_field_configs")
      .update({ is_active: false })
      .eq("id", configId);

    if (error) {
      throw new Error(error.message);
    }

    return true;
  } catch (error) {
    console.error("Error deleting form config:", error);
    return false;
  }
};

/**
 * Creates a new form field option
 * @param option The form field option to create
 * @returns The ID of the created option or null if failed
 */
export const createFormOption = async (option: Omit<FormFieldOption, 'id'> & { fieldConfigId: string }): Promise<string | null> => {
  try {
    const { data, error } = await supabase
      .from("form_field_options")
      .insert({
        field_config_id: option.fieldConfigId,
        label_el: option.labelEl,
        label_en: option.labelEn,
        order_index: option.order,
        is_active: option.isActive,
      })
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return data.id;
  } catch (error) {
    console.error("Error creating form option:", error);
    return null;
  }
};

/**
 * Updates an existing form field option
 * @param option The form field option to update
 * @returns True if successful, false otherwise
 */
export const updateFormOption = async (option: FormFieldOption & { fieldConfigId: string }): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from("form_field_options")
      .update({
        field_config_id: option.fieldConfigId,
        label_el: option.labelEl,
        label_en: option.labelEn,
        order_index: option.order,
        is_active: option.isActive,
      })
      .eq("id", option.id);

    if (error) {
      throw new Error(error.message);
    }

    return true;
  } catch (error) {
    console.error("Error updating form option:", error);
    return false;
  }
};

/**
 * Deletes a form field option (soft delete by setting is_active to false)
 * @param optionId The ID of the form field option to delete
 * @returns True if successful, false otherwise
 */
export const deleteFormOption = async (optionId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from("form_field_options")
      .update({ is_active: false })
      .eq("id", optionId);

    if (error) {
      throw new Error(error.message);
    }

    return true;
  } catch (error) {
    console.error("Error deleting form option:", error);
    return false;
  }
};

/**
 * Updates the order of form field options
 * @param updates Array of option IDs with their new order indices
 * @returns True if successful, false otherwise
 */
export const updateFormOptionsOrder = async (updates: { id: string; order: number }[]): Promise<boolean> => {
  try {
    // Use a transaction to update all orders atomically
    const promises = updates.map(update =>
      supabase
        .from("form_field_options")
        .update({ order_index: update.order })
        .eq("id", update.id)
    );

    const results = await Promise.all(promises);
    
    // Check if any update failed
    const hasError = results.some(result => result.error);
    if (hasError) {
      throw new Error("Failed to update some option orders");
    }

    return true;
  } catch (error) {
    console.error("Error updating form options order:", error);
    return false;
  }
};
