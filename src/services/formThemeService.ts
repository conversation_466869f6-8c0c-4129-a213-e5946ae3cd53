import { supabase } from "@/lib/supabase";
import { FormThemeConfig, FormThemeColors } from "@/schemas/FormSchema";

export interface FormThemeDB {
  id: string;
  name: string;
  title_el: string;
  title_en: string;
  is_active: boolean;
  is_default: boolean;
  colors: FormThemeColors;
  created_at?: string;
  updated_at?: string;
}

/**
 * Fetches all form themes from Supabase
 * @returns Array of form theme configurations
 */
export const fetchFormThemes = async (): Promise<FormThemeConfig[]> => {
  try {
    const { data, error } = await supabase
      .from("form_themes")
      .select("*")
      .eq("is_active", true)
      .order("is_default", { ascending: false })
      .order("name");

    if (error) {
      throw new Error(error.message);
    }

    const themes: FormThemeConfig[] = data.map((theme: FormThemeDB) => ({
      id: theme.id,
      name: theme.name,
      titleEl: theme.title_el,
      titleEn: theme.title_en,
      isActive: theme.is_active,
      isDefault: theme.is_default,
      colors: theme.colors,
    }));

    return themes;
  } catch (error) {
    console.error("Error fetching form themes:", error);
    return [];
  }
};

/**
 * Fetches the default form theme
 * @returns The default form theme or null if not found
 */
export const fetchDefaultFormTheme = async (): Promise<FormThemeConfig | null> => {
  try {
    const { data, error } = await supabase
      .from("form_themes")
      .select("*")
      .eq("is_active", true)
      .eq("is_default", true)
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return {
      id: data.id,
      name: data.name,
      titleEl: data.title_el,
      titleEn: data.title_en,
      isActive: data.is_active,
      isDefault: data.is_default,
      colors: data.colors,
    };
  } catch (error) {
    console.error("Error fetching default form theme:", error);
    return null;
  }
};

/**
 * Fetches a specific form theme by ID
 * @param themeId The ID of the theme to fetch
 * @returns The form theme or null if not found
 */
export const fetchFormThemeById = async (themeId: string): Promise<FormThemeConfig | null> => {
  try {
    const { data, error } = await supabase
      .from("form_themes")
      .select("*")
      .eq("id", themeId)
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return {
      id: data.id,
      name: data.name,
      titleEl: data.title_el,
      titleEn: data.title_en,
      isActive: data.is_active,
      isDefault: data.is_default,
      colors: data.colors,
    };
  } catch (error) {
    console.error("Error fetching form theme by ID:", error);
    return null;
  }
};

/**
 * Creates a new form theme
 * @param theme The form theme to create
 * @returns The ID of the created theme or null if failed
 */
export const createFormTheme = async (theme: Omit<FormThemeConfig, 'id'>): Promise<string | null> => {
  try {
    // If this is set as default, unset other defaults first
    if (theme.isDefault) {
      await supabase
        .from("form_themes")
        .update({ is_default: false })
        .eq("is_default", true);
    }

    const { data, error } = await supabase
      .from("form_themes")
      .insert({
        name: theme.name,
        title_el: theme.titleEl,
        title_en: theme.titleEn,
        is_active: theme.isActive,
        is_default: theme.isDefault,
        colors: theme.colors,
      })
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return data.id;
  } catch (error) {
    console.error("Error creating form theme:", error);
    return null;
  }
};

/**
 * Updates an existing form theme
 * @param theme The form theme to update
 * @returns True if successful, false otherwise
 */
export const updateFormTheme = async (theme: FormThemeConfig): Promise<boolean> => {
  try {
    // If this is set as default, unset other defaults first
    if (theme.isDefault) {
      await supabase
        .from("form_themes")
        .update({ is_default: false })
        .eq("is_default", true)
        .neq("id", theme.id);
    }

    const { error } = await supabase
      .from("form_themes")
      .update({
        name: theme.name,
        title_el: theme.titleEl,
        title_en: theme.titleEn,
        is_active: theme.isActive,
        is_default: theme.isDefault,
        colors: theme.colors,
      })
      .eq("id", theme.id);

    if (error) {
      throw new Error(error.message);
    }

    return true;
  } catch (error) {
    console.error("Error updating form theme:", error);
    return false;
  }
};

/**
 * Deletes a form theme (soft delete by setting is_active to false)
 * @param themeId The ID of the theme to delete
 * @returns True if successful, false otherwise
 */
export const deleteFormTheme = async (themeId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from("form_themes")
      .update({ is_active: false })
      .eq("id", themeId);

    if (error) {
      throw new Error(error.message);
    }

    return true;
  } catch (error) {
    console.error("Error deleting form theme:", error);
    return false;
  }
};

/**
 * Sets a theme as the default theme
 * @param themeId The ID of the theme to set as default
 * @returns True if successful, false otherwise
 */
export const setDefaultFormTheme = async (themeId: string): Promise<boolean> => {
  try {
    // First, unset all other defaults
    await supabase
      .from("form_themes")
      .update({ is_default: false })
      .eq("is_default", true);

    // Then set the new default
    const { error } = await supabase
      .from("form_themes")
      .update({ is_default: true })
      .eq("id", themeId);

    if (error) {
      throw new Error(error.message);
    }

    return true;
  } catch (error) {
    console.error("Error setting default form theme:", error);
    return false;
  }
};

/**
 * Predefined theme templates
 */
export const getThemeTemplates = (): Omit<FormThemeConfig, 'id'>[] => [
  {
    name: "default",
    titleEl: "Προεπιλεγμένο Θέμα",
    titleEn: "Default Theme",
    isActive: true,
    isDefault: true,
    colors: {
      progressBarBg: "bg-primary/20",
      progressBarFill: "bg-gradient-to-r from-primary to-secondary",
      formBackground: "bg-gradient-to-b from-accent/20 via-background to-accent/20",
      cardBackground: "bg-white",
      stepBackground: "bg-light/10",
      primaryText: "text-primary",
      secondaryText: "text-secondary",
      accentText: "text-accent-foreground",
      primaryButton: "bg-primary hover:bg-primary-hover",
      primaryButtonHover: "hover:bg-primary-hover",
      secondaryButton: "bg-secondary hover:bg-secondary-hover",
      secondaryButtonHover: "hover:bg-secondary-hover",
      borderColor: "border-accent/20",
      accentColor: "bg-accent",
      successColor: "text-green-600",
      errorColor: "text-destructive",
      stepActiveColor: "bg-primary",
      stepInactiveColor: "bg-muted",
      stepCompletedColor: "bg-green-500",
    },
  },
  {
    name: "ocean",
    titleEl: "Θέμα Ωκεανού",
    titleEn: "Ocean Theme",
    isActive: true,
    isDefault: false,
    colors: {
      progressBarBg: "bg-blue-200",
      progressBarFill: "bg-gradient-to-r from-blue-500 to-cyan-500",
      formBackground: "bg-gradient-to-b from-blue-50 via-white to-cyan-50",
      cardBackground: "bg-white",
      stepBackground: "bg-blue-50",
      primaryText: "text-blue-900",
      secondaryText: "text-blue-700",
      accentText: "text-cyan-700",
      primaryButton: "bg-blue-600 hover:bg-blue-700",
      primaryButtonHover: "hover:bg-blue-700",
      secondaryButton: "bg-cyan-600 hover:bg-cyan-700",
      secondaryButtonHover: "hover:bg-cyan-700",
      borderColor: "border-blue-200",
      accentColor: "bg-cyan-100",
      successColor: "text-green-600",
      errorColor: "text-red-600",
      stepActiveColor: "bg-blue-600",
      stepInactiveColor: "bg-blue-200",
      stepCompletedColor: "bg-green-500",
    },
  },
  {
    name: "sunset",
    titleEl: "Θέμα Ηλιοβασιλέματος",
    titleEn: "Sunset Theme",
    isActive: true,
    isDefault: false,
    colors: {
      progressBarBg: "bg-orange-200",
      progressBarFill: "bg-gradient-to-r from-orange-500 to-pink-500",
      formBackground: "bg-gradient-to-b from-orange-50 via-white to-pink-50",
      cardBackground: "bg-white",
      stepBackground: "bg-orange-50",
      primaryText: "text-orange-900",
      secondaryText: "text-orange-700",
      accentText: "text-pink-700",
      primaryButton: "bg-orange-600 hover:bg-orange-700",
      primaryButtonHover: "hover:bg-orange-700",
      secondaryButton: "bg-pink-600 hover:bg-pink-700",
      secondaryButtonHover: "hover:bg-pink-700",
      borderColor: "border-orange-200",
      accentColor: "bg-pink-100",
      successColor: "text-green-600",
      errorColor: "text-red-600",
      stepActiveColor: "bg-orange-600",
      stepInactiveColor: "bg-orange-200",
      stepCompletedColor: "bg-green-500",
    },
  },
  {
    name: "forest",
    titleEl: "Θέμα Δάσους",
    titleEn: "Forest Theme",
    isActive: true,
    isDefault: false,
    colors: {
      progressBarBg: "bg-green-200",
      progressBarFill: "bg-gradient-to-r from-green-500 to-emerald-500",
      formBackground: "bg-gradient-to-b from-green-50 via-white to-emerald-50",
      cardBackground: "bg-white",
      stepBackground: "bg-green-50",
      primaryText: "text-green-900",
      secondaryText: "text-green-700",
      accentText: "text-emerald-700",
      primaryButton: "bg-green-600 hover:bg-green-700",
      primaryButtonHover: "hover:bg-green-700",
      secondaryButton: "bg-emerald-600 hover:bg-emerald-700",
      secondaryButtonHover: "hover:bg-emerald-700",
      borderColor: "border-green-200",
      accentColor: "bg-emerald-100",
      successColor: "text-green-600",
      errorColor: "text-red-600",
      stepActiveColor: "bg-green-600",
      stepInactiveColor: "bg-green-200",
      stepCompletedColor: "bg-green-500",
    },
  },
];
