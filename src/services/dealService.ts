import { ClientFormData } from "@/components/crm/clients/ClientsTable";
import { CandidateFormData } from "@/components/nannies/models/Candidate";
import {
  PaginatedResponse,
  PaginationParams,
  paginateQuery,
} from "@/lib/paginationUtils";
import { supabase } from "@/lib/supabase";

export interface DealSchema {
  id: string;
  name: string;
  deal_date: string;
  revenue: number;
  candidate_salary: number;
  client_id: number;
  candidate_id: number;
  contract_url: string | null;
  created_at?: string;
  updated_at?: string;
  status: number;
  history: History;
}
// Define deal interface
export interface Deal {
  id: string;
  name: string;
  deal_date: string;
  revenue: number;
  candidate_salary: number;
  client_id: number;
  candidate_id: number;
  contract_url: string | null;
  created_at?: string;
  updated_at?: string;
  status: DealStatus;
  history: History;
  client_father_name?: string;
  client_mother_name?: string;
  candidate_name?: string;
  candidate_surname?: string;
  client_form_data?: ClientFormData;
  candidate_form_data?: CandidateFormData;
}

export const toDealSchema = (deal: Deal): DealSchema => {
  return {
    id: deal.id,
    name: deal.name,
    deal_date: deal.deal_date,
    revenue: deal.revenue,
    candidate_salary: deal.candidate_salary,
    client_id: deal.client_id,
    candidate_id: deal.candidate_id,
    contract_url: deal.contract_url,
    created_at: deal.created_at,
    updated_at: deal.updated_at,
    history: deal.history,
    status: statusMap[deal.status],
  };
};

export enum DealStatus {
  InProgress = "In Progress",
  Rejected = "Rejected",
  Inactive = "Inactive",
  Active = "Active",
}

export const statusMap = {
  [DealStatus.InProgress]: 1,
  [DealStatus.Rejected]: 2,
  [DealStatus.Inactive]: 3,
  [DealStatus.Active]: 4,
};

export interface History {
  cv_and_price_list: {
    comment: string;
    datetime: string;
    completed: boolean;
    location?: string;
  };
  contact_client_nanny: {
    comment: string;
    datetime: string;
    completed: boolean;
    location?: string;
  };
  appointment: {
    comment: string;
    datetime: string;
    completed: boolean;
    location: string;
  };
  trial: {
    comment: string;
    datetime: string;
    completed: boolean;
    location?: string;
  };
  feedback_from_family: {
    comment: string;
    datetime: string;
    completed: boolean;
    location?: string;
  };
  feedback_from_nanny: {
    comment: string;
    datetime: string;
    completed: boolean;
    location?: string;
  };
}

/**
 * Process a deal from the database
 * @param rawDeal Raw deal data from the database
 * @returns Processed deal
 */
const processDeal = (rawDeal: any): Deal => {
  // Create the base deal object with proper type conversion
  const deal: Deal = {
    ...rawDeal,
    id: rawDeal.id.toString(),
    client_id: rawDeal.client_id,
    candidate_id: rawDeal.candidate_id,
    candidate_salary: rawDeal.candidate_salary,
    history: rawDeal.history,
    status: Object.keys(statusMap).find(
      (key) => statusMap[key as DealStatus] === rawDeal.status
    ) as DealStatus,
    // Extract client information from joined data
    client_father_name: rawDeal.clients?.form_data?.father_name || "",
    client_mother_name: rawDeal.clients?.form_data?.mother_name || "",

    // Extract candidate information from joined data
    candidate_name: rawDeal.candidates?.form_data?.name || "",
    candidate_surname: rawDeal.candidates?.form_data?.surname || "",
    client_form_data: rawDeal.clients?.form_data,
    candidate_form_data: rawDeal.candidates?.form_data,
  };

  return deal;
};

/**
 * Apply filters to a Supabase query based on pagination filters
 * @param query The Supabase query to apply filters to
 * @param pagination Pagination parameters with filters
 * @returns The modified query with filters applied
 */
const applyFilters = (query: any, pagination?: PaginationParams): any => {
  if (!pagination?.filters) return query;

  console.debug("Applying filters in dealService:", pagination.filters);

  // Apply each filter
  Object.entries(pagination.filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== "") {
      console.debug(`Applying filter: ${key} = ${value}`);

      // Handle different filter types
      switch (key) {
        case "name":
          query = query.ilike("name", `%${value}%`);
          break;
        case "status":
          query = query.eq("status", statusMap[value]);
          break;
        case "deal_date":
          // Handle date range if value is an array
          if (Array.isArray(value) && value.length === 2) {
            if (value[0]) {
              query = query.gte("deal_date", value[0]);
            }
            if (value[1]) {
              query = query.lte("deal_date", value[1]);
            }
          } else if (typeof value === "string" && value) {
            query = query.eq("deal_date", value);
          }
          break;
        case "revenue":
          query = query.gte("revenue", value);
          break;
        case "candidate_salary":
          query = query.gte("candidate_salary", value);
          break;
        case "client_id":
          query = query.eq("client_id", value);
          break;
        case "candidate_id":
          query = query.eq("candidate_id", value);
          break;
        case "client_name":
          // Filter by client name in the form_data JSON
          query = query.or(
            `clients.form_data->father_name.ilike.%${value}%,clients.form_data->mother_name.ilike.%${value}%`
          );
          break;
        case "candidate_name":
          // Filter by candidate name in the form_data JSON
          query = query.or(
            `candidates.form_data->name.ilike.%${value}%,candidates.form_data->surname.ilike.%${value}%`
          );
          break;
        case "contract_url":
          if (value === "true") {
            query = query.not("contract_url", "is", null);
          } else if (value === "false") {
            query = query.is("contract_url", null);
          }
          break;
        default:
          // For any other fields, use exact match
          query = query.eq(key, value);
          break;
      }
    }
  });

  return query;
};

/**
 * Fetches all deals
 * @param forceRefresh Not used anymore, kept for API compatibility
 * @param pagination Optional pagination parameters
 * @returns Array of deals or paginated response if pagination is provided
 */
export const fetchDeals = async (
  forceRefresh = false,
  pagination?: PaginationParams
): Promise<Deal[] | PaginatedResponse<Deal>> => {
  try {
    // Create the base query with joins to clients and candidates
    let query = supabase
      .from("deals")
      .select(
        `
        *,
        clients:client_id (
          id,
          form_data
        ),
        candidates:candidate_id (
          id,
          form_data
        )
      `,
        { count: "exact" }
      )
      .order("deal_date", { ascending: false });

    // Apply filters
    query = applyFilters(query, pagination);
    const { data, count: totalCount } = await query;

    // If pagination is requested, use the paginate utility
    if (pagination) {
      console.debug("Using pagination with params:", pagination);
      const paginatedResponse = await paginateQuery<any>(
        data,
        pagination,
        totalCount,
        Math.ceil(totalCount / pagination.itemsPerPage)
      );

      // Process the data
      const processedData = paginatedResponse.data.map(processDeal);

      // Create the paginated response
      const result = {
        ...paginatedResponse,
        data: processedData,
        totalCount,
        totalPages: Math.ceil(totalCount / pagination.itemsPerPage),
      };

      return result;
    } else {
      // Otherwise, fetch all data
      const { data, error } = await query;

      if (error) {
        throw new Error(error.message);
      }

      // Process the data
      const deals = data?.map(processDeal) || [];
      return deals;
    }
  } catch (error) {
    console.error("Error fetching deals:", error);

    // Return empty array or empty paginated response
    return pagination
      ? {
          data: [],
          totalCount: 0,
          page: pagination.page,
          itemsPerPage: pagination.itemsPerPage,
          totalPages: 0,
        }
      : [];
  }
};

/**
 * Fetches a single deal by ID
 * @param id The deal ID
 * @param forceRefresh Not used anymore, kept for API compatibility
 * @returns The deal or null if not found
 */
export const fetchDealById = async (
  id: string,
  forceRefresh = false
): Promise<Deal | null> => {
  try {
    // Try to fetch by ID with joins
    const { data, error } = await supabase
      .from("deals")
      .select(
        `
        *,
        clients:client_id (
          id,
          form_data
        ),
        candidates:candidate_id (
          id,
          form_data
        )
      `
      )
      .eq("id", id)
      .single();

    if (error) throw error;

    if (!data) {
      return null;
    }

    // Process the data
    const deal = processDeal(data);
    return deal;
  } catch (error) {
    console.error(`Error fetching deal with ID ${id}:`, error);
    return null;
  }
};

/**
 * Creates a new deal
 * @param deal The deal to create
 * @returns The created deal or null if there was an error
 */
export const createDeal = async (deal: Partial<Deal>): Promise<Deal | null> => {
  try {
    const { data, error } = await supabase
      .from("deals")
      .insert([deal])
      .select();

    if (error) {
      throw new Error(error.message);
    }

    if (!data || data.length === 0) {
      throw new Error("No data returned after creating deal");
    }

    // Process the data
    const newDeal = processDeal(data[0]);
    return newDeal;
  } catch (error) {
    console.error("Error creating deal:", error);
    return null;
  }
};

/**
 * Updates a deal
 * @param id The deal ID
 * @param deal The deal data to update
 * @returns The updated deal or null if there was an error
 */
export const updateDeal = async (
  id: string,
  deal: DealSchema
): Promise<Deal | null> => {
  try {
    const { data, error } = await supabase
      .from("deals")
      .update(deal)
      .eq("id", id)
      .select(
        `
        *,
        clients:client_id (
          id,
          form_data
        ),
        candidates:candidate_id (
          id,
          form_data
        )
      `
      );

    if (error) {
      throw new Error(error.message);
    }

    if (!data || data.length === 0) {
      throw new Error("No data returned after updating deal");
    }

    // Process the data
    const updatedDeal = processDeal(data[0]);
    return updatedDeal;
  } catch (error) {
    console.error(`Error updating deal with ID ${id}:`, error);
    return null;
  }
};

/**
 * Deletes a deal
 * @param id The deal ID
 * @returns True if successful, false otherwise
 */
export const deleteDeal = async (id: string): Promise<boolean> => {
  try {
    const { error } = await supabase.from("deals").delete().eq("id", id);

    if (error) {
      throw new Error(error.message);
    }

    return true;
  } catch (error) {
    console.error(`Error deleting deal with ID ${id}:`, error);
    return false;
  }
};
