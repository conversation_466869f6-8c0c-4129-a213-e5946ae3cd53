import {
  <PERSON><PERSON><PERSON>,
  Too<PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { History } from "@/services/dealService";
import { Check } from "lucide-react";

interface CompactTimelineProps {
  history: History;
  className?: string;
}

interface TimelineStep {
  key: keyof History;
  title: string;
  shortTitle: string;
  description: string;
}

const timelineSteps: TimelineStep[] = [
  {
    key: "cv_and_price_list",
    title: "Βιογραφικό & Τιμοκατάλογος",
    shortTitle: "ΒΤ",
    description:
      "Αποστολή του βιογραφικού της nanny και του τιμοκαταλόγου των υπηρεσιών στην οικογένεια.",
  },
  {
    key: "contact_client_nanny",
    title: "Επικοινωνία Πελάτη-Nanny",
    shortTitle: "Ε<PERSON>",
    description:
      "Άμεση επικοινωνία μεταξύ του πελάτη και της nanny για συζήτηση λεπτομερειών και διευκρινίσεων.",
  },
  {
    key: "appointment",
    title: "Ραντεβού",
    shortTitle: "ΡΝ",
    description:
      "Προγραμματισμένη συνάντηση μεταξύ της nanny και της οικογένειας για αρχική γνωριμία.",
  },
  {
    key: "trial",
    title: "Δοκιμαστική Περίοδος",
    shortTitle: "ΔΠ",
    description:
      "Η δοκιμαστική περίοδος όπου η nanny εργάζεται με την οικογένεια για να διαπιστωθεί η συμβατότητα.",
  },
  {
    key: "feedback_from_family",
    title: "Feedback από Οικογένεια",
    shortTitle: "ΑΟ",
    description:
      "Λήψη σχολίων και εντυπώσεων από την οικογένεια σχετικά με τη nanny και τις υπηρεσίες της.",
  },
  {
    key: "feedback_from_nanny",
    title: "Feedback από nanny",
    shortTitle: "ΑΝ",
    description:
      "Λήψη σχολίων και εντυπώσεων από τη nanny σχετικά με την οικογένεια και το περιβάλλον εργασίας.",
  },
];

const CompactTimeline = ({ history, className }: CompactTimelineProps) => {
  // Find the current active step (first incomplete step)
  const getCurrentStep = (): keyof History | null => {
    for (const step of timelineSteps) {
      if (!history[step.key].completed) {
        return step.key;
      }
    }
    return null; // All steps are completed
  };

  const currentStep = getCurrentStep();

  // Calculate completion percentage
  const completedSteps = timelineSteps.filter(
    (step) => history[step.key].completed
  ).length;
  const totalSteps = timelineSteps.length;
  const completionPercentage = Math.round((completedSteps / totalSteps) * 100);

  return (
    <div className={cn("w-full", className)}>
      {/* Progress bar */}
      <div className="w-full bg-muted h-1.5 rounded-full mb-2">
        <div
          className="bg-primary h-1.5 rounded-full"
          style={{ width: `${completionPercentage}%` }}
        ></div>
      </div>

      {/* Completion percentage */}
      <div className="text-xs text-muted-foreground text-center mb-1">
        {completionPercentage}% ολοκληρωμένο
      </div>

      {/* Steps */}
      <div className="flex justify-between items-center">
        {timelineSteps.map((step, index) => {
          const isCompleted = history[step.key].completed;
          const isActive = currentStep === step.key;

          return (
            <TooltipProvider key={step.key}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex flex-col items-center cursor-help">
                    <div
                      className={cn(
                        "flex items-center justify-center w-5 h-5 rounded-full border text-xs",
                        isCompleted
                          ? "bg-primary border-primary text-white"
                          : isActive
                            ? "bg-white border-primary text-primary"
                            : "bg-white border-muted-foreground/50 text-muted-foreground"
                      )}
                    >
                      {isCompleted ? (
                        <Check className="h-3 w-3" />
                      ) : (
                        <span className="text-[9px]">{index + 1}</span>
                      )}
                    </div>
                    <span className="text-[9px] mt-0.5 text-muted-foreground">
                      {step.shortTitle}
                    </span>
                  </div>
                </TooltipTrigger>
                <TooltipContent side="top" className="max-w-[250px] p-3">
                  <div className="space-y-2">
                    <div className="font-medium">{step.title}</div>
                    <p className="text-sm text-muted-foreground">
                      {step.description}
                    </p>
                    <div className="text-xs font-medium mt-1">
                      {isCompleted ? (
                        <span className="text-green-500">✓ Ολοκληρώθηκε</span>
                      ) : isActive ? (
                        <span className="text-blue-500">→ Τρέχον βήμα</span>
                      ) : (
                        <span className="text-gray-500">○ Εκκρεμεί</span>
                      )}
                    </div>
                    {history[step.key].datetime && (
                      <div className="text-xs text-muted-foreground">
                        Ημερομηνία:{" "}
                        {new Date(
                          history[step.key].datetime
                        ).toLocaleDateString("el-GR")}
                      </div>
                    )}
                    {history[step.key].comment && (
                      <div className="text-xs">
                        <span className="font-medium">Σχόλια:</span>{" "}
                        {history[step.key].comment}
                      </div>
                    )}
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          );
        })}
      </div>
    </div>
  );
};

export default CompactTimeline;
