import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { getCityLabel } from "@/lib/tableUtils";
import { getFilePreviewUrl } from "@/lib/utils";
import { approveCandidate, rejectCandidate } from "@/services/candidateService";
import {
  ArrowLeft,
  BookOpen,
  CheckCircle,
  Pencil,
  Save,
  User,
  X,
  XCircle,
} from "lucide-react";
import { useEffect, useState } from "react";
import { SearchableCitySelect } from "../ui/SearchableCitySelect";
import CreateDealButton from "./CreateDealButton";

export interface ProfileData {
  id: string;
  name?: string;
  surname?: string;
  form_data: {
    email?: string;
    contact_number?: string;
  };
  city?: string;
  address?: string;
  position_interests?: string[];
  type?: "nanny" | "candidate" | "tutor";
  profile_photo?: string;
  is_nanny_approved?: boolean;
  is_tutor_approved?: boolean;
  is_rejected?: boolean;
}

export interface ProfilePermissions {
  canEdit?: boolean;
  canCreateDeal?: boolean;
  canApproveNanny?: boolean;
  canApproveTutor?: boolean;
  canRejectCandidate?: boolean;
}

export interface ProfileHeaderProps {
  profile: ProfileData;
  editMode: boolean;
  saving: boolean;
  hideBackButton?: boolean;
  onEdit: () => void;
  onCancel: () => void;
  onSave: () => void;
  onBack: () => void;
  onRefresh?: () => void; // Optional callback to refresh data after approval/rejection
  isNannyUser?: boolean;
  permissions?: ProfilePermissions; // Optional permissions object
  editedProfile?: ProfileData; // For edit mode
  onInputChange?: (field: string, value: string | boolean | string[] | any) => void; // For handling input changes
}

const ProfileHeader = ({
  profile,
  editMode,
  saving,
  hideBackButton = false,
  onEdit,
  onCancel,
  onSave,
  onBack,
  onRefresh,
  isNannyUser = false,
  permissions = {
    canEdit: true,
    canCreateDeal: true,
    canApproveNanny: true,
    canApproveTutor: true,
    canRejectCandidate: true,
  },
  editedProfile,
  onInputChange,
}: ProfileHeaderProps) => {
  const { toast } = useToast();
  const [isProcessing, setIsProcessing] = useState(false);
  const [profilePhotoUrl, setProfilePhotoUrl] = useState<string | null>(null);

  // Load profile photo URL
  useEffect(() => {
    const loadProfilePhoto = async () => {
      if (profile.profile_photo) {
        try {
          const url = await getFilePreviewUrl(
            "candidate-documents",
            profile.profile_photo
          );
          setProfilePhotoUrl(url);
        } catch (error) {
          console.error("Error loading profile photo:", error);
          setProfilePhotoUrl(null);
        }
      } else {
        setProfilePhotoUrl(null);
      }
    };

    loadProfilePhoto();
  }, [profile.profile_photo]);

  // Determine the prefix based on profile type
  const getIdPrefix = () => {
    switch (profile.type) {
      case "nanny":
        return "NAN";
      case "tutor":
        return "TUT";
      case "candidate":
      default:
        return "CAN";
    }
  };

  // Handle approve as nanny
  const handleApproveNanny = async () => {
    if (!profile.id) return;

    setIsProcessing(true);
    try {
      const success = await approveCandidate(profile.id.toString(), "Nanny");

      if (success) {
        toast({
          title: "Επιτυχία",
          description: `Εγκρίθηκε ως Nanny: ${profile.name} ${profile.surname}`,
        });

        // Refresh data if callback provided
        if (onRefresh) {
          onRefresh();
        }
      } else {
        throw new Error("Failed to approve candidate as nanny");
      }
    } catch (error) {
      console.error("Error approving candidate as nanny:", error);
      toast({
        title: "Σφάλμα",
        description: "Απέτυχε η εγκρισή του υποψήφου ως Nanny.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle approve as tutor
  const handleApproveTutor = async () => {
    if (!profile.id) return;

    setIsProcessing(true);
    try {
      const success = await approveCandidate(profile.id.toString(), "Tutor");

      if (success) {
        toast({
          title: "Επιτυχία",
          description: `Εγκρίθηκε ως Tutor: ${profile.name} ${profile.surname}`,
        });

        // Refresh data if callback provided
        if (onRefresh) {
          onRefresh();
        }
      } else {
        throw new Error("Failed to approve candidate as tutor");
      }
    } catch (error) {
      console.error("Error approving candidate as tutor:", error);
      toast({
        title: "Σφάλμα",
        description: "Απέτυχε η εγκρισή του υποψήφου ως Tutor.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle reject
  const handleReject = async () => {
    if (!profile.id) return;

    setIsProcessing(true);
    try {
      const success = await rejectCandidate(profile.id.toString());

      if (success) {
        toast({
          title: "Επιτυχία",
          description: `Απορρίφθηκε: ${profile.name} ${profile.surname}`,
        });

        // Refresh data if callback provided
        if (onRefresh) {
          onRefresh();
        }
      } else {
        throw new Error("Failed to reject candidate");
      }
    } catch (error) {
      console.error("Error rejecting candidate:", error);
      toast({
        title: "Σφάλμα",
        description: "Απέτυχε η απόρριψη του υποψήφου.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle upgrade to tutor
  const handleUpgradeToTutor = async () => {
    if (!profile.id) return;

    setIsProcessing(true);
    try {
      const success = await approveCandidate(profile.id.toString(), "Tutor");

      if (success) {
        toast({
          title: "Επιτυχία",
          description: `Αναβαθμίστηκε σε Tutor: ${profile.name} ${profile.surname}`,
        });

        // Refresh data if callback provided
        if (onRefresh) {
          onRefresh();
        }
      } else {
        throw new Error("Failed to upgrade to tutor");
      }
    } catch (error) {
      console.error("Error upgrading to tutor:", error);
      toast({
        title: "Σφάλμα",
        description: "Απέτυχε η αναβάθμιση σε Tutor.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle upgrade to nanny
  const handleUpgradeToNanny = async () => {
    if (!profile.id) return;

    setIsProcessing(true);
    try {
      const success = await approveCandidate(profile.id.toString(), "Nanny");

      if (success) {
        toast({
          title: "Επιτυχία",
          description: `Αναβαθμίστηκε σε Nanny: ${profile.name} ${profile.surname}`,
        });

        // Refresh data if callback provided
        if (onRefresh) {
          onRefresh();
        }
      } else {
        throw new Error("Failed to upgrade to nanny");
      }
    } catch (error) {
      console.error("Error upgrading to nanny:", error);
      toast({
        title: "Σφάλμα",
        description: "Απέτυχε η αναβάθμιση σε Nanny.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <>
      {/* Back Button - only shown if not hidden */}
      {!hideBackButton && (
        <Button
          variant="ghost"
          className="flex items-center gap-1 mb-4 hover:bg-primary/10 transition-colors"
          onClick={onBack}
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Επιστροφή</span>
        </Button>
      )}

      {/* Header with ID and Date */}
      <div className="bg-white rounded-xl shadow-sm border border-primary/10 p-6 mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex items-center gap-6">
            <Avatar className="h-20 w-20 border-4 border-primary/10 shadow-sm">
              <AvatarImage src={profilePhotoUrl} />
              <AvatarFallback className="bg-primary/5 text-primary text-xl font-bold">
                {profile.name?.charAt(0) || "P"}
              </AvatarFallback>
            </Avatar>
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <h2 className="text-3xl font-bold text-primary">
                  {profile.name} {profile.surname}
                </h2>
                <Badge
                  variant="outline"
                  className="text-xs px-2 py-1 uppercase"
                >
                  {getIdPrefix()}-{profile.id}
                </Badge>
                {profile.is_rejected && !isNannyUser && (
                  <Badge variant="destructive">Απορρίφθηκε</Badge>
                )}
              </div>

              <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
                <span className="text-sm text-muted-foreground flex items-center gap-1">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="lucide lucide-mail"
                  >
                    <rect width="20" height="16" x="2" y="4" rx="2" />
                    <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
                  </svg>
                  {profile.form_data.email}
                </span>
                <span className="text-sm text-muted-foreground flex items-center gap-1">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="lucide lucide-phone"
                  >
                    <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z" />
                  </svg>
                  {profile.form_data.contact_number || "Not provided"}
                </span>
                {editMode && onInputChange ? (
                  <div className="flex items-center gap-1">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="lucide lucide-map-pin text-muted-foreground"
                    >
                      <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z" />
                      <circle cx="12" cy="10" r="3" />
                    </svg>
                    <SearchableCitySelect
                      value={profile.city || ""}
                      onValueChange={(value) => onInputChange("city", value)}
                      placeholder="Επιλέξτε πόλη"
                      className="min-w-[200px]"
                      allowReset={true}
                    />
                  </div>
                ) : (
                  <span className="text-sm text-muted-foreground flex items-center gap-1">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="lucide lucide-map-pin"
                    >
                      <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z" />
                      <circle cx="12" cy="10" r="3" />
                    </svg>
                    {getCityLabel(profile.city) ||
                      profile.address ||
                      "Not provided"}
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-wrap items-center gap-2 mt-4 sm:mt-0">
            {/* Candidate Approval/Rejection Buttons */}
            {profile.type === "candidate" && !editMode && !isNannyUser && (
              <>
                {permissions.canApproveNanny && (
                  <Button
                    onClick={handleApproveNanny}
                    className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
                    disabled={isProcessing}
                  >
                    {isProcessing ? (
                      <div className="animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent" />
                    ) : (
                      <CheckCircle size={16} />
                    )}
                    <span>Εγκριση ως Nanny</span>
                  </Button>
                )}
                {permissions.canApproveTutor && (
                  <Button
                    onClick={handleApproveTutor}
                    className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
                    disabled={isProcessing}
                  >
                    {isProcessing ? (
                      <div className="animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent" />
                    ) : (
                      <CheckCircle size={16} />
                    )}
                    <span>Εγκριση ως Tutor</span>
                  </Button>
                )}
              </>
            )}

            {!editMode &&
              !isNannyUser &&
              !profile.is_rejected &&
              permissions.canRejectCandidate && (
                <Button
                  onClick={handleReject}
                  className="flex items-center gap-2 bg-red-600 hover:bg-red-700"
                  disabled={isProcessing}
                >
                  {isProcessing ? (
                    <div className="animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent" />
                  ) : (
                    <XCircle size={16} />
                  )}
                  <span>Απόρριψη</span>
                </Button>
              )}

            {/* Upgrade Nanny to Tutor Button */}
            {!editMode &&
              profile.is_nanny_approved &&
              !isNannyUser &&
              !profile.is_tutor_approved &&
              permissions.canApproveTutor && (
                <Button
                  onClick={handleUpgradeToTutor}
                  className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
                  disabled={isProcessing}
                >
                  {isProcessing ? (
                    <div className="animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent" />
                  ) : (
                    <BookOpen size={16} />
                  )}
                  <span>Αναβάθμιση σε Tutor</span>
                </Button>
              )}

            {/* Upgrade Tutor to Nanny Button */}
            {!editMode &&
              profile.is_tutor_approved &&
              !profile.is_nanny_approved &&
              !isNannyUser &&
              permissions.canApproveNanny && (
                <Button
                  onClick={handleUpgradeToNanny}
                  className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
                  disabled={isProcessing}
                >
                  {isProcessing ? (
                    <div className="animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent" />
                  ) : (
                    <User size={16} />
                  )}
                  <span>Αναβάθμιση σε Nanny</span>
                </Button>
              )}

            {/* Create Deal Button - Only for nannies and tutors */}
            {!editMode &&
              !isNannyUser &&
              (profile.type === "nanny" || profile.type === "tutor") &&
              permissions.canCreateDeal && (
                <CreateDealButton
                  candidateId={profile.id}
                  variant="default"
                  className="flex items-center gap-2 bg-blue-500 hover:bg-blue-600"
                />
              )}

            {/* Edit Mode Buttons */}
            {!editMode ? (
              permissions.canEdit && (
                <Button
                  onClick={onEdit}
                  className="flex items-center gap-2 bg-primary hover:bg-primary/90"
                >
                  <Pencil size={16} />
                  <span>Επεξεργασία</span>
                </Button>
              )
            ) : (
              <>
                <Button
                  onClick={onCancel}
                  variant="outline"
                  className="flex items-center gap-2"
                  disabled={saving}
                >
                  <X size={16} />
                  <span>Ακύρωση</span>
                </Button>
                <Button
                  onClick={onSave}
                  className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
                  disabled={saving}
                >
                  {saving ? (
                    <div className="animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent" />
                  ) : (
                    <Save size={16} />
                  )}
                  <span>Αποθήκευση</span>
                </Button>
              </>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default ProfileHeader;
