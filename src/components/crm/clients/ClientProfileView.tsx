import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import { usePermissionCheck } from "@/hooks/usePermissionCheck";
import {
  CLIENT_STATUS,
  getClientStatusLabel,
  getClientStatusVariant,
  isClientCandidate,
} from "@/lib/clientUtils";
import { supabase } from "@/lib/supabase";
import { getFilePreviewUrl } from "@/lib/utils";
import {
  deleteClient,
  fetchClientById,
  updateClient,
} from "@/services/clientService";
import { fetchDeals, Deal as ServiceDeal } from "@/services/dealService";
import {
  AlertCircle,
  ArrowLeft,
  Download,
  Eye,
  Trash,
  Upload,
  X,
} from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import CreateDealButton from "../../common/CreateDealButton";
import { Client } from "./ClientsTable";
import HistoryTab from "./tabs/HistoryTab";
import ProfileTab from "./tabs/ProfileTab";

// Use the Client interface from the service

// Use the Deal interface from the service
type Deal = ServiceDeal;
const CLIENTS_BUCKET = "client-documents";
const ClientProfileView = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { toast } = useToast();
  const { canEdit, canCreate, canDelete, hasSpecificPermission } =
    usePermissionCheck();

  // Check specific permissions for CRM
  const canEditCRM = canEdit("crm");
  const canDeleteCRM = canDelete("crm");
  const canCreateDeals = canCreate("deals");

  const [client, setClient] = useState<Client | null>(null);
  const [editedClient, setEditedClient] = useState<Client | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editMode, setEditMode] = useState(false);
  const [saving, setSaving] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // File upload states
  const [docFiles, setDocFiles] = useState<Record<string, File | null>>({
    document_upload: null,
  });

  const fileInputRefs: Record<string, React.RefObject<HTMLInputElement>> = {
    document_upload: useRef<HTMLInputElement>(null),
  };

  const [deals, setDeals] = useState<Deal[]>([]);
  const [dealsLoading, setDealsLoading] = useState(false);

  // Handler functions
  const handleEditClient = () => {
    // Check if user has permission to edit clients
    if (!canEditCRM) {
      toast({
        title: "Σφάλμα",
        description: "Δεν έχετε δικαίωμα επεξεργασίας πελατών.",
        variant: "destructive",
      });
      return;
    }

    setEditMode(true);
    // Create a deep copy of the client for editing
    setEditedClient(JSON.parse(JSON.stringify(client)));
  };

  const handleCancelEdit = () => {
    setEditMode(false);
    // Reset to original client data
    setEditedClient(JSON.parse(JSON.stringify(client)));
    // Reset file states
    setDocFiles({
      document_upload: null,
    });
  };

  const handleDeleteClient = async () => {
    if (!client) return;

    // Check if user has permission to delete clients
    if (!canDeleteCRM) {
      toast({
        title: "Σφάλμα",
        description: "Δεν έχετε δικαίωμα διαγραφής πελατών.",
        variant: "destructive",
      });
      setIsDeleteDialogOpen(false);
      return;
    }

    try {
      const success = await deleteClient(client.id);

      if (success) {
        toast({
          title: "Επιτυχία",
          description: "Ο πελάτης διαγράφηκε επιτυχώς.",
          variant: "default",
        });

        // Navigate back to clients list
        navigate("/crm/clients");
      } else {
        throw new Error("Failed to delete client");
      }
    } catch (error) {
      console.error("Error deleting client:", error);
      toast({
        title: "Σφάλμα",
        description:
          "Αποτυχία διαγραφής του πελάτη. Διεγράψτε τα deals που ανήκουν στον πελάτη και δοκιμάστε ξανά. ",
        variant: "destructive",
      });
    } finally {
      setIsDeleteDialogOpen(false);
    }
  };

  // Handle client status update
  const handleStatusUpdate = async (newStatus: number) => {
    if (!client) return;

    // Check if user has permission to edit clients
    if (!canEditCRM) {
      toast({
        title: "Σφάλμα",
        description: "Δεν έχετε δικαίωμα ενημέρωσης κατάστασης πελατών.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Update client status
      const updateResult = await updateClient(client.id.toString(), {
        status: newStatus,
      });

      if (!updateResult) {
        throw new Error("Failed to update client status");
      }

      // Update local state
      setClient({
        ...client,
        status: newStatus,
      });

      toast({
        title: "Ενημέρωση Κατάστασης",
        description: `Η κατάσταση του πελάτη άλλαξε σε ${getClientStatusLabel(
          newStatus
        )}`,
        variant: "success",
      });
    } catch (error) {
      console.error("Error updating client status:", error);
      toast({
        title: "Σφάλμα",
        description: "Αποτυχία ενημέρωσης κατάστασης πελάτη.",
        variant: "destructive",
      });
    }
  };

  // Handle position interest toggle
  const handlePositionInterestToggle = (positionId: string) => {
    if (!editedClient) return;

    const currentInterests = [
      ...(editedClient.form_data?.position_interests || []),
    ];
    const index = currentInterests.indexOf(positionId);

    if (index === -1) {
      // Add interest
      currentInterests.push(positionId);
    } else {
      // Remove interest
      currentInterests.splice(index, 1);
    }

    // Update form_data
    handleInputChange("form_data.position_interests", currentInterests);
  };

  const handleInputChange = (
    field: string,
    value: string | boolean | string[] | any
  ) => {
    if (!editedClient) return;

    // Handle nested form_data fields
    if (field.includes(".")) {
      const [parent, child] = field.split(".");
      if (parent === "form_data") {
        // Create a copy of form_data or initialize it if it doesn't exist
        const updatedFormData = { ...(editedClient.form_data || {}) };

        // Update the specific field in form_data
        updatedFormData[child] = value;

        // Update the client with the new form_data
        setEditedClient({
          ...editedClient,
          form_data: updatedFormData,
        });
      }
    } else {
      // Handle top-level fields
      setEditedClient({
        ...editedClient,
        [field]: value,
      });
    }
  };

  const handleFileChange = (
    type: keyof typeof docFiles,
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (e.target.files && e.target.files[0]) {
      setDocFiles({
        ...docFiles,
        [type]: e.target.files[0],
      });
      toast({
        title: "Επιλογή Αρχείου",
        description: `Το έγγραφο "${e.target.files[0].name}" επιλέχθηκε για ανέβασμα.`,
      });
    }
  };

  const triggerFileInput = (ref: React.RefObject<HTMLInputElement>) => {
    if (ref.current) {
      ref.current.click();
    }
  };

  const handleFilePreview = async (filePath: string) => {
    try {
      // If it's a path and not a full URL, get a signed URL
      if (!filePath.startsWith("http")) {
        // Use a longer expiration time for preview (4 hours)
        const expiresIn = 14400; // 4 hours in seconds

        // Use our utility function to get a properly formatted URL
        const previewUrl = await getFilePreviewUrl(
          CLIENTS_BUCKET,
          filePath,
          expiresIn
        );

        // Open the URL in a new tab
        window.open(previewUrl, "_blank");
      } else {
        // If it's already a URL, just open it
        window.open(filePath, "_blank");
      }
    } catch (error) {
      console.error("Error previewing file:", error);
      toast({
        title: "Σφάλμα",
        description:
          "Αποτυχία προεπισκόπησης αρχείου. Ο σύνδεσμος ενδέχεται να έχει λήξει - παρακαλώ δοκιμάστε ξανά.",
        variant: "destructive",
      });
    }
  };

  const handleFileDownload = async (filePath: string) => {
    try {
      let downloadUrl = filePath;

      // If it's a path and not a full URL, get a signed URL
      if (!filePath.startsWith("http")) {
        // Use a longer expiration time for downloads (24 hours)
        const { data, error } = await supabase.storage
          .from(CLIENTS_BUCKET)
          .createSignedUrl(filePath, 86400); // 24 hours in seconds

        if (error) {
          throw error;
        }

        downloadUrl = data.signedUrl;
      }

      // Create a temporary link element to trigger the download
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = filePath.split("/").pop() || "download";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: "Λήψη Ξεκίνησε",
        description: "Η λήψη του αρχείου σας ξεκίνησε.",
      });
    } catch (error) {
      console.error("Error downloading file:", error);
      toast({
        title: "Σφάλμα",
        description:
          "Αποτυχία λήψης αρχείου. Ο σύνδεσμος ενδέχεται να έχει λήξει - παρακαλώ δοκιμάστε ξανά.",
        variant: "destructive",
      });
    }
  };

  const handleSaveChanges = async () => {
    if (!editedClient) return;

    // Check if user has permission to edit clients
    if (!canEditCRM) {
      toast({
        title: "Σφάλμα",
        description: "Δεν έχετε δικαίωμα αποθήκευσης αλλαγών σε πελάτες.",
        variant: "destructive",
      });
      return;
    }

    setSaving(true);
    try {
      // First save the client data using our service
      const updateResult = await updateClient(editedClient.id.toString(), {
        form_data: editedClient.form_data,
      });

      if (!updateResult) {
        throw new Error("Failed to update client");
      }

      // Handle file uploads if any
      const uploadPromises = [];

      // Upload document files if any
      Object.entries(docFiles).forEach(([docType, file]) => {
        if (file) {
          // Create a consistent path format for files
          const fileExt = file.name.split(".").pop();
          const filePath = `${editedClient.storage_id}/${docType}.${fileExt}`;

          uploadPromises.push(
            supabase.storage
              .from(CLIENTS_BUCKET)
              .upload(filePath, file, { upsert: true })
              .then(async (result) => {
                if (result.error) {
                  console.error(`Error uploading ${docType}:`, result.error);
                  throw result.error;
                }

                // Verify that we can create a signed URL for the file
                const { data: signedUrlData, error: urlError } =
                  await supabase.storage
                    .from(CLIENTS_BUCKET)
                    .createSignedUrl(filePath, 3600);

                if (urlError) {
                  console.error(
                    `Error creating signed URL for ${docType}:`,
                    urlError
                  );
                  throw urlError;
                }

                // Store just the path, not the full URL
                // Update client with document path
                const updatedFormData = { ...editedClient.form_data };
                updatedFormData[`${docType}`] = filePath;

                const { error: updateError } = await supabase
                  .from("clients")
                  .update({
                    form_data: updatedFormData,
                  })
                  .eq("id", editedClient.id);

                if (updateError) {
                  console.error(
                    `Error updating client with ${docType} path:`,
                    updateError
                  );
                  throw updateError;
                }

                toast({
                  title: "Επιτυχία",
                  description: `Το ${docType} ανέβηκε επιτυχώς.`,
                });
              })
              .catch((error) => {
                console.error(`Error in ${docType} upload process:`, error);
                toast({
                  title: "Σφάλμα",
                  description: `Υπήρξε πρόβλημα κατά την αποθήκευση του ${docType}.`,
                  variant: "destructive",
                });
                throw error;
              })
          );
        }
      });

      // Wait for all uploads to complete
      if (uploadPromises.length > 0) {
        await Promise.all(uploadPromises);
      }

      // Fetch updated client data using our service
      const updatedClient = await fetchClientById(editedClient.id.toString());

      // Update local state
      if (updatedClient) {
        setClient(updatedClient);
        setEditedClient(JSON.parse(JSON.stringify(updatedClient)));
      }

      toast({
        title: "Επιτυχία",
        description: "Το προφίλ του πελάτη ενημερώθηκε με επιτυχία.",
        variant: "success",
      });

      setEditMode(false);
      setDocFiles({
        document_upload: null,
      });
    } catch (error) {
      console.error("Error saving changes:", error);
      toast({
        title: "Σφάλμα",
        description: "Αποτυχία ενημέρωσης προφίλ πελάτη.",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  // Function to fetch deals by client ID
  const fetchClientDeals = async (clientId: string) => {
    try {
      setDealsLoading(true);

      // Fetch deals with client_id filter using the updated service with joins
      const clientDeals = await fetchDeals(false, {
        page: 1,
        itemsPerPage: 100,
        filters: { client_id: clientId },
      });

      // Check if the result is paginated or an array
      if (Array.isArray(clientDeals)) {
        setDeals(clientDeals);
      } else {
        setDeals(clientDeals.data);
      }
    } catch (error) {
      console.error("Error fetching deals:", error);
      toast({
        title: "Σφάλμα",
        description: "Αποτυχία λήψης συμφωνιών πελάτη.",
        variant: "destructive",
      });
    } finally {
      setDealsLoading(false);
    }
  };

  useEffect(() => {
    const loadClient = async () => {
      if (!id) {
        setError("Client ID is missing");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // Fetch client using the service with caching
        const clientData = await fetchClientById(id);

        if (clientData) {
          setClient(clientData);
          setEditedClient(JSON.parse(JSON.stringify(clientData))); // Deep copy

          // Fetch deals for this client
          await fetchClientDeals(clientData.id.toString());
        } else {
          setError("Client not found");
        }
      } catch (err: any) {
        console.error("Error fetching client:", err);
        setError(err.message || "Failed to fetch client");
      } finally {
        setLoading(false);
      }
    };

    loadClient();
  }, [id]);

  if (loading) {
    return (
      <div className="w-full max-w-[1920px] mx-auto py-6 space-y-8 bg-white min-h-screen px-4 md:px-6 lg:px-8 xl:px-12">
        <Button
          variant="ghost"
          className="flex items-center gap-1 mb-4 hover:bg-primary/10 transition-colors"
          onClick={() => navigate(-1)}
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Επιστροφή</span>
        </Button>

        {/* Header with skeleton */}
        <div className="flex justify-between items-center">
          <div className="inline-flex items-center bg-white/80 backdrop-blur-sm px-4 py-3 rounded-lg shadow-sm border border-primary/10">
            <div className="space-y-1">
              <Skeleton className="h-8 w-32" />
              <Skeleton className="h-4 w-24" />
            </div>
          </div>
        </div>

        {/* Tabs with skeleton content */}
        <div className="space-y-6">
          <div className="flex justify-end mb-4">
            <Skeleton className="h-9 w-64 rounded-lg" />
          </div>

          <Card
            className="p-4 flex items-center gap-4 bg-white border border-primary/10 shadow-sm"
            variant="default"
          >
            <Skeleton className="h-12 w-12 rounded-full" />
            <div className="space-y-1">
              <Skeleton className="h-5 w-48" />
              <Skeleton className="h-4 w-32" />
            </div>
          </Card>

          <Card
            className="p-4 bg-white border border-tertiary/20 shadow-sm"
            variant="default"
          >
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {Array(8)
                .fill(0)
                .map((_, i) => (
                  <div key={i} className="space-y-2">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                ))}
            </div>
          </Card>
        </div>
      </div>
    );
  }

  if (error || !client) {
    return (
      <div className="w-full max-w-[1920px] mx-auto py-6 space-y-8 bg-white min-h-screen px-4 md:px-6 lg:px-8 xl:px-12">
        <Button
          variant="ghost"
          className="flex items-center gap-1 mb-4 hover:bg-primary/10 transition-colors"
          onClick={() => navigate(-1)}
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Επιστροφή</span>
        </Button>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error || "Client not found"}</AlertDescription>
        </Alert>
      </div>
    );
  }

  // Format date for display
  const formatDate = (dateStr: string | undefined) => {
    if (!dateStr) return "";
    const date = new Date(dateStr);
    return date.toLocaleDateString("el-GR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  return (
    <div className="w-full max-w-[1920px] mx-auto py-6 space-y-8 bg-white min-h-screen px-4 md:px-6 lg:px-8 xl:px-12">
      {/* Back Button */}
      <Button
        variant="ghost"
        className="flex items-center gap-1 mb-4 hover:bg-primary/10 transition-colors"
        onClick={() => navigate(-1)}
      >
        <ArrowLeft className="h-4 w-4" />
        <span>Επιστροφή</span>
      </Button>
      {/* Header with ID and Date */}
      <div className="flex justify-between items-center">
        <Card
          className="p-4 flex items-center gap-4 bg-white border border-primary/10 shadow-sm"
          variant="default"
        >
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <h2 className="text-2xl font-semibold text-primary">
                {`CL-${client.id.toString().substring(0, 5)}`}
              </h2>
              <Badge
                variant={
                  isClientCandidate(client.status) ? "outline" : "default"
                }
                className="ml-2"
              >
                {isClientCandidate(client.status) ? "Υποψήφιος" : "Πελάτης"}
              </Badge>
              <Badge
                variant={getClientStatusVariant(client.status)}
                className="ml-1"
              >
                {getClientStatusLabel(client.status)}
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground">
              {formatDate(client.created_at || "")}
            </p>
          </div>

          <Avatar className="h-12 w-12 border-2 border-primary/20">
            <AvatarFallback className="bg-primary text-primary-foreground font-semibold">
              {client.form_data?.father_name?.[0] || "C"}
            </AvatarFallback>
          </Avatar>
          <div className="space-y-1">
            <h3 className="text-lg font-semibold text-primary">
              {client.form_data?.father_name || ""}{" "}
              {client.form_data?.mother_name || ""}
            </h3>
            <p className="text-sm text-muted-foreground">
              {client.form_data?.email || "Δεν δόθηκε διεύθυνση email"}
            </p>
          </div>
        </Card>

        <div className="flex gap-2">
          {/* Status Update Dropdown for Lead Clients */}
          {/* Status Update Buttons */}
          {!editMode &&
            canEditCRM &&
            (client.status === CLIENT_STATUS.LEAD ? (
              <Button
                variant="default"
                className="flex items-center gap-2"
                onClick={() => handleStatusUpdate(CLIENT_STATUS.REJECTED)}
              >
                Απόρριψη
              </Button>
            ) : (
              client.status === CLIENT_STATUS.REJECTED && (
                <Button
                  variant="default"
                  className="flex items-center gap-2"
                  onClick={() => handleStatusUpdate(CLIENT_STATUS.LEAD)}
                >
                  Αλλαγή σε Lead
                </Button>
              )
            ))}

          {/* Create Deal Button - Only for lead, active client, or past client */}
          {!editMode &&
            canCreateDeals &&
            (client.status === CLIENT_STATUS.LEAD ||
              client.status === CLIENT_STATUS.ACTIVE_CLIENT ||
              client.status === CLIENT_STATUS.PAST_CLIENT) && (
              <CreateDealButton
                clientId={client.id.toString()}
                variant="default"
                className="flex items-center gap-2 bg-blue-500 hover:bg-blue-600"
              />
            )}

          {!editMode ? (
            <>
              {canEditCRM && (
                <Button
                  variant="outline"
                  className="flex items-center gap-2"
                  onClick={handleEditClient}
                >
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z" />
                    <path d="m15 5 4 4" />
                  </svg>
                  Επεξεργασία
                </Button>
              )}
              {canDeleteCRM && (
                <Button
                  variant="destructive"
                  className="flex items-center gap-2"
                  onClick={() => setIsDeleteDialogOpen(true)}
                >
                  <Trash size={16} />
                  <span>Διαγραφή</span>
                </Button>
              )}
              <AlertDialog
                open={isDeleteDialogOpen}
                onOpenChange={setIsDeleteDialogOpen}
              >
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Διαγραφή Επαφής</AlertDialogTitle>
                    <AlertDialogDescription>
                      Είστε βέβαιοι ότι θέλετε να διαγράψετε την επαφή "
                      {client.form_data?.father_name} &
                      {client.form_data?.mother_name}" ? Αυτή η ενέργεια δεν
                      μπορεί να αναιρεθεί.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Άκυρο</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={handleDeleteClient}
                      className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    >
                      Διαγραφή
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </>
          ) : (
            <>
              <Button
                variant="outline"
                className="flex items-center gap-2"
                onClick={handleCancelEdit}
                disabled={saving}
              >
                <X size={16} />
                <span>Ακύρωση</span>
              </Button>
              <Button
                className="flex items-center gap-2 bg-primary text-white"
                onClick={handleSaveChanges}
                disabled={saving}
              >
                {saving ? (
                  <div className="animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent"></div>
                ) : (
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z" />
                    <polyline points="17 21 17 13 7 13 7 21" />
                    <polyline points="7 3 7 8 15 8" />
                  </svg>
                )}
                <span>Αποθήκευση</span>
              </Button>
            </>
          )}
        </div>
      </div>

      <Tabs defaultValue="profile" className="w-full">
        <div className="flex justify-end mb-4">
          <TabsList className="w-full sm:w-auto grid grid-cols-3 h-9 bg p-1 rounded-lg">
            <TabsTrigger
              value="profile"
              className="data-[state=active]:bg-white data-[state=active]:text-primary data-[state=active]:shadow-sm"
            >
              Προφίλ
            </TabsTrigger>
            <TabsTrigger
              value="files"
              className="data-[state=active]:bg-white data-[state=active]:text-primary data-[state=active]:shadow-sm"
            >
              Αρχεία
            </TabsTrigger>
            <TabsTrigger
              value="history"
              className="data-[state=active]:bg-white data-[state=active]:text-primary data-[state=active]:shadow-sm"
            >
              Ιστορικό
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="profile" className="mt-0">
          <ProfileTab
            client={client}
            editedClient={editedClient}
            editMode={editMode}
            handleInputChange={handleInputChange}
            handlePositionInterestToggle={handlePositionInterestToggle}
            isContact={!deals || deals.length === 0}
            deals={deals}
            dealsLoading={dealsLoading}
          />
        </TabsContent>

        <TabsContent value="files" className="mt-0">
          <div className="space-y-6">
            <Card className="p-6" variant="light">
              <h3 className="text-lg font-medium text-primary">
                Αρχεία Πελάτη
              </h3>
              <p className="text-secondary">
                Όλα τα έγγραφα που σχετίζονται με τον πελάτη.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
                {/* Document Upload */}
                <div className="border border-light/30 rounded-lg p-4 flex items-center gap-3">
                  <div className="bg-primary/10 p-2 rounded-md">
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-primary"
                    >
                      <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                      <polyline points="14 2 14 8 20 8" />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <p className="font-medium">Αποδεικτικό</p>
                    <p className="text-sm text-muted-foreground">
                      {client.form_data?.document_upload
                        ? "Υποβλήθηκε"
                        : "Δεν έχει υποβληθεί"}
                    </p>
                    {client.form_data?.document_upload && !editMode && (
                      <div className="flex gap-2 mt-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 px-2"
                          onClick={() =>
                            handleFilePreview(client.form_data.document_upload)
                          }
                        >
                          <Eye size={14} className="mr-1" />
                          <span className="text-xs">Προβολή</span>
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 px-2"
                          onClick={() =>
                            handleFileDownload(client.form_data.document_upload)
                          }
                        >
                          <Download size={14} className="mr-1" />
                          <span className="text-xs">Κατέβασμα</span>
                        </Button>
                      </div>
                    )}
                  </div>
                  {editMode && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="ml-auto"
                      onClick={() =>
                        triggerFileInput(fileInputRefs.document_upload)
                      }
                    >
                      <Upload size={16} className="mr-2" />
                      {docFiles.document_upload ? "Αλλαγή" : "Ανέβασμα"}
                    </Button>
                  )}
                </div>
              </div>

              {/* Hidden file inputs */}
              <input
                type="file"
                ref={fileInputRefs.document_upload}
                className="hidden"
                onChange={(e) => handleFileChange("document_upload", e)}
                accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
              />
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="history" className="mt-0">
          {client && <HistoryTab client={client} editMode={editMode} />}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ClientProfileView;
