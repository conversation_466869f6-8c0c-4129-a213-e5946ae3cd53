import { Badge } from "@/components/ui/badge";
import { languageLevels } from "@/lib/staticData";
import {
  getCandidateTypeLabel,
  getCandidateTypeOptions,
  getCityLabel,
  getCityOptions,
  getLanguageLabel,
  getLanguageOptions,
  getPositionLabel,
  getPositionOptions,
} from "@/lib/tableUtils";
import { startDateOptions } from "@/schemas/FormSchema";
import {
  Candidate,
  approveCandidate,
  fetchCandidates,
  rejectCandidate,
} from "@/services/candidateService";
import { User, UserCircle, UserRound } from "lucide-react";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import NannyBlueTable from "../common/NannyBlueTable";
import { ColumnConfig } from "../crm/BasicTable";
import { toast } from "../ui/use-toast";
import CandidateContextMenu from "./CandidateContextMenu";

const CandidatesTable = () => {
  const navigate = useNavigate();
  const [shouldReload, setShouldReload] = useState(false);

  const columns: ColumnConfig<Candidate | any>[] = [
    {
      key: "id",
      header: "ID",
      filterable: true,
      filterType: "text",
      sortable: true,

      render: (item) => {
        const gender = item.form_data?.gender?.toLowerCase();

        return (
          <div className="flex items-center gap-2">
            {gender === "male" ? (
              <UserCircle className="h-4 w-4 text-blue-500" />
            ) : gender === "female" ? (
              <UserRound className="h-4 w-4 text-pink-500" />
            ) : (
              <User className="h-4 w-4 text-gray-500" />
            )}
            <span>CAND-{item.id}</span>
          </div>
        );
      },
    },
    {
      key: "is_rejected",
      header: "Rejected",
      filterable: true,
      filterType: "select",
      options: ["Ναι", "Όχι"],
      render: (item) =>
        item.is_rejected ? (
          <Badge variant="destructive">Rejected</Badge>
        ) : (
          <Badge variant="outline">Pending</Badge>
        ),
    },

    {
      key: "form_data.name",
      header: "Όνομα",
      filterable: true,
      filterType: "text",

      render: (item) => item.form_data?.name || "-",
    },
    {
      key: "form_data.surname",
      header: "Επώνυμο",
      filterable: true,
      filterType: "text",

      render: (item) => item.form_data?.surname || "-",
    },

    {
      key: "form_data.duration_interests",
      header: "Διάρκεια",
      filterable: true,
      filterType: "select",
      options: ["Long-Term", "Short-Term"],

      render: (item) => (
        <div className="flex flex-wrap gap-1">
          {Array.isArray(item.form_data?.duration_interests) &&
            item.form_data?.duration_interests.length > 0 ? (
            item.form_data.duration_interests.map((duration: string) => (
              <Badge key={duration} variant="secondary">
                {duration
                  .split("-")
                  .map((word: string) => word[0].toUpperCase() + word.slice(1))
                  .join("-")}
              </Badge>
            ))
          ) : (
            <span className="text-gray-400">-</span>
          )}
        </div>
      ),
    },
    {
      key: "form_data.candidate_type",
      header: "Κατηγορία",
      filterable: true,
      filterType: "select",
      options: getCandidateTypeOptions().map(
        (option) => option.split(" / ")[0]
      ),
      render: (item) => (
        <Badge variant="secondary">
          {
            getCandidateTypeLabel(item.form_data?.candidate_type).split(
              " / "
            )[0]
          }
        </Badge>
      ),
    },
    {
      key: "form_data.schedule_interests",
      header: "Είδος",
      filterable: true,
      filterType: "select",
      options: ["Full-Time", "Part-Time"],

      render: (item) => (
        <div className="flex flex-wrap gap-1">
          {Array.isArray(item.form_data?.schedule_interests) &&
            item.form_data?.schedule_interests.length > 0 ? (
            item.form_data.schedule_interests.map((schedule: string) => (
              <Badge key={schedule} variant="outline">
                {schedule
                  .split("-")
                  .map((word: string) => word[0].toUpperCase() + word.slice(1))
                  .join("-")}
              </Badge>
            ))
          ) : (
            <span className="text-gray-400">-</span>
          )}
        </div>
      ),
    },
    {
      key: "form_data.position_interests",
      header: "Θέση",
      filterable: true,
      filterType: "select",
      options: getPositionOptions(),

      render: (item) => (
        <div className="flex flex-wrap gap-1">
          {Array.isArray(item.form_data?.position_interests) &&
            item.form_data?.position_interests.length > 0 ? (
            item.form_data.position_interests.map((position: string) => (
              <Badge key={position} variant="outline">
                {getPositionLabel(position)}
              </Badge>
            ))
          ) : (
            <span className="text-gray-400">-</span>
          )}
        </div>
      ),
    },
    {
      key: "form_data.languages",
      header: "Γλώσσα",
      filterable: true,
      filterType: "select",
      options: getLanguageOptions(),

      render: (item) => (
        <div className="flex flex-wrap gap-1">
          {item.form_data?.languages &&
            Array.isArray(item.form_data?.languages) &&
            item.form_data?.languages.map(
              (lang: { language: string; level: string }, index: number) => (
                <Badge key={index} variant="outline">
                  <span>{getLanguageLabel(lang.language)}</span>
                  <span className="text-xs text-gray-500">
                    (
                    {languageLevels.find((level) => level.id === lang.level)
                      ?.labelEl || lang.level}
                    )
                  </span>
                </Badge>
              )
            )}
        </div>
      ),
    },
    {
      key: "form_data.start_availability",
      header: "Έναρξη",
      filterable: true,
      filterType: "select",
      options: startDateOptions.map((option) => option.labelEl),

      render: (item) => {
        const startAvailability = item.form_data?.start_availability;
        const startDateOption = startDateOptions.find(
          (option) => option.id === startAvailability
        );
        return (
          <div className="flex flex-wrap gap-1">
            {startDateOption
              ? startDateOption.labelEl
              : startAvailability || "Δεν δηλώθηκε"}
          </div>
        );
      },
    },
    {
      key: "form_data.years_experience",
      header: "Εμπειρία",
      filterable: true,
      filterType: "number",

      render: (item: Candidate) =>
        item.form_data?.years_experience
          ? `${item.form_data.years_experience} έτη`
          : "-",
    },
    {
      key: "form_data.city",
      header: "Τοποθεσία",
      filterable: true,
      filterType: "city-select",
      options: getCityOptions(),
      render: (item) => getCityLabel(item.city || item.form_data?.city),
    },
  ];

  const handleApproveNanny = async (candidate: Candidate) => {
    try {
      const success = await approveCandidate(candidate.id.toString(), "Nanny");

      if (success) {
        toast({
          title: "Επιτυχία",
          description: `Εγκρίθηκε ως Nanny: ${candidate.form_data.name} ${candidate.form_data.surname}`,
        });

        setShouldReload((prev) => !prev);
      } else {
        throw new Error("Failed to approve candidate as nanny");
      }
    } catch (error) {
      console.error("Error approving candidate as nanny:", error);
      toast({
        title: "Σφάλμα",
        description: "Απέτυχε η εγκρισή του υποψήφου ως Nanny.",
        variant: "destructive",
      });
    }
  };

  const handleApproveTutor = async (candidate: Candidate) => {
    try {
      const success = await approveCandidate(candidate.id.toString(), "Tutor");

      if (success) {
        toast({
          title: "Επιτυχία",
          description: `Εγκρίθηκε ως Turor: ${candidate.form_data.name} ${candidate.form_data.surname}`,
        });

        setShouldReload((prev) => !prev);
      } else {
        throw new Error("Failed to approve candidate as tutor");
      }
    } catch (error) {
      console.error("Error approving candidate as tutor:", error);
      toast({
        title: "Σφάλμα",
        description: "Απέτυχε η εγκρισή του υποψήφου ως Tutor.",
        variant: "destructive",
      });
    }
  };

  const handleReject = async (candidate: Candidate) => {
    try {
      const success = await rejectCandidate(candidate.id.toString());

      if (success) {
        toast({
          title: "Επιτυχία",
          description: `Απορρίφθηκε: ${candidate.form_data.name} ${candidate.form_data.surname}`,
        });

        setShouldReload((prev) => !prev);
      } else {
        throw new Error("Failed to reject candidate");
      }
    } catch (error) {
      console.error("Error rejecting candidate:", error);
      toast({
        title: "Σφάλμα",
        description: "Απέτυχε η απόρριψη του υποψήφου.",
        variant: "destructive",
      });
    }
  };

  // Use the reusable context menu component
  const { contextMenuItems, dialogComponents } = CandidateContextMenu({
    candidateType: "candidate",
    onReject: handleReject,
    onApproveNanny: handleApproveNanny,
    onApproveTutor: handleApproveTutor,
    onReload: () => setShouldReload((prev) => !prev),
  });

  return (
    <div className="space-y-4">
      <>
        <NannyBlueTable
          fetchData={fetchCandidates}
          columns={columns}
          onRowClick={(row) => navigate(`/candidates/${row.id}`)}
          contextMenuItems={contextMenuItems}
          shouldReload={shouldReload}
        />
        {dialogComponents}
      </>
    </div>
  );
};

export default CandidatesTable;
