import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/components/ui/use-toast";
import apiClient from "@/lib/api";
import {
  Briefcase,
  Car,
  Clock,
  Euro,
  Home,
  MapPin,
  Plus,
  Trash2,
} from "lucide-react";
import { Candidate } from "../models/Candidate";

interface PriceListTabProps {
  candidate: Candidate;
  editedCandidate: Candidate | null;
  editMode: boolean;
  handleInputChange: (
    field: string,
    value: string | boolean | string[]
  ) => void;
}

const PriceListTab = ({
  candidate,
  editedCandidate,
  editMode,
  handleInputChange,
}: PriceListTabProps) => {
  // Helper function to add a new note
  const addNote = () => {
    if (!editedCandidate) return;

    const currentNotes = [
      ...(editedCandidate.form_data?.price_list_notes || []),
    ];
    currentNotes.push("");

    handleInputChange("form_data.price_list_notes", currentNotes);
  };

  // Helper function to update a note
  const updateNote = (index: number, value: string) => {
    if (!editedCandidate) return;

    const currentNotes = [
      ...(editedCandidate.form_data?.price_list_notes || []),
    ];
    currentNotes[index] = value;

    handleInputChange("form_data.price_list_notes", currentNotes);
  };

  // Helper function to remove a note
  const removeNote = (index: number) => {
    if (!editedCandidate) return;

    const currentNotes = [
      ...(editedCandidate.form_data?.price_list_notes || []),
    ];
    currentNotes.splice(index, 1);

    handleInputChange("form_data.price_list_notes", currentNotes);
  };

  return (
    <div className="space-y-6">
      {/* Position Details */}
      <Card className="p-6" variant="accent">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2 mb-4">
            <Briefcase className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-medium text-primary">Στοιχεία Θέσης</h3>
          </div>
          <Button
            onClick={async () => {
              try {
                await apiClient.put(
                  `/candidates/${candidate.id}/generate-file`,
                  {
                    file_type: "price_list",
                  }
                );
                toast({
                  title: "Επιτυχία",
                  description:
                    "Το PDF του τιμοκαταλόγου δημιουργήθηκε με επιτυχία.",
                });
                setTimeout(() => {
                  window.location.reload();
                }, 500);
              } catch (error) {
                console.error("Error generating price list PDF:", error);
                toast({
                  title: "Σφάλμα",
                  description:
                    "Απέτυχε η δημιουργία του PDF του τιμοκαταλόγου.",
                  variant: "destructive",
                });
              }
            }}
            variant="outline"
            className="border-primary text-primary hover:bg-primary/10"
          >
            Δημιουργία PDF Τιμοκαταλόγου
          </Button>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Position Type */}
          <div className="space-y-2">
            <Label htmlFor="position-type">Τύπος θέσης</Label>
            {editMode ? (
              <Input
                id="position-type"
                value={
                  editedCandidate?.form_data?.price_list_position_type || ""
                }
                onChange={(e) =>
                  handleInputChange(
                    "form_data.price_list_position_type",
                    e.target.value
                  )
                }
                placeholder="π.χ. Nanny πλήρους απασχόλησης"
              />
            ) : (
              <p className="text-sm p-2 bg-muted/20 rounded-md">
                {candidate.form_data?.price_list_position_type || "-"}
              </p>
            )}
          </div>

          {/* Location */}
          <div className="space-y-2">
            <Label htmlFor="location">Τοποθεσία</Label>
            {editMode ? (
              <Input
                id="location"
                value={editedCandidate?.form_data?.price_list_location || ""}
                onChange={(e) =>
                  handleInputChange(
                    "form_data.price_list_location",
                    e.target.value
                  )
                }
                placeholder="π.χ. Αθήνα, Βόρεια Προάστια"
              />
            ) : (
              <p className="text-sm p-2 bg-muted/20 rounded-md">
                {candidate.form_data?.price_list_location || "-"}
              </p>
            )}
          </div>

          {/* Start Date */}
          <div className="space-y-2">
            <Label htmlFor="start-date">Έναρξη</Label>
            {editMode ? (
              <Input
                id="start-date"
                value={editedCandidate?.form_data?.price_list_start_date || ""}
                onChange={(e) =>
                  handleInputChange(
                    "form_data.price_list_start_date",
                    e.target.value
                  )
                }
                placeholder="π.χ. Άμεσα"
              />
            ) : (
              <p className="text-sm p-2 bg-muted/20 rounded-md">
                {candidate.form_data?.price_list_start_date || "-"}
              </p>
            )}
          </div>

          {/* Duration */}
          <div className="space-y-2">
            <Label htmlFor="duration">Διάρκεια</Label>
            {editMode ? (
              <Input
                id="duration"
                value={editedCandidate?.form_data?.price_list_duration || ""}
                onChange={(e) =>
                  handleInputChange(
                    "form_data.price_list_duration",
                    e.target.value
                  )
                }
                placeholder="π.χ. Μακροχρόνια"
              />
            ) : (
              <p className="text-sm p-2 bg-muted/20 rounded-md">
                {candidate.form_data?.price_list_duration || "-"}
              </p>
            )}
          </div>
        </div>
      </Card>

      {/* Schedule and Compensation */}
      <Card className="p-6" variant="accent">
        <div className="flex items-center gap-2 mb-4">
          <Clock className="h-5 w-5 text-primary" />
          <h3 className="text-lg font-medium text-primary">
            Πρόγραμμα & Αμοιβή
          </h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Schedule */}
          <div className="space-y-2">
            <Label htmlFor="schedule">Πρόγραμμα</Label>
            {editMode ? (
              <Input
                id="schedule"
                value={editedCandidate?.form_data?.price_list_schedule || ""}
                onChange={(e) =>
                  handleInputChange(
                    "form_data.price_list_schedule",
                    e.target.value
                  )
                }
                placeholder="π.χ. Δευτέρα-Παρασκευή, 8:00-16:00"
              />
            ) : (
              <p className="text-sm p-2 bg-muted/20 rounded-md">
                {candidate.form_data?.price_list_schedule || "-"}
              </p>
            )}
          </div>

          {/* Days Off */}
          <div className="space-y-2">
            <Label htmlFor="days-off">Ρεπό</Label>
            {editMode ? (
              <Input
                id="days-off"
                value={editedCandidate?.form_data?.price_list_days_off || ""}
                onChange={(e) =>
                  handleInputChange(
                    "form_data.price_list_days_off",
                    e.target.value
                  )
                }
                placeholder="π.χ. Σαββατοκύριακα"
              />
            ) : (
              <p className="text-sm p-2 bg-muted/20 rounded-md">
                {candidate.form_data?.price_list_days_off || "-"}
              </p>
            )}
          </div>

          {/* Salary */}
          <div className="space-y-2">
            <Label htmlFor="salary">Μισθός</Label>
            {editMode ? (
              <Input
                id="salary"
                value={editedCandidate?.form_data?.price_list_salary || ""}
                onChange={(e) =>
                  handleInputChange(
                    "form_data.price_list_salary",
                    e.target.value
                  )
                }
                placeholder="π.χ. 800€ / μήνα"
              />
            ) : (
              <p className="text-sm p-2 bg-muted/20 rounded-md">
                {candidate.form_data?.price_list_salary || "-"}
              </p>
            )}
          </div>

          {/* Insurance */}
          <div className="space-y-2">
            <Label htmlFor="insurance">Ασφάλιση</Label>
            {editMode ? (
              <Input
                id="insurance"
                value={editedCandidate?.form_data?.price_list_insurance || ""}
                onChange={(e) =>
                  handleInputChange(
                    "form_data.price_list_insurance",
                    e.target.value
                  )
                }
                placeholder="π.χ. ΙΚΑ"
              />
            ) : (
              <p className="text-sm p-2 bg-muted/20 rounded-md">
                {candidate.form_data?.price_list_insurance || "-"}
              </p>
            )}
          </div>
        </div>
      </Card>

      {/* Hourly Rates */}
      <Card className="p-6" variant="accent">
        <div className="flex items-center gap-2 mb-4">
          <Euro className="h-5 w-5 text-primary" />
          <h3 className="text-lg font-medium text-primary">Ωριαίες Χρεώσεις</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Weekday Rate */}
          <div className="space-y-2">
            <Label htmlFor="weekday-rate">Χρέωση ανά ώρα καθημερινές</Label>
            {editMode ? (
              <Input
                id="weekday-rate"
                value={
                  editedCandidate?.form_data?.price_list_weekday_rate || ""
                }
                onChange={(e) =>
                  handleInputChange(
                    "form_data.price_list_weekday_rate",
                    e.target.value
                  )
                }
                placeholder="π.χ. 10€/ώρα"
              />
            ) : (
              <p className="text-sm p-2 bg-muted/20 rounded-md">
                {candidate.form_data?.price_list_weekday_rate || "-"}
              </p>
            )}
          </div>

          {/* Weekend Rate */}
          <div className="space-y-2">
            <Label htmlFor="weekend-rate">Χρέωση ανά ώρα Σαββατοκύριακα</Label>
            {editMode ? (
              <Input
                id="weekend-rate"
                value={
                  editedCandidate?.form_data?.price_list_weekend_rate || ""
                }
                onChange={(e) =>
                  handleInputChange(
                    "form_data.price_list_weekend_rate",
                    e.target.value
                  )
                }
                placeholder="π.χ. 12€/ώρα"
              />
            ) : (
              <p className="text-sm p-2 bg-muted/20 rounded-md">
                {candidate.form_data?.price_list_weekend_rate || "-"}
              </p>
            )}
          </div>

          {/* Overnight Rate */}
          <div className="space-y-2">
            <Label htmlFor="overnight-rate">Διανυκτέρευση</Label>
            {editMode ? (
              <Input
                id="overnight-rate"
                value={
                  editedCandidate?.form_data?.price_list_overnight_rate || ""
                }
                onChange={(e) =>
                  handleInputChange(
                    "form_data.price_list_overnight_rate",
                    e.target.value
                  )
                }
                placeholder="π.χ. 22:00-8:00, 50€"
              />
            ) : (
              <p className="text-sm p-2 bg-muted/20 rounded-md">
                {candidate.form_data?.price_list_overnight_rate || "-"}
              </p>
            )}
          </div>

          {/* Out of Town Rate */}
          <div className="space-y-2">
            <Label htmlFor="out-of-town-rate">Για φύλαξη εκτός έδρας</Label>
            {editMode ? (
              <Input
                id="out-of-town-rate"
                value={
                  editedCandidate?.form_data?.price_list_out_of_town_rate || ""
                }
                onChange={(e) =>
                  handleInputChange(
                    "form_data.price_list_out_of_town_rate",
                    e.target.value
                  )
                }
                placeholder="π.χ. 100€/ημέρα"
              />
            ) : (
              <p className="text-sm p-2 bg-muted/20 rounded-md">
                {candidate.form_data?.price_list_out_of_town_rate || "-"}
              </p>
            )}
          </div>

          {/* Additional Children Rate */}
          <div className="space-y-2 md:col-span-2">
            <Label htmlFor="additional-children-rate">
              Για φύλαξη επιπλέον παιδιών
            </Label>
            {editMode ? (
              <Input
                id="additional-children-rate"
                value={
                  editedCandidate?.form_data
                    ?.price_list_additional_children_rate || ""
                }
                onChange={(e) =>
                  handleInputChange(
                    "form_data.price_list_additional_children_rate",
                    e.target.value
                  )
                }
                placeholder="π.χ. Ισχύουν οι παραπάνω χρεώσεις ανά οικογένεια"
              />
            ) : (
              <p className="text-sm p-2 bg-muted/20 rounded-md">
                {candidate.form_data?.price_list_additional_children_rate ||
                  "-"}
              </p>
            )}
          </div>
        </div>
      </Card>

      {/* Coverage */}
      <Card className="p-6" variant="accent">
        <div className="flex items-center gap-2 mb-4">
          <Home className="h-5 w-5 text-primary" />
          <h3 className="text-lg font-medium text-primary">Κάλυψη</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Transportation */}
          <div className="flex items-center space-x-2">
            {editMode ? (
              <Checkbox
                id="transportation"
                checked={
                  editedCandidate?.form_data
                    ?.price_list_transportation_covered || false
                }
                onCheckedChange={(checked) =>
                  handleInputChange(
                    "form_data.price_list_transportation_covered",
                    checked === true
                  )
                }
              />
            ) : (
              <div
                className={`h-4 w-4 rounded-sm border ${candidate.form_data?.price_list_transportation_covered
                    ? "bg-primary border-primary"
                    : "border-input"
                  }`}
              >
                {candidate.form_data?.price_list_transportation_covered && (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-4 w-4 text-white"
                  >
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                )}
              </div>
            )}
            <Label htmlFor="transportation">
              Μεταφορικά (βενζίνες/διόδια/εισιτήρια κλπ.)
            </Label>
          </div>

          {/* Food */}
          <div className="flex items-center space-x-2">
            {editMode ? (
              <Checkbox
                id="food"
                checked={
                  editedCandidate?.form_data?.price_list_food_covered || false
                }
                onCheckedChange={(checked) =>
                  handleInputChange(
                    "form_data.price_list_food_covered",
                    checked === true
                  )
                }
              />
            ) : (
              <div
                className={`h-4 w-4 rounded-sm border ${candidate.form_data?.price_list_food_covered
                    ? "bg-primary border-primary"
                    : "border-input"
                  }`}
              >
                {candidate.form_data?.price_list_food_covered && (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-4 w-4 text-white"
                  >
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                )}
              </div>
            )}
            <Label htmlFor="food">Διατροφή</Label>
          </div>

          {/* Accommodation */}
          <div className="flex items-center space-x-2">
            {editMode ? (
              <Checkbox
                id="accommodation"
                checked={
                  editedCandidate?.form_data
                    ?.price_list_accommodation_covered || false
                }
                onCheckedChange={(checked) =>
                  handleInputChange(
                    "form_data.price_list_accommodation_covered",
                    checked === true
                  )
                }
              />
            ) : (
              <div
                className={`h-4 w-4 rounded-sm border ${candidate.form_data?.price_list_accommodation_covered
                    ? "bg-primary border-primary"
                    : "border-input"
                  }`}
              >
                {candidate.form_data?.price_list_accommodation_covered && (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-4 w-4 text-white"
                  >
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                )}
              </div>
            )}
            <Label htmlFor="accommodation">Διαμονή</Label>
          </div>
        </div>
      </Card>

      {/* Availability */}
      <Card className="p-6" variant="accent">
        <div className="flex items-center gap-2 mb-4">
          <MapPin className="h-5 w-5 text-primary" />
          <h3 className="text-lg font-medium text-primary">
            Διαθεσιμότητα και ευελιξία για:
          </h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Holidays */}
          <div className="flex items-center space-x-2">
            {editMode ? (
              <Checkbox
                id="holidays"
                checked={
                  editedCandidate?.form_data?.price_list_holidays_available ||
                  false
                }
                onCheckedChange={(checked) =>
                  handleInputChange(
                    "form_data.price_list_holidays_available",
                    checked === true
                  )
                }
              />
            ) : (
              <div
                className={`h-4 w-4 rounded-sm border ${candidate.form_data?.price_list_holidays_available
                    ? "bg-primary border-primary"
                    : "border-input"
                  }`}
              >
                {candidate.form_data?.price_list_holidays_available && (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-4 w-4 text-white"
                  >
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                )}
              </div>
            )}
            <Label htmlFor="holidays">Διακοπές</Label>
          </div>

          {/* Travel */}
          <div className="flex items-center space-x-2">
            {editMode ? (
              <Checkbox
                id="travel"
                checked={
                  editedCandidate?.form_data?.price_list_travel_available ||
                  false
                }
                onCheckedChange={(checked) =>
                  handleInputChange(
                    "form_data.price_list_travel_available",
                    checked === true
                  )
                }
              />
            ) : (
              <div
                className={`h-4 w-4 rounded-sm border ${candidate.form_data?.price_list_travel_available
                    ? "bg-primary border-primary"
                    : "border-input"
                  }`}
              >
                {candidate.form_data?.price_list_travel_available && (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-4 w-4 text-white"
                  >
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                )}
              </div>
            )}
            <Label htmlFor="travel">Ταξίδια</Label>
          </div>

          {/* Weekends */}
          <div className="flex items-center space-x-2">
            {editMode ? (
              <Checkbox
                id="weekends"
                checked={
                  editedCandidate?.form_data?.price_list_weekends_available ||
                  false
                }
                onCheckedChange={(checked) =>
                  handleInputChange(
                    "form_data.price_list_weekends_available",
                    checked === true
                  )
                }
              />
            ) : (
              <div
                className={`h-4 w-4 rounded-sm border ${candidate.form_data?.price_list_weekends_available
                    ? "bg-primary border-primary"
                    : "border-input"
                  }`}
              >
                {candidate.form_data?.price_list_weekends_available && (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-4 w-4 text-white"
                  >
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                )}
              </div>
            )}
            <Label htmlFor="weekends">Σαββατοκύριακα</Label>
          </div>

          {/* Overnight */}
          <div className="flex items-center space-x-2">
            {editMode ? (
              <Checkbox
                id="overnight"
                checked={
                  editedCandidate?.form_data?.price_list_overnight_available ||
                  false
                }
                onCheckedChange={(checked) =>
                  handleInputChange(
                    "form_data.price_list_overnight_available",
                    checked === true
                  )
                }
              />
            ) : (
              <div
                className={`h-4 w-4 rounded-sm border ${candidate.form_data?.price_list_overnight_available
                    ? "bg-primary border-primary"
                    : "border-input"
                  }`}
              >
                {candidate.form_data?.price_list_overnight_available && (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-4 w-4 text-white"
                  >
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                )}
              </div>
            )}
            <Label htmlFor="overnight">Διανυκτερεύσεις</Label>
          </div>
        </div>
      </Card>

      {/* Driver's License and Car */}
      <Card className="p-6" variant="accent">
        <div className="flex items-center gap-2 mb-4">
          <Car className="h-5 w-5 text-primary" />
          <h3 className="text-lg font-medium text-primary">Οδήγηση</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Driver's License */}
          <div className="space-y-2">
            <Label htmlFor="drivers-license">Δίπλωμα</Label>
            {editMode ? (
              <Input
                id="drivers-license"
                value={
                  editedCandidate?.form_data?.price_list_drivers_license || ""
                }
                onChange={(e) =>
                  handleInputChange(
                    "form_data.price_list_drivers_license",
                    e.target.value
                  )
                }
                placeholder="π.χ. Ναι, από το 2015"
              />
            ) : (
              <p className="text-sm p-2 bg-muted/20 rounded-md">
                {candidate.form_data?.price_list_drivers_license || "-"}
              </p>
            )}
          </div>

          {/* Car */}
          <div className="space-y-2">
            <Label htmlFor="car">Αυτοκίνητο</Label>
            {editMode ? (
              <Input
                id="car"
                value={editedCandidate?.form_data?.price_list_car || ""}
                onChange={(e) =>
                  handleInputChange("form_data.price_list_car", e.target.value)
                }
                placeholder="π.χ. Ναι, ιδιόκτητο"
              />
            ) : (
              <p className="text-sm p-2 bg-muted/20 rounded-md">
                {candidate.form_data?.price_list_car || "-"}
              </p>
            )}
          </div>
        </div>
      </Card>

      {/* Extra Notes */}
      <Card className="p-6" variant="accent">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Plus className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-medium text-primary">
              Έξτρα σημειώσεις
            </h3>
          </div>
          {editMode && (
            <Button variant="outline" size="sm" onClick={addNote}>
              <Plus className="h-4 w-4 mr-1" />
              Προσθήκη
            </Button>
          )}
        </div>

        <div className="space-y-4">
          {editMode ? (
            (editedCandidate?.form_data?.price_list_notes || []).length > 0 ? (
              (editedCandidate?.form_data?.price_list_notes || []).map(
                (note, index) => (
                  <div key={index} className="flex gap-2">
                    <Textarea
                      value={note}
                      onChange={(e) => updateNote(index, e.target.value)}
                      placeholder="Προσθέστε μια σημείωση..."
                      className="min-h-[80px] flex-grow"
                    />
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => removeNote(index)}
                      className="h-10 w-10 text-destructive hover:text-destructive/80 self-start"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                )
              )
            ) : (
              <p className="text-sm text-muted-foreground">
                Δεν υπάρχουν σημειώσεις. Πατήστε "Προσθήκη" για να προσθέσετε
                μια σημείωση.
              </p>
            )
          ) : (candidate.form_data?.price_list_notes || []).length > 0 ? (
            (candidate.form_data?.price_list_notes || []).map((note, index) => (
              <div key={index} className="bg-muted/20 p-4 rounded-lg">
                <p className="whitespace-pre-line">{note}</p>
              </div>
            ))
          ) : (
            <p className="text-sm text-muted-foreground">
              Δεν υπάρχουν σημειώσεις.
            </p>
          )}
        </div>
      </Card>
    </div>
  );
};

export default PriceListTab;
