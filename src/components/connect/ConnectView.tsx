import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { usePermissionCheck } from "@/hooks/usePermissionCheck";
import {
  getClientStatusLabel,
  getClientStatusVariant,
} from "@/lib/clientUtils";
import { PaginatedResponse, PaginationParams } from "@/lib/paginationUtils";
import { cities, languages } from "@/lib/staticData";
import { getLanguageLabel, getPositionLabel } from "@/lib/tableUtils";
import {
  durationOptions,
  positionOptions,
  startDateOptions,
} from "@/schemas/FormSchema";
import {
  Candidate,
  fetchNannies,
  fetchTutors,
} from "@/services/candidateService";
import { fetchConnectClients } from "@/services/clientService";
import {
  ArrowDown,
  ArrowLeftRight,
  Award,
  Briefcase,
  Calendar,
  Clock,
  ExternalLink,
  Globe,
  MapPin,
  RefreshCw,
  Timer,
  User,
  Users,
} from "lucide-react";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import BasicTable, { ColumnConfig, ContextMenuItem } from "../crm/BasicTable";
import { Client } from "../crm/clients/ClientsTable";
import CreateDealDialog from "../crm/forms/CreateDealDialog";
import { SearchableCitySelect } from "../ui/SearchableCitySelect";

const ConnectView = () => {
  const navigate = useNavigate();
  const { canCreate } = usePermissionCheck();
  const [clientPagination, setClientPagination] = useState<PaginationParams>({
    page: 1,
    itemsPerPage: 5,
    filters: {},
  });
  const [nannyPagination, setNannyPagination] = useState<PaginationParams>({
    page: 1,
    itemsPerPage: 5,
    filters: {},
  });

  const [filters, setFilters] = useState({
    type: "",
    "form_data.level": "",
    "form_data.duration_interests": "",
    "form_data.schedule_interests": "",
    "form_data.position_interests": "",
    "form_data.start_availability": "",
    "form_data.languages": "",
    "form_data.city": "",
  });

  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [selectedNanny, setSelectedNanny] = useState<Candidate | null>(null);

  const [searchClients, setSearchClients] = useState("");
  const [searchNannies, setSearchNannies] = useState("");

  const [clients, setClients] = useState<Client[] | PaginatedResponse<Client>>(
    []
  );
  const [nannies, setNannies] = useState<
    Candidate[] | PaginatedResponse<Candidate>
  >([]);
  const [tutors, setTutors] = useState<
    Candidate[] | PaginatedResponse<Candidate>
  >([]);

  const [activeTab, setActiveTab] = useState<"nannies" | "tutors">("nannies");

  const [loadingClients, setLoadingClients] = useState(false);
  const [loadingNannies, setLoadingNannies] = useState(false);
  const [loadingTutors, setLoadingTutors] = useState(false);

  const [isDealDialogOpen, setIsDealDialogOpen] = useState(false);
  const canCreateDeals = canCreate("deals");
  const clientContextMenuItems: ContextMenuItem[] = [
    {
      label: "Προφίλ Πελάτη",
      icon: <User className="h-4 w-4" />,
      onClick: (client: Client) => navigate(`/crm/client/${client.id}`),
    },
    {
      label: "Νέο παράθυρο",
      icon: <ExternalLink className="h-4 w-4" />,
      onClick: (client: Client) => {
        window.open(`/crm/client/${client.id}`, "_blank");
      },
    },
  ];

  const nannyContextMenuItems: ContextMenuItem[] = [
    {
      label: "Προφίλ " + (activeTab === "nannies" ? "Nanny" : "Tutor"),
      icon: <User className="h-4 w-4" />,
      onClick: (nanny: Candidate) => navigate(`/nannies/${nanny.id}`),
    },
    {
      label: "Νέο παράθυρο",
      icon: <ExternalLink className="h-4 w-4" />,
      onClick: (nanny: Candidate) => {
        window.open(`/nannies/${nanny.id}`, "_blank");
      },
    },
  ];

  const clientPaginationRef = useRef(clientPagination);
  const nannyPaginationRef = useRef(nannyPagination);

  const firstMountClientRef = useRef(true);
  const firstMountNannyRef = useRef(true);
  const isResettingRef = useRef<boolean>(false);

  useEffect(() => {
    clientPaginationRef.current = clientPagination;
  }, [clientPagination]);

  useEffect(() => {
    nannyPaginationRef.current = nannyPagination;
  }, [nannyPagination]);

  const debounceClientTimerRef = useRef<NodeJS.Timeout | null>(null);
  const debounceNannyTimerRef = useRef<NodeJS.Timeout | null>(null);

  const loadClientData = useCallback(
    async (
      forceRefresh = false,
      paginationParams = clientPaginationRef.current
    ) => {
      try {
        setLoadingClients(true);
        const clientsData = await fetchConnectClients(
          forceRefresh,
          paginationParams
        );
        setClients(clientsData);
      } catch (error) {
        console.error("Error loading clients:", error);
      } finally {
        setLoadingClients(false);
      }
    },
    []
  );

  const loadActiveTabData = useCallback(
    async (
      forceRefresh = false,
      paginationParams = nannyPaginationRef.current
    ) => {
      try {
        if (activeTab === "nannies") {
          setLoadingNannies(true);
          const nanniesData = await fetchNannies(
            forceRefresh,
            paginationParams
          );
          setNannies(nanniesData);
        } else {
          setLoadingTutors(true);
          const tutorsData = await fetchTutors(forceRefresh, paginationParams);
          setTutors(tutorsData);
        }
      } catch (error) {
        console.error("Error loading nannies/tutors:", error);
      } finally {
        if (activeTab === "nannies") {
          setLoadingNannies(false);
        } else {
          setLoadingTutors(false);
        }
      }
    },
    [activeTab]
  );

  const debouncedClientDataFetch = useCallback(
    (updatedPagination: PaginationParams) => {
      if (isResettingRef.current) return;
      if (debounceClientTimerRef.current) {
        clearTimeout(debounceClientTimerRef.current);
      }
      debounceClientTimerRef.current = setTimeout(() => {
        loadClientData(true, updatedPagination);
        debounceClientTimerRef.current = null;
      }, 50);
    },
    [loadClientData]
  );

  const debouncedNannyDataFetch = useCallback(
    (updatedPagination: PaginationParams) => {
      if (isResettingRef.current) return;
      if (debounceNannyTimerRef.current) {
        clearTimeout(debounceNannyTimerRef.current);
      }
      debounceNannyTimerRef.current = setTimeout(() => {
        loadActiveTabData(true, updatedPagination);
        debounceNannyTimerRef.current = null;
      }, 50);
    },
    [loadActiveTabData]
  );

  useEffect(() => {
    loadClientData(false, clientPaginationRef.current);
    loadActiveTabData(false, nannyPaginationRef.current);

    return () => {
      if (debounceClientTimerRef.current)
        clearTimeout(debounceClientTimerRef.current);
      if (debounceNannyTimerRef.current)
        clearTimeout(debounceNannyTimerRef.current);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (firstMountClientRef.current) {
      firstMountClientRef.current = false;
      return;
    }
    if (isResettingRef.current) return;
    debouncedClientDataFetch(clientPagination);
  }, [clientPagination, debouncedClientDataFetch]);

  useEffect(() => {
    if (firstMountNannyRef.current) {
      firstMountNannyRef.current = false;
      return;
    }
    if (isResettingRef.current) return;
    debouncedNannyDataFetch(nannyPagination);
  }, [nannyPagination, debouncedNannyDataFetch]);

  const handleFilterChange = useCallback(
    (key: string, value: string) => {
      const prevFilterValue = filters[key];
      if (
        prevFilterValue === value &&
        !(key === "form_data.city" && value === "All" && prevFilterValue === "")
      ) {
        return;
      }

      const newUiFilters = {
        ...filters,
        [key]: value === "All" && key === "form_data.city" ? "" : value,
      };
      setFilters(newUiFilters);

      const baseQueryParams = Object.entries(newUiFilters).reduce(
        (acc, [filterKey, filterValue]) => {
          if (filterValue && filterValue !== "All") {
            acc[filterKey] = filterValue;
          }
          return acc;
        },
        {} as Record<string, string>
      );

      if (!isResettingRef.current) {
        const clientSearchFilter = searchClients
          ? { "form_data.client_search": searchClients }
          : {};
        setClientPagination((prev) => ({
          ...prev,
          filters: { ...baseQueryParams, ...clientSearchFilter },
          page: 1,
        }));

        const nannySearchFilter = searchNannies
          ? { "form_data.candidate_search": searchNannies }
          : {};
        setNannyPagination((prev) => ({
          ...prev,
          filters: { ...baseQueryParams, ...nannySearchFilter },
          page: 1,
        }));
      }
    },
    [filters, searchClients, searchNannies]
  );

  const handleSearchClientsChange = useCallback(
    (value: string) => {
      if (searchClients === value) return;
      setSearchClients(value);

      if (isResettingRef.current) return;

      setClientPagination((prev) => {
        const newFilters = { ...prev.filters };
        if (value) {
          newFilters["form_data.client_search"] = value;
        } else {
          delete newFilters["form_data.client_search"];
        }
        return {
          ...prev,
          filters: newFilters,
          page: 1,
        };
      });
    },
    [searchClients]
  );

  const handleSearchNanniesChange = useCallback(
    (value: string) => {
      if (searchNannies === value) return;
      setSearchNannies(value);

      if (isResettingRef.current) return;

      setNannyPagination((prev) => {
        const newFilters = { ...prev.filters };
        if (value) {
          newFilters["form_data.candidate_search"] = value;
        } else {
          delete newFilters["form_data.candidate_search"];
        }
        return {
          ...prev,
          filters: newFilters,
          page: 1,
        };
      });
    },
    [searchNannies]
  );

  const handleClientPaginationChange = useCallback(
    (tableParams: Pick<PaginationParams, "page" | "itemsPerPage">) => {
      setClientPagination((prev) => {
        if (
          tableParams.page === prev.page &&
          tableParams.itemsPerPage === prev.itemsPerPage
        ) {
          return prev;
        }
        return {
          ...prev,
          page: tableParams.page,
          itemsPerPage: tableParams.itemsPerPage,
        };
      });
    },
    []
  );

  const handleNannyPaginationChange = useCallback(
    (tableParams: Pick<PaginationParams, "page" | "itemsPerPage">) => {
      setNannyPagination((prev) => {
        if (
          tableParams.page === prev.page &&
          tableParams.itemsPerPage === prev.itemsPerPage
        ) {
          return prev;
        }
        return {
          ...prev,
          page: tableParams.page,
          itemsPerPage: tableParams.itemsPerPage,
        };
      });
    },
    []
  );

  const handleResetFilters = useCallback(() => {
    isResettingRef.current = true;

    if (debounceClientTimerRef.current)
      clearTimeout(debounceClientTimerRef.current);
    if (debounceNannyTimerRef.current)
      clearTimeout(debounceNannyTimerRef.current);
    debounceClientTimerRef.current = null;
    debounceNannyTimerRef.current = null;

    setFilters(
      Object.fromEntries(
        Object.keys(filters).map((key) => [key, ""])
      ) as typeof filters
    );
    setSearchClients("");
    setSearchNannies("");

    const resetClientPag = { page: 1, itemsPerPage: 5, filters: {} };
    setClientPagination(resetClientPag);

    const resetNannyPag = { page: 1, itemsPerPage: 5, filters: {} };
    setNannyPagination(resetNannyPag);

    setLoadingClients(true);
    if (activeTab === "nannies") setLoadingNannies(true);
    else setLoadingTutors(true);

    setTimeout(() => {
      loadClientData(true, resetClientPag);
      loadActiveTabData(true, resetNannyPag);
      isResettingRef.current = false;
    }, 50);
  }, [loadClientData, loadActiveTabData, activeTab, filters]);

  const clientColumns: ColumnConfig<Client | any>[] = [
    {
      key: "id",
      header: "Client",
      filterable: false,
      render: (client: Client) => (
        <div className="flex items-center gap-2">
          <Checkbox
            checked={selectedClient?.id == client.id}
            onCheckedChange={(checked) => {
              if (checked) {
                setSelectedClient(client);
              } else {
                setSelectedClient(null);
              }
            }}
            onClick={(e) => e.stopPropagation()}
          />
          <span>CL-{client.id}</span>
        </div>
      ),
    },
    {
      key: "form_data.father_name",
      header: "Πατέρας",
      filterable: false,
      render: (client: Client) => <span>{client.form_data.father_name}</span>,
    },
    {
      key: "status",
      header: "Status",
      filterable: false,
      render: (client: Client) => (
        <Badge variant={getClientStatusVariant(client.status)}>
          {getClientStatusLabel(client.status)}
        </Badge>
      ),
    },
    {
      key: "form_data.mother_name",
      header: "Μητέρα",
      filterable: false,
      render: (client: Client) => <span>{client.form_data?.mother_name}</span>,
    },
    {
      key: "form_data.schedule_type",
      header: "Ωράριο",
      filterable: false,
      render: (client: Client) => (
        <div className="flex flex-wrap gap-1">
          {client.form_data?.schedule_type && (
            <Badge key={client.form_data?.schedule_type} variant="outline">
              {client.form_data?.schedule_type
                .replace("_", " ")
                .split("-")
                .map((word) => word[0].toUpperCase() + word.slice(1))
                .join("-")}
            </Badge>
          )}
        </div>
      ),
    },
    {
      key: "form_data.working_level",
      header: "Επίπεδο",
      filterable: false,
      render: (client: Client) => (
        <Badge variant="primary">
          {client.form_data.working_level.slice(0, 1).toUpperCase() +
            client.form_data.working_level.slice(1)}
        </Badge>
      ),
    },
    {
      key: "form_data.languages",
      header: "Γλώσσα",
      filterable: false,
      render: (client: Client) => (
        <div className="flex flex-wrap gap-1">
          {client.form_data?.languages &&
            client.form_data?.languages?.map((lang) => (
              <Badge key={lang.language} variant="outline">
                {getLanguageLabel(lang.language)}
              </Badge>
            ))}
        </div>
      ),
    },
    {
      key: "form_data.city",
      header: "Πόλη",
      filterable: false,
      render: (client: Client) => {
        if (!client.form_data?.city) {
          return <span className="text-gray-400">-</span>;
        }
        const locationEntry = cities.find(
          (city) => city.id === client.form_data?.city
        );
        return (
          <div className="flex flex-wrap gap-1">
            <Badge variant="secondary">{locationEntry?.labelEl}</Badge>
          </div>
        );
      },
    },
    {
      key: "form_data.position_type",
      header: "Είδος",
      filterable: false,
      render: (client: Client) => (
        <div className="space-x-1">
          <Badge variant="secondary">
            {client.form_data?.position_type &&
              client.form_data?.position_type
                .replace("_", " ")
                .split("-")
                .map((word) => word[0].toUpperCase() + word.slice(1))
                .join("-")}
          </Badge>
        </div>
      ),
    },
    {
      key: "form_data.position_interests",
      header: "Θέση",
      filterable: false,
      render: (client: Client) => (
        <div className="flex flex-wrap gap-1">
          {Array.isArray(client.form_data?.position_interests) &&
            client.form_data?.position_interests.length > 0 ? (
            client.form_data?.position_interests.map((position: string) => (
              <Badge key={position} variant="outline">
                {getPositionLabel(position)}
              </Badge>
            ))
          ) : (
            <span className="text-gray-400">-</span>
          )}
        </div>
      ),
    },
    {
      key: "form_data.start_date",
      header: "Έναρξη",
      filterable: false,
      render: (client: Client) => {
        const startDateOption = startDateOptions.find(
          (option) => option.id === client.form_data?.start_date
        );
        return (
          <div className="flex flex-wrap gap-1">
            {startDateOption ? startDateOption.labelEl : "Δεν δηλώθηκε"}
          </div>
        );
      },
    },
  ];

  const nannyColumns: ColumnConfig<Candidate | any>[] = [
    {
      key: "id",
      header: activeTab === "nannies" ? "Nanny" : "Tutor",
      filterable: false,
      render: (nanny: Candidate) => (
        <div className="flex items-center gap-2">
          <Checkbox
            checked={selectedNanny?.id === nanny.id}
            onCheckedChange={(checked) => {
              if (checked) {
                setSelectedNanny(nanny);
              } else {
                setSelectedNanny(null);
              }
            }}
            onClick={(e) => e.stopPropagation()}
          />
          <span>
            {activeTab === "nannies" ? "NAN" : "TUT"}-{nanny.id}
          </span>
        </div>
      ),
    },
    {
      key: "form_data.name",
      header: "Όνομα",
      filterable: false,
      render: (nanny: Candidate) => nanny.form_data?.name || "-",
    },
    {
      key: "form_data.surname",
      header: "Επώνυμο",
      filterable: false,
      render: (nanny: Candidate) => nanny.form_data?.surname || "-",
    },

    {
      key: "form_data.languages",
      header: "Γλώσσα",
      filterable: false,
      render: (nanny: Candidate) => (
        <div className="flex flex-wrap gap-1">
          {nanny.form_data.languages &&
            nanny.form_data.languages?.map((lang) => (
              <Badge key={lang.language} variant="outline">
                {getLanguageLabel(lang.language)}
              </Badge>
            ))}
        </div>
      ),
    },
    {
      key: "form_data.city",
      header: "Πόλη",
      filterable: false,
      render: (nanny: Candidate) => {
        if (!nanny.form_data.city) {
          return <span className="text-gray-400">-</span>;
        }
        const locationEntry = cities.find(
          (city) => city.id === nanny.form_data.city
        );
        return (
          <div className="flex flex-wrap gap-1">
            <Badge variant="secondary">{locationEntry?.labelEl}</Badge>
          </div>
        );
      },
    },
    {
      key: "form_data.position_interests",
      header: "Θέση",
      filterable: false,
      render: (nanny: Candidate) => (
        <div className="flex flex-wrap gap-1">
          {nanny.form_data.position_interests.map((position) => (
            <Badge key={position} variant="outline">
              {getPositionLabel(position)}
            </Badge>
          ))}
        </div>
      ),
    },
    {
      key: "form_data.schedule_interests",
      header: "Ωράριο",
      filterable: false,
      render: (nanny: Candidate) => (
        <div className="space-x-1">
          {Array.isArray(nanny.form_data.schedule_interests) &&
            nanny.form_data.schedule_interests.length > 0 ? (
            nanny.form_data.schedule_interests.map((schedule) => (
              <Badge key={schedule} variant="outline">
                {schedule
                  .split("-")
                  .map((word) => word[0].toUpperCase() + word.slice(1))
                  .join("-")}
              </Badge>
            ))
          ) : (
            <span className="text-gray-400">-</span>
          )}
        </div>
      ),
    },
    {
      key: "form_data.start_availability",
      header: "Έναρξη",
      filterable: false,
      render: (nanny: Candidate) => {
        const startDateOption = startDateOptions.find(
          (option) => option.id === nanny.form_data.start_availability
        );
        return (
          <div className="flex flex-wrap gap-1">
            {startDateOption ? startDateOption.labelEl : "Δεν δηλώθηκε"}
          </div>
        );
      },
    },
  ];

  return (
    <div className="px-4 md:px-8 mx-auto py-4 md:py-6 space-y-4 md:space-y-6 bg-white min-h-screen  ">
      {/* Top Filters */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 md:gap-4 bg-white/80 backdrop-blur-sm p-4 rounded-lg shadow-sm">
        <div className="space-y-2">
          <label className="text-sm flex items-center gap-1">
            <Award className="h-4 w-4 text-primary" /> Επίπεδο
          </label>
          <Select
            value={filters["form_data.level"]}
            onValueChange={(value) =>
              handleFilterChange("form_data.level", value)
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Επιλογή Επιπέδου" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">Όλα</SelectItem>
              <SelectItem value="casual">Casual</SelectItem>
              <SelectItem value="basic">Basic</SelectItem>
              <SelectItem value="vip">VIP</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <label className="text-sm flex items-center gap-1">
            <Timer className="h-4 w-4 text-primary" /> Διάρκεια
          </label>
          <Select
            value={filters["form_data.duration_interests"]}
            onValueChange={(value) =>
              handleFilterChange("form_data.duration_interests", value)
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Επιλογή Διάρκειας" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">Όλα</SelectItem>
              {durationOptions.map((option) => (
                <SelectItem key={option.id} value={option.id}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <label className="text-sm flex items-center gap-1">
            <Clock className="h-4 w-4 text-primary" /> Ωράριο
          </label>
          <Select
            value={filters["form_data.schedule_interests"]}
            onValueChange={(value) =>
              handleFilterChange("form_data.schedule_interests", value)
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Επιλογή Ωραρίου" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">Όλα</SelectItem>
              <SelectItem value="part-time">Part-Time</SelectItem>
              <SelectItem value="full-time">Full-Time</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <label className="text-sm flex items-center gap-1">
            <Briefcase className="h-4 w-4 text-primary" /> Θέση
          </label>
          <Select
            value={filters["form_data.position_interests"]}
            onValueChange={(value) =>
              handleFilterChange("form_data.position_interests", value)
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Επιλογή Θέσης" />
            </SelectTrigger>
            <SelectContent className="max-h-[300px]">
              <SelectItem value="All">Όλα</SelectItem>
              {positionOptions.map((position) => (
                <SelectItem key={position.id} value={position.label}>
                  {position.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <label className="text-sm flex items-center gap-1">
            <Calendar className="h-4 w-4 text-primary" /> Διαθεσιμότητα
          </label>
          <Select
            value={filters["form_data.start_availability"]}
            onValueChange={(value) =>
              handleFilterChange(
                "form_data.start_availability",
                value === "All" ? "" : value
              )
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Επιλογή Διαθεσιμότητας" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">Όλα</SelectItem>
              {startDateOptions.map((option) => (
                <SelectItem key={option.id} value={option.labelEl}>
                  {option.labelEl}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <label className="text-sm flex items-center gap-1">
            <Globe className="h-4 w-4 text-primary" /> Γλώσσα
          </label>
          <Select
            value={filters["form_data.languages"]}
            onValueChange={(value) =>
              handleFilterChange(
                "form_data.languages",
                value === "All" ? "" : value
              )
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Επιλογή Γλώσσας" />
            </SelectTrigger>
            <SelectContent className="max-h-[300px]">
              <SelectItem value="All">Όλα</SelectItem>
              {languages.map((lang) => (
                <SelectItem key={lang.id} value={lang.labelEl}>
                  {lang.labelEl}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <label className="text-sm flex items-center gap-1">
            <MapPin className="h-4 w-4 text-primary" /> Πόλη
          </label>
          <SearchableCitySelect
            value={
              filters["form_data.city"] === "All"
                ? ""
                : filters["form_data.city"]
            }
            onValueChange={(value) =>
              handleFilterChange("form_data.city", value || "All")
            }
            placeholder="Επιλογή Πόλης"
            language="el"
          />
        </div>
        <div className="flex justify-between items-center gap-2 mt-4">
          <div className="flex-1"></div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => handleResetFilters()}
              className="flex items-center gap-1"
            >
              <RefreshCw className="w-4 h-4" /> Επαναφορά Φίλτρων
            </Button>
          </div>
        </div>
      </div>

      {/* Split View */}
      <div className="grid grid-cols-1 md:grid-cols-[minmax(0,1fr),auto,minmax(0,1fr)] gap-4 md:gap-6 w-full mt-8">
        {/* Left Side - Clients/Leads */}
        <div className="flex flex-col space-y-4">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2">
            <Input
              placeholder="Αναζήτηση σε clients/leads..."
              className="w-full sm:max-w-sm"
              value={searchClients}
              onChange={(e) => handleSearchClientsChange(e.target.value)}
            />
            <Tabs defaultValue="clients" className="w-full sm:w-auto">
              <TabsList className="w-full sm:w-auto">
                <TabsTrigger value="clients" className="flex-1 sm:flex-none">
                  Clients / Leads
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
          {useMemo(
            () => (
              <BasicTable
                data={clients}
                columns={clientColumns}
                itemsPerPageOptions={[5, 10, 20, 50, 100]}
                serverSidePagination={true}
                onPaginationChange={handleClientPaginationChange}
                onResetFilters={handleResetFilters}
                onRowClick={(client) => {
                  if (selectedClient?.id == client.id) {
                    setSelectedClient(null);
                  } else {
                    setSelectedClient(client);
                  }
                }}
                contextMenuItems={clientContextMenuItems}
                rowsLoading={loadingClients}
                selectedItems={selectedClient ? [selectedClient.id] : []}
              />
            ),
            [
              clients,
              clientColumns,
              selectedClient,
              clientContextMenuItems,
              loadingClients,
              handleClientPaginationChange,
              handleResetFilters,
            ]
          )}
        </div>

        <div className="hidden md:flex items-center justify-center">
          <div className="w-10 h-10 rounded-full bg-gradient-to-r from-primary to-secondary flex items-center justify-center text-white shadow-md">
            <ArrowLeftRight className="w-5 h-5" />
          </div>
        </div>
        <div className="flex md:hidden items-center justify-center">
          <div className="w-10 h-10 rounded-full bg-gradient-to-r from-primary to-secondary flex items-center justify-center text-white shadow-md">
            <ArrowDown className="w-5 h-5" />
          </div>
        </div>

        {/* Right Side - Nannies */}
        <div className="flex flex-col space-y-4">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2">
            <Input
              placeholder="Αναζήτηση σε nannies..."
              className="w-full sm:max-w-sm"
              value={searchNannies}
              onChange={(e) => handleSearchNanniesChange(e.target.value)}
            />
            <Tabs
              defaultValue="nannies"
              className="w-full sm:w-auto"
              value={activeTab}
              onValueChange={(value) =>
                setActiveTab(value as "nannies" | "tutors")
              }
            >
              <TabsList className="w-full sm:w-auto">
                <TabsTrigger value="nannies" className="flex-1 sm:flex-none">
                  Nannies
                </TabsTrigger>
                <TabsTrigger value="tutors" className="flex-1 sm:flex-none">
                  Tutors/Activities
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
          {activeTab === "nannies"
            ? useMemo(
              () => (
                <BasicTable
                  data={nannies}
                  columns={nannyColumns}
                  itemsPerPageOptions={[5, 10, 20, 50, 100]}
                  serverSidePagination={true}
                  onPaginationChange={handleNannyPaginationChange}
                  onResetFilters={handleResetFilters}
                  onRowClick={(nanny) => {
                    if (selectedNanny?.id == nanny.id) {
                      setSelectedNanny(null);
                    } else {
                      setSelectedNanny(nanny);
                    }
                  }}
                  contextMenuItems={nannyContextMenuItems}
                  rowsLoading={loadingNannies}
                  selectedItems={selectedNanny ? [selectedNanny.id] : []}
                />
              ),
              [
                nannies,
                nannyColumns,
                selectedNanny,
                nannyContextMenuItems,
                loadingNannies,
                handleNannyPaginationChange, // Added for completeness
                handleResetFilters,
              ]
            )
            : useMemo(
              () => (
                <BasicTable
                  data={tutors}
                  columns={nannyColumns}
                  itemsPerPageOptions={[5, 10, 20, 50, 100]}
                  serverSidePagination={true}
                  onPaginationChange={handleNannyPaginationChange}
                  onResetFilters={handleResetFilters}
                  onRowClick={(tutor) => {
                    if (selectedNanny?.id == tutor.id) {
                      setSelectedNanny(null);
                    } else {
                      setSelectedNanny(tutor);
                    }
                  }}
                  contextMenuItems={nannyContextMenuItems}
                  rowsLoading={loadingTutors}
                  selectedItems={selectedNanny ? [selectedNanny.id] : []}
                />
              ),
              [
                tutors,
                nannyColumns,
                selectedNanny,
                nannyContextMenuItems,
                loadingTutors,
                handleNannyPaginationChange, // Added for completeness
                handleResetFilters,
              ]
            )}
        </div>
      </div>
      {!canCreateDeals && (
        <div className="text-right text-sm text-muted-foreground">
          Χρειάζεστε πρόσβαση στη δημιουργία deals για να δημιουργήσετε έναν νέο
          deal.
        </div>
      )}
      {/* Bottom Action Button */}
      <div className="flex justify-end">
        <Button
          variant="default"
          className="w-full sm:w-auto hover:from-primary-hover hover:to-secondary-hover text-white shadow-md transition-all duration-200"
          disabled={
            selectedClient === null || selectedNanny === null || !canCreateDeals
          }
          onClick={() => setIsDealDialogOpen(true)}
        >
          <Users className="w-4 h-4 mr-2" /> Προσθήκη Deal
        </Button>

        {selectedClient && selectedNanny && (
          <CreateDealDialog
            open={isDealDialogOpen}
            onOpenChange={setIsDealDialogOpen}
            candidateName={`${selectedNanny.form_data.name} ${selectedNanny.form_data.surname}`}
            clientName={`${selectedClient.form_data.father_name} ${selectedClient.form_data.mother_name}`}
            clientId={selectedClient.id}
            candidateId={selectedNanny.id}
            onSuccess={() => {
              setSelectedClient(null);
              setSelectedNanny(null);
              loadActiveTabData(true);
              loadClientData(true);
            }}
          />
        )}
      </div>
    </div>
  );
};

export default ConnectView;
