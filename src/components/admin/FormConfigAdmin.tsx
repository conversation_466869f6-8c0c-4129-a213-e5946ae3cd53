import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { toast } from "@/components/ui/use-toast";
import { FormFieldConfig } from "@/schemas/FormSchema";
import {
  createFormConfig,
  deleteFormConfig,
  fetchFormConfigs,
  updateFormConfig,
} from "@/services/formConfigService";
import { Edit, Plus, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";

export function FormConfigAdmin() {
  const [configs, setConfigs] = useState<FormFieldConfig[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingConfig, setEditingConfig] = useState<FormFieldConfig | null>(null);
  const [newConfig, setNewConfig] = useState<Partial<FormFieldConfig>>({
    name: "",
    configName: "",
    fieldKey: "",
    category: "",
    titleEl: "",
    titleEn: "",
    labelEl: "",
    labelEn: "",
    placeholderEl: "",
    placeholderEn: "",
    descriptionEl: "",
    descriptionEn: "",
    fieldType: "select",
    isActive: true,
    isRequired: false,
    formType: "both",
    displayOrder: 0,
    conditionalFieldKey: "",
    conditionalValue: "",
    conditionalOperator: "equals",
    validationRules: {},
    customValidationMessageEl: "",
    customValidationMessageEn: "",
  });

  // Load form configurations
  const loadConfigs = async () => {
    setLoading(true);
    try {
      const data = await fetchFormConfigs();
      setConfigs(data);
    } catch (error) {
      console.error("Error loading form configs:", error);
      toast({
        title: "Error",
        description: "Failed to load form configurations",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadConfigs();
  }, []);

  // Handle create new configuration
  const handleCreateConfig = async () => {
    if (!newConfig.name || !newConfig.titleEl || !newConfig.titleEn || !newConfig.labelEl || !newConfig.labelEn || !newConfig.fieldKey || !newConfig.category) {
      toast({
        title: "Error",
        description: "Please fill in all required fields (name, titles, labels, field key, and category)",
        variant: "destructive",
      });
      return;
    }

    try {
      const configId = await createFormConfig(newConfig as Omit<FormFieldConfig, 'id' | 'options'>);
      if (configId) {
        await loadConfigs();
        setIsCreateDialogOpen(false);
        setNewConfig({
          name: "",
          configName: "",
          fieldKey: "",
          category: "",
          titleEl: "",
          titleEn: "",
          labelEl: "",
          labelEn: "",
          placeholderEl: "",
          placeholderEn: "",
          descriptionEl: "",
          descriptionEn: "",
          fieldType: "select",
          isActive: true,
          isRequired: false,
          formType: "both",
          displayOrder: 0,
        });
        toast({
          title: "Success",
          description: "Form configuration created successfully",
        });
      } else {
        throw new Error("Failed to create configuration");
      }
    } catch (error) {
      console.error("Error creating form config:", error);
      toast({
        title: "Error",
        description: "Failed to create form configuration",
        variant: "destructive",
      });
    }
  };

  // Handle edit configuration
  const handleEditConfig = (config: FormFieldConfig) => {
    setEditingConfig(config);
    setNewConfig({
      name: config.name,
      configName: config.configName,
      fieldKey: config.fieldKey,
      category: config.category,
      titleEl: config.titleEl,
      titleEn: config.titleEn,
      labelEl: config.labelEl,
      labelEn: config.labelEn,
      placeholderEl: config.placeholderEl,
      placeholderEn: config.placeholderEn,
      descriptionEl: config.descriptionEl,
      descriptionEn: config.descriptionEn,
      fieldType: config.fieldType,
      isActive: config.isActive,
      isRequired: config.isRequired,
      formType: config.formType,
      displayOrder: config.displayOrder,
    });
    setIsEditDialogOpen(true);
  };

  // Handle update configuration
  const handleUpdateConfig = async () => {
    if (!editingConfig || !newConfig.name || !newConfig.titleEl || !newConfig.titleEn || !newConfig.labelEl || !newConfig.labelEn || !newConfig.fieldKey || !newConfig.category) {
      toast({
        title: "Error",
        description: "Please fill in all required fields (name, titles, labels, field key, and category)",
        variant: "destructive",
      });
      return;
    }

    try {
      const success = await updateFormConfig({
        id: editingConfig.id,
        name: newConfig.name!,
        configName: newConfig.configName || newConfig.name!,
        fieldKey: newConfig.fieldKey!,
        category: newConfig.category!,
        titleEl: newConfig.titleEl!,
        titleEn: newConfig.titleEn!,
        labelEl: newConfig.labelEl!,
        labelEn: newConfig.labelEn!,
        placeholderEl: newConfig.placeholderEl,
        placeholderEn: newConfig.placeholderEn,
        descriptionEl: newConfig.descriptionEl,
        descriptionEn: newConfig.descriptionEn,
        fieldType: newConfig.fieldType!,
        isActive: newConfig.isActive!,
        isRequired: newConfig.isRequired!,
        formType: newConfig.formType!,
        displayOrder: newConfig.displayOrder,
      });

      if (success) {
        await loadConfigs();
        setIsEditDialogOpen(false);
        setEditingConfig(null);
        setNewConfig({
          name: "",
          configName: "",
          fieldKey: "",
          category: "",
          titleEl: "",
          titleEn: "",
          labelEl: "",
          labelEn: "",
          placeholderEl: "",
          placeholderEn: "",
          descriptionEl: "",
          descriptionEn: "",
          fieldType: "select",
          isActive: true,
          isRequired: false,
          formType: "both",
          displayOrder: 0,
        });
        toast({
          title: "Success",
          description: "Form configuration updated successfully",
        });
      } else {
        throw new Error("Failed to update configuration");
      }
    } catch (error) {
      console.error("Error updating form config:", error);
      toast({
        title: "Error",
        description: "Failed to update form configuration",
        variant: "destructive",
      });
    }
  };

  // Handle delete configuration
  const handleDeleteConfig = async (configId: string) => {
    if (!confirm("Are you sure you want to delete this configuration?")) {
      return;
    }

    try {
      const success = await deleteFormConfig(configId);
      if (success) {
        await loadConfigs();
        toast({
          title: "Success",
          description: "Form configuration deleted successfully",
        });
      } else {
        throw new Error("Failed to delete configuration");
      }
    } catch (error) {
      console.error("Error deleting form config:", error);
      toast({
        title: "Error",
        description: "Failed to delete form configuration",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-lg">Loading form configurations...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Form Configuration Management</h2>
          <p className="text-muted-foreground">
            Manage form field options, their order, and text content
          </p>
        </div>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Add Configuration
        </Button>
      </div>

      <div className="grid gap-4">
        {configs.map((config) => (
          <Card key={config.id}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-lg">{config.name}</CardTitle>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleEditConfig(config)}
                >
                  <Edit className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDeleteConfig(config.id)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <strong>Field Key:</strong> {config.fieldKey}
                </div>
                <div>
                  <strong>Category:</strong> {config.category}
                </div>
                <div>
                  <strong>Field Type:</strong> {config.fieldType}
                </div>
                <div>
                  <strong>Greek Title:</strong> {config.titleEl}
                </div>
                <div>
                  <strong>English Title:</strong> {config.titleEn}
                </div>
                <div>
                  <strong>Form Type:</strong> {config.formType}
                </div>
                <div>
                  <strong>Required:</strong> {config.isRequired ? "Yes" : "No"}
                </div>
                <div>
                  <strong>Display Order:</strong> {config.displayOrder || 0}
                </div>
                <div>
                  <strong>Options Count:</strong> {config.options.length}
                </div>
              </div>

              {/* Labels Section */}
              <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
                <div>
                  <strong>Greek Label:</strong> {config.labelEl}
                </div>
                <div>
                  <strong>English Label:</strong> {config.labelEn}
                </div>
              </div>
              {config.options.length > 0 && (
                <div className="mt-4">
                  <strong>Options:</strong>
                  <div className="mt-2 flex flex-wrap gap-2">
                    {config.options.slice(0, 5).map((option) => (
                      <span
                        key={option.id}
                        className="px-2 py-1 bg-secondary text-secondary-foreground rounded text-xs"
                      >
                        {option.labelEn}
                      </span>
                    ))}
                    {config.options.length > 5 && (
                      <span className="px-2 py-1 bg-muted text-muted-foreground rounded text-xs">
                        +{config.options.length - 5} more
                      </span>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Create Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create Form Configuration</DialogTitle>
            <DialogDescription>
              Add a new form field configuration with its properties.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4 max-h-96 overflow-y-auto">
            {/* Basic Information */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Configuration Name *</Label>
                <Input
                  id="name"
                  value={newConfig.name || ""}
                  onChange={(e) => setNewConfig({ ...newConfig, name: e.target.value, configName: e.target.value })}
                  placeholder="e.g., musicalInstruments"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="fieldKey">Field Key *</Label>
                <Input
                  id="fieldKey"
                  value={newConfig.fieldKey || ""}
                  onChange={(e) => setNewConfig({ ...newConfig, fieldKey: e.target.value })}
                  placeholder="e.g., musicalInstrument"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="category">Category *</Label>
                <Input
                  id="category"
                  value={newConfig.category || ""}
                  onChange={(e) => setNewConfig({ ...newConfig, category: e.target.value })}
                  placeholder="e.g., skills, preferences, personalInfo"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="fieldType">Field Type *</Label>
                <Select
                  value={newConfig.fieldType}
                  onValueChange={(value: any) => setNewConfig({ ...newConfig, fieldType: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="text">Text</SelectItem>
                    <SelectItem value="email">Email</SelectItem>
                    <SelectItem value="tel">Phone</SelectItem>
                    <SelectItem value="number">Number</SelectItem>
                    <SelectItem value="textarea">Textarea</SelectItem>
                    <SelectItem value="select">Select</SelectItem>
                    <SelectItem value="multiselect">Multi Select</SelectItem>
                    <SelectItem value="checkbox">Checkbox</SelectItem>
                    <SelectItem value="radio">Radio</SelectItem>
                    <SelectItem value="switch">Switch</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Titles */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="titleEl">Greek Title *</Label>
                <Input
                  id="titleEl"
                  value={newConfig.titleEl || ""}
                  onChange={(e) => setNewConfig({ ...newConfig, titleEl: e.target.value })}
                  placeholder="Μουσικά Όργανα"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="titleEn">English Title *</Label>
                <Input
                  id="titleEn"
                  value={newConfig.titleEn || ""}
                  onChange={(e) => setNewConfig({ ...newConfig, titleEn: e.target.value })}
                  placeholder="Musical Instruments"
                />
              </div>
            </div>

            {/* Labels */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="labelEl">Greek Label *</Label>
                <Input
                  id="labelEl"
                  value={newConfig.labelEl || ""}
                  onChange={(e) => setNewConfig({ ...newConfig, labelEl: e.target.value })}
                  placeholder="Επιλέξτε μουσικό όργανο"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="labelEn">English Label *</Label>
                <Input
                  id="labelEn"
                  value={newConfig.labelEn || ""}
                  onChange={(e) => setNewConfig({ ...newConfig, labelEn: e.target.value })}
                  placeholder="Select musical instrument"
                />
              </div>
            </div>

            {/* Placeholders */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="placeholderEl">Greek Placeholder</Label>
                <Input
                  id="placeholderEl"
                  value={newConfig.placeholderEl || ""}
                  onChange={(e) => setNewConfig({ ...newConfig, placeholderEl: e.target.value })}
                  placeholder="Επιλέξτε..."
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="placeholderEn">English Placeholder</Label>
                <Input
                  id="placeholderEn"
                  value={newConfig.placeholderEn || ""}
                  onChange={(e) => setNewConfig({ ...newConfig, placeholderEn: e.target.value })}
                  placeholder="Select..."
                />
              </div>
            </div>

            {/* Form Settings */}
            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="formType">Form Type</Label>
                <Select
                  value={newConfig.formType}
                  onValueChange={(value: any) => setNewConfig({ ...newConfig, formType: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="candidate">Candidate</SelectItem>
                    <SelectItem value="nannyRequest">Nanny Request</SelectItem>
                    <SelectItem value="both">Both</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="displayOrder">Display Order</Label>
                <Input
                  id="displayOrder"
                  type="number"
                  value={newConfig.displayOrder || 0}
                  onChange={(e) => setNewConfig({ ...newConfig, displayOrder: parseInt(e.target.value) || 0 })}
                  placeholder="0"
                />
              </div>
              <div className="space-y-2 flex items-end">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="isRequired"
                    checked={newConfig.isRequired}
                    onCheckedChange={(checked) => setNewConfig({ ...newConfig, isRequired: checked })}
                  />
                  <Label htmlFor="isRequired">Required</Label>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateConfig}>Create</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Form Configuration</DialogTitle>
            <DialogDescription>
              Update the form field configuration properties.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4 max-h-96 overflow-y-auto">
            {/* Basic Information */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">Configuration Name *</Label>
                <Input
                  id="edit-name"
                  value={newConfig.name || ""}
                  onChange={(e) => setNewConfig({ ...newConfig, name: e.target.value, configName: e.target.value })}
                  placeholder="e.g., musicalInstruments"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-fieldKey">Field Key *</Label>
                <Input
                  id="edit-fieldKey"
                  value={newConfig.fieldKey || ""}
                  onChange={(e) => setNewConfig({ ...newConfig, fieldKey: e.target.value })}
                  placeholder="e.g., musicalInstrument"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-category">Category *</Label>
                <Input
                  id="edit-category"
                  value={newConfig.category || ""}
                  onChange={(e) => setNewConfig({ ...newConfig, category: e.target.value })}
                  placeholder="e.g., skills, preferences, personalInfo"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-fieldType">Field Type *</Label>
                <Select
                  value={newConfig.fieldType}
                  onValueChange={(value: any) => setNewConfig({ ...newConfig, fieldType: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="text">Text</SelectItem>
                    <SelectItem value="email">Email</SelectItem>
                    <SelectItem value="tel">Phone</SelectItem>
                    <SelectItem value="number">Number</SelectItem>
                    <SelectItem value="textarea">Textarea</SelectItem>
                    <SelectItem value="select">Select</SelectItem>
                    <SelectItem value="multiselect">Multi Select</SelectItem>
                    <SelectItem value="checkbox">Checkbox</SelectItem>
                    <SelectItem value="radio">Radio</SelectItem>
                    <SelectItem value="switch">Switch</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Titles */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-titleEl">Greek Title *</Label>
                <Input
                  id="edit-titleEl"
                  value={newConfig.titleEl || ""}
                  onChange={(e) => setNewConfig({ ...newConfig, titleEl: e.target.value })}
                  placeholder="Μουσικά Όργανα"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-titleEn">English Title *</Label>
                <Input
                  id="edit-titleEn"
                  value={newConfig.titleEn || ""}
                  onChange={(e) => setNewConfig({ ...newConfig, titleEn: e.target.value })}
                  placeholder="Musical Instruments"
                />
              </div>
            </div>

            {/* Labels */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-labelEl">Greek Label *</Label>
                <Input
                  id="edit-labelEl"
                  value={newConfig.labelEl || ""}
                  onChange={(e) => setNewConfig({ ...newConfig, labelEl: e.target.value })}
                  placeholder="Επιλέξτε μουσικό όργανο"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-labelEn">English Label *</Label>
                <Input
                  id="edit-labelEn"
                  value={newConfig.labelEn || ""}
                  onChange={(e) => setNewConfig({ ...newConfig, labelEn: e.target.value })}
                  placeholder="Select musical instrument"
                />
              </div>
            </div>

            {/* Placeholders */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-placeholderEl">Greek Placeholder</Label>
                <Input
                  id="edit-placeholderEl"
                  value={newConfig.placeholderEl || ""}
                  onChange={(e) => setNewConfig({ ...newConfig, placeholderEl: e.target.value })}
                  placeholder="Επιλέξτε..."
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-placeholderEn">English Placeholder</Label>
                <Input
                  id="edit-placeholderEn"
                  value={newConfig.placeholderEn || ""}
                  onChange={(e) => setNewConfig({ ...newConfig, placeholderEn: e.target.value })}
                  placeholder="Select..."
                />
              </div>
            </div>

            {/* Form Settings */}
            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-formType">Form Type</Label>
                <Select
                  value={newConfig.formType}
                  onValueChange={(value: any) => setNewConfig({ ...newConfig, formType: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="candidate">Candidate</SelectItem>
                    <SelectItem value="nannyRequest">Nanny Request</SelectItem>
                    <SelectItem value="both">Both</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-displayOrder">Display Order</Label>
                <Input
                  id="edit-displayOrder"
                  type="number"
                  value={newConfig.displayOrder || 0}
                  onChange={(e) => setNewConfig({ ...newConfig, displayOrder: parseInt(e.target.value) || 0 })}
                  placeholder="0"
                />
              </div>
              <div className="space-y-2 flex items-end">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="edit-isRequired"
                    checked={newConfig.isRequired}
                    onCheckedChange={(checked) => setNewConfig({ ...newConfig, isRequired: checked })}
                  />
                  <Label htmlFor="edit-isRequired">Required</Label>
                </div>
              </div>
            </div>

            {/* Status */}
            <div className="flex items-center space-x-2">
              <Switch
                id="edit-isActive"
                checked={newConfig.isActive}
                onCheckedChange={(checked) => setNewConfig({ ...newConfig, isActive: checked })}
              />
              <Label htmlFor="edit-isActive">Active</Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateConfig}>Update</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
