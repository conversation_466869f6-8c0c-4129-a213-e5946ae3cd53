import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/components/ui/use-toast";
import { FormLabelCategory, FormLabelConfig } from "@/schemas/FormSchema";
import {
  bulkUpdateFormLabels,
  createFormLabel,
  deleteFormLabel,
  fetchFormLabelCategories,
  updateFormLabel,
} from "@/services/formLabelService";
import { Edit, Languages, Plus, Save, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";

export function FormLabelsAdmin() {
  const [categories, setCategories] = useState<FormLabelCategory[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [loading, setLoading] = useState(true);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingLabel, setEditingLabel] = useState<FormLabelConfig | null>(null);
  const [newLabel, setNewLabel] = useState<Partial<FormLabelConfig>>({
    key: "",
    category: "",
    context: "label",
    labelEl: "",
    labelEn: "",
    isActive: true,
    formType: "both",
  });

  // Load form label categories
  const loadCategories = async () => {
    setLoading(true);
    try {
      const data = await fetchFormLabelCategories();
      setCategories(data);
      if (data.length > 0 && !selectedCategory) {
        setSelectedCategory(data[0].name);
      }
    } catch (error) {
      console.error("Error loading form label categories:", error);
      toast({
        title: "Error",
        description: "Failed to load form label categories",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadCategories();
  }, []);

  // Get current category data
  const currentCategory = categories.find(cat => cat.name === selectedCategory);
  const currentLabels = currentCategory?.labels || [];

  // Handle create new label
  const handleCreateLabel = async () => {
    if (!newLabel.key || !newLabel.labelEl || !newLabel.labelEn) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    try {
      const labelId = await createFormLabel({
        key: newLabel.key!,
        category: selectedCategory,
        context: newLabel.context!,
        labelEl: newLabel.labelEl!,
        labelEn: newLabel.labelEn!,
        isActive: newLabel.isActive!,
        formType: newLabel.formType,
      });

      if (labelId) {
        await loadCategories();
        setIsCreateDialogOpen(false);
        resetNewLabel();
        toast({
          title: "Success",
          description: "Form label created successfully",
        });
      } else {
        throw new Error("Failed to create label");
      }
    } catch (error) {
      console.error("Error creating form label:", error);
      toast({
        title: "Error",
        description: "Failed to create form label",
        variant: "destructive",
      });
    }
  };

  // Handle edit label
  const handleEditLabel = (label: FormLabelConfig) => {
    setEditingLabel(label);
    setNewLabel({
      key: label.key,
      category: label.category,
      context: label.context,
      labelEl: label.labelEl,
      labelEn: label.labelEn,
      isActive: label.isActive,
      formType: label.formType,
    });
    setIsEditDialogOpen(true);
  };

  // Handle update label
  const handleUpdateLabel = async () => {
    if (!editingLabel || !newLabel.key || !newLabel.labelEl || !newLabel.labelEn) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    try {
      const success = await updateFormLabel({
        id: editingLabel.id,
        key: newLabel.key!,
        category: newLabel.category!,
        context: newLabel.context!,
        labelEl: newLabel.labelEl!,
        labelEn: newLabel.labelEn!,
        isActive: newLabel.isActive!,
        formType: newLabel.formType,
      });

      if (success) {
        await loadCategories();
        setIsEditDialogOpen(false);
        setEditingLabel(null);
        resetNewLabel();
        toast({
          title: "Success",
          description: "Form label updated successfully",
        });
      } else {
        throw new Error("Failed to update label");
      }
    } catch (error) {
      console.error("Error updating form label:", error);
      toast({
        title: "Error",
        description: "Failed to update form label",
        variant: "destructive",
      });
    }
  };

  // Handle delete label
  const handleDeleteLabel = async (labelId: string) => {
    if (!confirm("Are you sure you want to delete this label?")) {
      return;
    }

    try {
      const success = await deleteFormLabel(labelId);
      if (success) {
        await loadCategories();
        toast({
          title: "Success",
          description: "Form label deleted successfully",
        });
      } else {
        throw new Error("Failed to delete label");
      }
    } catch (error) {
      console.error("Error deleting form label:", error);
      toast({
        title: "Error",
        description: "Failed to delete form label",
        variant: "destructive",
      });
    }
  };

  // Handle bulk save
  const handleBulkSave = async () => {
    try {
      const success = await bulkUpdateFormLabels(currentLabels);
      if (success) {
        await loadCategories();
        toast({
          title: "Success",
          description: "All labels saved successfully",
        });
      } else {
        throw new Error("Failed to save labels");
      }
    } catch (error) {
      console.error("Error saving labels:", error);
      toast({
        title: "Error",
        description: "Failed to save labels",
        variant: "destructive",
      });
    }
  };

  // Reset new label form
  const resetNewLabel = () => {
    setNewLabel({
      key: "",
      category: selectedCategory,
      context: "label",
      labelEl: "",
      labelEn: "",
      isActive: true,
      formType: "both",
    });
  };

  // Update label in current list
  const updateLabelInList = (labelId: string, updates: Partial<FormLabelConfig>) => {
    setCategories(prevCategories =>
      prevCategories.map(category =>
        category.name === selectedCategory
          ? {
              ...category,
              labels: category.labels.map(label =>
                label.id === labelId ? { ...label, ...updates } : label
              ),
            }
          : category
      )
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-lg">Loading form labels...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Form Labels Management</h2>
          <p className="text-muted-foreground">
            Manage form labels, placeholders, and text content in multiple languages
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={handleBulkSave} variant="outline">
            <Save className="h-4 w-4 mr-2" />
            Save All Changes
          </Button>
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Label
          </Button>
        </div>
      </div>

      {/* Category Tabs */}
      <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
        <TabsList className="grid w-full grid-cols-5">
          {categories.slice(0, 5).map((category) => (
            <TabsTrigger key={category.name} value={category.name}>
              {category.titleEn}
            </TabsTrigger>
          ))}
        </TabsList>

        {categories.map((category) => (
          <TabsContent key={category.name} value={category.name} className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Languages className="h-5 w-5" />
                  {category.titleEn} ({category.labels.length} labels)
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {category.labels.map((label) => (
                    <Card key={label.id} className="p-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <Label className="text-sm font-medium">Key</Label>
                          <div className="text-sm text-muted-foreground">{label.key}</div>
                          <div className="text-xs text-muted-foreground mt-1">
                            Context: {label.context} | Type: {label.formType || 'both'}
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <Label className="text-sm font-medium">Greek Text</Label>
                          <Textarea
                            value={label.labelEl}
                            onChange={(e) =>
                              updateLabelInList(label.id, { labelEl: e.target.value })
                            }
                            className="min-h-[60px]"
                            placeholder="Greek text..."
                          />
                        </div>
                        
                        <div className="space-y-2">
                          <Label className="text-sm font-medium">English Text</Label>
                          <Textarea
                            value={label.labelEn}
                            onChange={(e) =>
                              updateLabelInList(label.id, { labelEn: e.target.value })
                            }
                            className="min-h-[60px]"
                            placeholder="English text..."
                          />
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between mt-4">
                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={label.isActive}
                            onCheckedChange={(checked) =>
                              updateLabelInList(label.id, { isActive: checked })
                            }
                          />
                          <Label className="text-sm">Active</Label>
                        </div>
                        
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditLabel(label)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteLabel(label.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </Card>
                  ))}
                  
                  {category.labels.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      No labels found for this category.
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        ))}
      </Tabs>

      {/* Create Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create Form Label</DialogTitle>
            <DialogDescription>
              Add a new label for {currentCategory?.titleEn}.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="key">Key *</Label>
                <Input
                  id="key"
                  value={newLabel.key || ""}
                  onChange={(e) => setNewLabel({ ...newLabel, key: e.target.value })}
                  placeholder="e.g., fatherName.label"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="context">Context *</Label>
                <Select
                  value={newLabel.context}
                  onValueChange={(value: any) => setNewLabel({ ...newLabel, context: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="label">Label</SelectItem>
                    <SelectItem value="placeholder">Placeholder</SelectItem>
                    <SelectItem value="description">Description</SelectItem>
                    <SelectItem value="title">Title</SelectItem>
                    <SelectItem value="button">Button</SelectItem>
                    <SelectItem value="message">Message</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="labelEl">Greek Text *</Label>
              <Textarea
                id="labelEl"
                value={newLabel.labelEl || ""}
                onChange={(e) => setNewLabel({ ...newLabel, labelEl: e.target.value })}
                placeholder="Greek text..."
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="labelEn">English Text *</Label>
              <Textarea
                id="labelEn"
                value={newLabel.labelEn || ""}
                onChange={(e) => setNewLabel({ ...newLabel, labelEn: e.target.value })}
                placeholder="English text..."
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="formType">Form Type</Label>
                <Select
                  value={newLabel.formType}
                  onValueChange={(value: any) => setNewLabel({ ...newLabel, formType: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="both">Both Forms</SelectItem>
                    <SelectItem value="candidate">Candidate Form</SelectItem>
                    <SelectItem value="nannyRequest">Nanny Request Form</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center space-x-2 mt-6">
                <Switch
                  id="isActive"
                  checked={newLabel.isActive}
                  onCheckedChange={(checked) => setNewLabel({ ...newLabel, isActive: checked })}
                />
                <Label htmlFor="isActive">Active</Label>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateLabel}>Create</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Dialog - Similar structure to Create Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Form Label</DialogTitle>
            <DialogDescription>
              Update the form label properties.
            </DialogDescription>
          </DialogHeader>
          {/* Similar content to create dialog */}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateLabel}>Update</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
