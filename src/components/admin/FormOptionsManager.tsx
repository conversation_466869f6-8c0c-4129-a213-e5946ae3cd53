import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { toast } from "@/components/ui/use-toast";
import { FormFieldConfig, FormFieldOption } from "@/schemas/FormSchema";
import {
  createFormOption,
  deleteFormOption,
  updateFormOption,
  updateFormOptionsOrder,
} from "@/services/formConfigService";
import { ChevronDown, ChevronUp, Edit, Plus, Trash2 } from "lucide-react";
import { useState } from "react";

interface FormOptionsManagerProps {
  config: FormFieldConfig;
  onConfigUpdate: () => void;
}

export function FormOptionsManager({ config, onConfigUpdate }: FormOptionsManagerProps) {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingOption, setEditingOption] = useState<FormFieldOption | null>(null);
  const [newOption, setNewOption] = useState<Partial<FormFieldOption>>({
    labelEl: "",
    labelEn: "",
    order: config.options.length,
    isActive: true,
  });

  // Handle create new option
  const handleCreateOption = async () => {
    if (!newOption.labelEl || !newOption.labelEn) {
      toast({
        title: "Error",
        description: "Please fill in both Greek and English labels",
        variant: "destructive",
      });
      return;
    }

    try {
      const optionId = await createFormOption({
        fieldConfigId: config.id,
        labelEl: newOption.labelEl!,
        labelEn: newOption.labelEn!,
        order: newOption.order || config.options.length,
        isActive: newOption.isActive!,
      });

      if (optionId) {
        onConfigUpdate();
        setIsCreateDialogOpen(false);
        setNewOption({
          labelEl: "",
          labelEn: "",
          order: config.options.length + 1,
          isActive: true,
        });
        toast({
          title: "Success",
          description: "Option created successfully",
        });
      } else {
        throw new Error("Failed to create option");
      }
    } catch (error) {
      console.error("Error creating option:", error);
      toast({
        title: "Error",
        description: "Failed to create option",
        variant: "destructive",
      });
    }
  };

  // Handle edit option
  const handleEditOption = (option: FormFieldOption) => {
    setEditingOption(option);
    setNewOption({
      labelEl: option.labelEl,
      labelEn: option.labelEn,
      order: option.order,
      isActive: option.isActive,
    });
    setIsEditDialogOpen(true);
  };

  // Handle update option
  const handleUpdateOption = async () => {
    if (!editingOption || !newOption.labelEl || !newOption.labelEn) {
      toast({
        title: "Error",
        description: "Please fill in both Greek and English labels",
        variant: "destructive",
      });
      return;
    }

    try {
      const success = await updateFormOption({
        id: editingOption.id,
        fieldConfigId: config.id,
        labelEl: newOption.labelEl!,
        labelEn: newOption.labelEn!,
        order: newOption.order!,
        isActive: newOption.isActive!,
      });

      if (success) {
        onConfigUpdate();
        setIsEditDialogOpen(false);
        setEditingOption(null);
        setNewOption({
          labelEl: "",
          labelEn: "",
          order: config.options.length,
          isActive: true,
        });
        toast({
          title: "Success",
          description: "Option updated successfully",
        });
      } else {
        throw new Error("Failed to update option");
      }
    } catch (error) {
      console.error("Error updating option:", error);
      toast({
        title: "Error",
        description: "Failed to update option",
        variant: "destructive",
      });
    }
  };

  // Handle delete option
  const handleDeleteOption = async (optionId: string) => {
    if (!confirm("Are you sure you want to delete this option?")) {
      return;
    }

    try {
      const success = await deleteFormOption(optionId);
      if (success) {
        onConfigUpdate();
        toast({
          title: "Success",
          description: "Option deleted successfully",
        });
      } else {
        throw new Error("Failed to delete option");
      }
    } catch (error) {
      console.error("Error deleting option:", error);
      toast({
        title: "Error",
        description: "Failed to delete option",
        variant: "destructive",
      });
    }
  };

  // Handle move option up/down
  const handleMoveOption = async (optionId: string, direction: 'up' | 'down') => {
    const currentIndex = config.options.findIndex(opt => opt.id === optionId);
    if (currentIndex === -1) return;

    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    if (newIndex < 0 || newIndex >= config.options.length) return;

    // Create new order array
    const newOptions = [...config.options];
    const [movedOption] = newOptions.splice(currentIndex, 1);
    newOptions.splice(newIndex, 0, movedOption);

    // Update order indices
    const updates = newOptions.map((option, index) => ({
      id: option.id,
      order: index,
    }));

    try {
      const success = await updateFormOptionsOrder(updates);
      if (success) {
        onConfigUpdate();
        toast({
          title: "Success",
          description: "Option order updated successfully",
        });
      } else {
        throw new Error("Failed to update option order");
      }
    } catch (error) {
      console.error("Error updating option order:", error);
      toast({
        title: "Error",
        description: "Failed to update option order",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">
          Options for {config.titleEn} ({config.options.length})
        </h3>
        <Button onClick={() => setIsCreateDialogOpen(true)} size="sm">
          <Plus className="h-4 w-4 mr-2" />
          Add Option
        </Button>
      </div>

      <div className="space-y-2">
        {config.options.map((option, index) => (
          <Card key={option.id} className="p-3">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <strong>Greek:</strong> {option.labelEl}
                  </div>
                  <div>
                    <strong>English:</strong> {option.labelEn}
                  </div>
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  Order: {option.order} | Active: {option.isActive ? "Yes" : "No"}
                </div>
              </div>
              <div className="flex items-center gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleMoveOption(option.id, 'up')}
                  disabled={index === 0}
                >
                  <ChevronUp className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleMoveOption(option.id, 'down')}
                  disabled={index === config.options.length - 1}
                >
                  <ChevronDown className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleEditOption(option)}
                >
                  <Edit className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDeleteOption(option.id)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Create Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Option</DialogTitle>
            <DialogDescription>
              Add a new option to {config.titleEn}.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="labelEl">Greek Label *</Label>
              <Input
                id="labelEl"
                value={newOption.labelEl || ""}
                onChange={(e) => setNewOption({ ...newOption, labelEl: e.target.value })}
                placeholder="Ελληνική ετικέτα"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="labelEn">English Label *</Label>
              <Input
                id="labelEn"
                value={newOption.labelEn || ""}
                onChange={(e) => setNewOption({ ...newOption, labelEn: e.target.value })}
                placeholder="English label"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="order">Order</Label>
              <Input
                id="order"
                type="number"
                value={newOption.order || 0}
                onChange={(e) => setNewOption({ ...newOption, order: parseInt(e.target.value) })}
              />
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="isActive"
                checked={newOption.isActive}
                onCheckedChange={(checked) => setNewOption({ ...newOption, isActive: checked })}
              />
              <Label htmlFor="isActive">Active</Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateOption}>Create</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Option</DialogTitle>
            <DialogDescription>
              Update the option details.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-labelEl">Greek Label *</Label>
              <Input
                id="edit-labelEl"
                value={newOption.labelEl || ""}
                onChange={(e) => setNewOption({ ...newOption, labelEl: e.target.value })}
                placeholder="Ελληνική ετικέτα"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-labelEn">English Label *</Label>
              <Input
                id="edit-labelEn"
                value={newOption.labelEn || ""}
                onChange={(e) => setNewOption({ ...newOption, labelEn: e.target.value })}
                placeholder="English label"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-order">Order</Label>
              <Input
                id="edit-order"
                type="number"
                value={newOption.order || 0}
                onChange={(e) => setNewOption({ ...newOption, order: parseInt(e.target.value) })}
              />
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="edit-isActive"
                checked={newOption.isActive}
                onCheckedChange={(checked) => setNewOption({ ...newOption, isActive: checked })}
              />
              <Label htmlFor="edit-isActive">Active</Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateOption}>Update</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
