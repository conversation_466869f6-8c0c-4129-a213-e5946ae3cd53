import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { FormThemeColors } from "@/schemas/FormSchema";
import { Palette, RotateCcw } from "lucide-react";
import { useState } from "react";

interface ColorCustomizerProps {
  colors: FormThemeColors;
  onChange: (colors: FormThemeColors) => void;
  onReset?: () => void;
}

// Predefined color options for quick selection
const colorPresets = {
  blue: {
    primary: "bg-blue-600",
    secondary: "bg-blue-500",
    accent: "bg-blue-100",
    text: "text-blue-900",
  },
  green: {
    primary: "bg-green-600",
    secondary: "bg-green-500",
    accent: "bg-green-100",
    text: "text-green-900",
  },
  purple: {
    primary: "bg-purple-600",
    secondary: "bg-purple-500",
    accent: "bg-purple-100",
    text: "text-purple-900",
  },
  orange: {
    primary: "bg-orange-600",
    secondary: "bg-orange-500",
    accent: "bg-orange-100",
    text: "text-orange-900",
  },
  pink: {
    primary: "bg-pink-600",
    secondary: "bg-pink-500",
    accent: "bg-pink-100",
    text: "text-pink-900",
  },
  indigo: {
    primary: "bg-indigo-600",
    secondary: "bg-indigo-500",
    accent: "bg-indigo-100",
    text: "text-indigo-900",
  },
};

export function ColorCustomizer({ colors, onChange, onReset }: ColorCustomizerProps) {
  const [activeTab, setActiveTab] = useState("progress");

  const updateColor = (key: keyof FormThemeColors, value: string) => {
    onChange({
      ...colors,
      [key]: value,
    });
  };

  const applyColorPreset = (presetName: keyof typeof colorPresets) => {
    const preset = colorPresets[presetName];
    onChange({
      ...colors,
      progressBarFill: `bg-gradient-to-r from-${presetName}-500 to-${presetName}-600`,
      progressBarBg: `${preset.accent}`,
      primaryButton: `${preset.primary} hover:${preset.primary.replace('600', '700')}`,
      secondaryButton: `${preset.secondary} hover:${preset.secondary.replace('500', '600')}`,
      primaryText: preset.text,
      secondaryText: preset.text.replace('900', '700'),
      accentColor: preset.accent,
      stepActiveColor: preset.primary,
      stepInactiveColor: preset.accent,
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Palette className="h-5 w-5" />
          <h3 className="text-lg font-semibold">Color Customization</h3>
        </div>
        {onReset && (
          <Button variant="outline" size="sm" onClick={onReset}>
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset
          </Button>
        )}
      </div>

      {/* Quick Color Presets */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Quick Color Presets</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-6 gap-2">
            {Object.entries(colorPresets).map(([name, preset]) => (
              <button
                key={name}
                onClick={() => applyColorPreset(name as keyof typeof colorPresets)}
                className="flex flex-col items-center p-2 rounded-lg border hover:bg-muted transition-colors"
                title={`Apply ${name} theme`}
              >
                <div className="flex gap-1 mb-1">
                  <div className={`w-3 h-3 rounded ${preset.primary}`} />
                  <div className={`w-3 h-3 rounded ${preset.secondary}`} />
                  <div className={`w-3 h-3 rounded ${preset.accent}`} />
                </div>
                <span className="text-xs capitalize">{name}</span>
              </button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Detailed Color Controls */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="progress">Progress</TabsTrigger>
          <TabsTrigger value="backgrounds">Backgrounds</TabsTrigger>
          <TabsTrigger value="buttons">Buttons</TabsTrigger>
          <TabsTrigger value="text">Text & Steps</TabsTrigger>
        </TabsList>

        <TabsContent value="progress" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Progress Bar Colors</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="progressBarBg">Progress Bar Background</Label>
                <Input
                  id="progressBarBg"
                  value={colors.progressBarBg}
                  onChange={(e) => updateColor('progressBarBg', e.target.value)}
                  placeholder="bg-primary/20"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="progressBarFill">Progress Bar Fill</Label>
                <Input
                  id="progressBarFill"
                  value={colors.progressBarFill}
                  onChange={(e) => updateColor('progressBarFill', e.target.value)}
                  placeholder="bg-gradient-to-r from-primary to-secondary"
                />
              </div>
              {/* Progress bar preview */}
              <div className="mt-4">
                <Label className="text-sm">Preview:</Label>
                <div className={`w-full h-4 rounded-full ${colors.progressBarBg} mt-2`}>
                  <div className={`h-4 rounded-full ${colors.progressBarFill} transition-all`} style={{ width: '60%' }} />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="backgrounds" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Background Colors</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="formBackground">Form Background</Label>
                <Input
                  id="formBackground"
                  value={colors.formBackground}
                  onChange={(e) => updateColor('formBackground', e.target.value)}
                  placeholder="bg-gradient-to-b from-accent/20 via-background to-accent/20"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="cardBackground">Card Background</Label>
                <Input
                  id="cardBackground"
                  value={colors.cardBackground}
                  onChange={(e) => updateColor('cardBackground', e.target.value)}
                  placeholder="bg-white"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="stepBackground">Step Background</Label>
                <Input
                  id="stepBackground"
                  value={colors.stepBackground}
                  onChange={(e) => updateColor('stepBackground', e.target.value)}
                  placeholder="bg-light/10"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="buttons" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Button Colors</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="primaryButton">Primary Button</Label>
                <Input
                  id="primaryButton"
                  value={colors.primaryButton}
                  onChange={(e) => updateColor('primaryButton', e.target.value)}
                  placeholder="bg-primary hover:bg-primary-hover"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="secondaryButton">Secondary Button</Label>
                <Input
                  id="secondaryButton"
                  value={colors.secondaryButton}
                  onChange={(e) => updateColor('secondaryButton', e.target.value)}
                  placeholder="bg-secondary hover:bg-secondary-hover"
                />
              </div>
              {/* Button preview */}
              <div className="mt-4 space-y-2">
                <Label className="text-sm">Preview:</Label>
                <div className="flex gap-2">
                  <button className={`px-4 py-2 rounded text-white ${colors.primaryButton}`}>
                    Primary
                  </button>
                  <button className={`px-4 py-2 rounded text-white ${colors.secondaryButton}`}>
                    Secondary
                  </button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="text" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Text & Step Colors</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="primaryText">Primary Text</Label>
                  <Input
                    id="primaryText"
                    value={colors.primaryText}
                    onChange={(e) => updateColor('primaryText', e.target.value)}
                    placeholder="text-primary"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="secondaryText">Secondary Text</Label>
                  <Input
                    id="secondaryText"
                    value={colors.secondaryText}
                    onChange={(e) => updateColor('secondaryText', e.target.value)}
                    placeholder="text-secondary"
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="stepActiveColor">Active Step</Label>
                  <Input
                    id="stepActiveColor"
                    value={colors.stepActiveColor}
                    onChange={(e) => updateColor('stepActiveColor', e.target.value)}
                    placeholder="bg-primary"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="stepInactiveColor">Inactive Step</Label>
                  <Input
                    id="stepInactiveColor"
                    value={colors.stepInactiveColor}
                    onChange={(e) => updateColor('stepInactiveColor', e.target.value)}
                    placeholder="bg-muted"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="stepCompletedColor">Completed Step</Label>
                  <Input
                    id="stepCompletedColor"
                    value={colors.stepCompletedColor}
                    onChange={(e) => updateColor('stepCompletedColor', e.target.value)}
                    placeholder="bg-green-500"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="successColor">Success Color</Label>
                  <Input
                    id="successColor"
                    value={colors.successColor}
                    onChange={(e) => updateColor('successColor', e.target.value)}
                    placeholder="text-green-600"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="errorColor">Error Color</Label>
                  <Input
                    id="errorColor"
                    value={colors.errorColor}
                    onChange={(e) => updateColor('errorColor', e.target.value)}
                    placeholder="text-destructive"
                  />
                </div>
              </div>

              {/* Text preview */}
              <div className="mt-4 space-y-2">
                <Label className="text-sm">Preview:</Label>
                <div className="space-y-1">
                  <p className={colors.primaryText}>Primary text example</p>
                  <p className={colors.secondaryText}>Secondary text example</p>
                  <p className={colors.successColor}>Success message</p>
                  <p className={colors.errorColor}>Error message</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Color Helper */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Color Format Helper</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-muted-foreground space-y-1">
            <p><strong>Tailwind Classes:</strong> bg-blue-500, text-red-600, hover:bg-green-700</p>
            <p><strong>Gradients:</strong> bg-gradient-to-r from-blue-500 to-purple-600</p>
            <p><strong>Opacity:</strong> bg-primary/20, text-white/80</p>
            <p><strong>Hover States:</strong> bg-primary hover:bg-primary-hover</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
