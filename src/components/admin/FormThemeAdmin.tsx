import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { toast } from "@/components/ui/use-toast";
import { FormThemeConfig } from "@/schemas/FormSchema";
import {
  createFormTheme,
  deleteFormTheme,
  fetchFormThemes,
  getThemeTemplates,
  setDefaultFormTheme,
  updateFormTheme,
} from "@/services/formThemeService";
import { Edit, Eye, Palette, Plus, Star, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";

export function FormThemeAdmin() {
  const [themes, setThemes] = useState<FormThemeConfig[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isPreviewDialogOpen, setIsPreviewDialogOpen] = useState(false);
  const [editingTheme, setEditingTheme] = useState<FormThemeConfig | null>(null);
  const [previewTheme, setPreviewTheme] = useState<FormThemeConfig | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<string>("");
  const [newTheme, setNewTheme] = useState<Partial<FormThemeConfig>>({
    name: "",
    titleEl: "",
    titleEn: "",
    isActive: true,
    isDefault: false,
    colors: getThemeTemplates()[0].colors,
  });

  // Load form themes
  const loadThemes = async () => {
    setLoading(true);
    try {
      const data = await fetchFormThemes();
      setThemes(data);
    } catch (error) {
      console.error("Error loading form themes:", error);
      toast({
        title: "Error",
        description: "Failed to load form themes",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadThemes();
  }, []);

  // Handle template selection
  const handleTemplateSelect = (templateName: string) => {
    const template = getThemeTemplates().find(t => t.name === templateName);
    if (template) {
      setNewTheme({
        ...newTheme,
        colors: template.colors,
      });
      setSelectedTemplate(templateName);
    }
  };

  // Handle create new theme
  const handleCreateTheme = async () => {
    if (!newTheme.name || !newTheme.titleEl || !newTheme.titleEn) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    try {
      const themeId = await createFormTheme(newTheme as Omit<FormThemeConfig, 'id'>);
      if (themeId) {
        await loadThemes();
        setIsCreateDialogOpen(false);
        resetNewTheme();
        toast({
          title: "Success",
          description: "Form theme created successfully",
        });
      } else {
        throw new Error("Failed to create theme");
      }
    } catch (error) {
      console.error("Error creating form theme:", error);
      toast({
        title: "Error",
        description: "Failed to create form theme",
        variant: "destructive",
      });
    }
  };

  // Handle edit theme
  const handleEditTheme = (theme: FormThemeConfig) => {
    setEditingTheme(theme);
    setNewTheme({
      name: theme.name,
      titleEl: theme.titleEl,
      titleEn: theme.titleEn,
      isActive: theme.isActive,
      isDefault: theme.isDefault,
      colors: theme.colors,
    });
    setIsEditDialogOpen(true);
  };

  // Handle update theme
  const handleUpdateTheme = async () => {
    if (!editingTheme || !newTheme.name || !newTheme.titleEl || !newTheme.titleEn) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    try {
      const success = await updateFormTheme({
        id: editingTheme.id,
        name: newTheme.name!,
        titleEl: newTheme.titleEl!,
        titleEn: newTheme.titleEn!,
        isActive: newTheme.isActive!,
        isDefault: newTheme.isDefault!,
        colors: newTheme.colors!,
      });

      if (success) {
        await loadThemes();
        setIsEditDialogOpen(false);
        setEditingTheme(null);
        resetNewTheme();
        toast({
          title: "Success",
          description: "Form theme updated successfully",
        });
      } else {
        throw new Error("Failed to update theme");
      }
    } catch (error) {
      console.error("Error updating form theme:", error);
      toast({
        title: "Error",
        description: "Failed to update form theme",
        variant: "destructive",
      });
    }
  };

  // Handle delete theme
  const handleDeleteTheme = async (themeId: string) => {
    if (!confirm("Are you sure you want to delete this theme?")) {
      return;
    }

    try {
      const success = await deleteFormTheme(themeId);
      if (success) {
        await loadThemes();
        toast({
          title: "Success",
          description: "Form theme deleted successfully",
        });
      } else {
        throw new Error("Failed to delete theme");
      }
    } catch (error) {
      console.error("Error deleting form theme:", error);
      toast({
        title: "Error",
        description: "Failed to delete form theme",
        variant: "destructive",
      });
    }
  };

  // Handle set default theme
  const handleSetDefault = async (themeId: string) => {
    try {
      const success = await setDefaultFormTheme(themeId);
      if (success) {
        await loadThemes();
        toast({
          title: "Success",
          description: "Default theme updated successfully",
        });
      } else {
        throw new Error("Failed to set default theme");
      }
    } catch (error) {
      console.error("Error setting default theme:", error);
      toast({
        title: "Error",
        description: "Failed to set default theme",
        variant: "destructive",
      });
    }
  };

  // Handle preview theme
  const handlePreviewTheme = (theme: FormThemeConfig) => {
    setPreviewTheme(theme);
    setIsPreviewDialogOpen(true);
  };

  // Reset new theme form
  const resetNewTheme = () => {
    setNewTheme({
      name: "",
      titleEl: "",
      titleEn: "",
      isActive: true,
      isDefault: false,
      colors: getThemeTemplates()[0].colors,
    });
    setSelectedTemplate("");
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-lg">Loading form themes...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Form Theme Management</h2>
          <p className="text-muted-foreground">
            Customize form colors, progress bars, and visual appearance
          </p>
        </div>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Create Theme
        </Button>
      </div>

      <div className="grid gap-4">
        {themes.map((theme) => (
          <Card key={theme.id} className={theme.isDefault ? "ring-2 ring-primary" : ""}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-lg flex items-center gap-2">
                {theme.titleEn}
                {theme.isDefault && <Star className="h-4 w-4 text-yellow-500 fill-current" />}
              </CardTitle>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePreviewTheme(theme)}
                >
                  <Eye className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleEditTheme(theme)}
                >
                  <Edit className="h-4 w-4" />
                </Button>
                {!theme.isDefault && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSetDefault(theme.id)}
                  >
                    <Star className="h-4 w-4" />
                  </Button>
                )}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDeleteTheme(theme.id)}
                  disabled={theme.isDefault}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <strong>Name:</strong> {theme.name}
                </div>
                <div>
                  <strong>Status:</strong>{" "}
                  <span className={theme.isActive ? "text-green-600" : "text-red-600"}>
                    {theme.isActive ? "Active" : "Inactive"}
                  </span>
                </div>
              </div>

              {/* Color preview */}
              <div className="mt-4">
                <strong className="text-sm">Color Preview:</strong>
                <div className="mt-2 flex gap-2">
                  <div
                    className="w-8 h-8 rounded border"
                    style={{ backgroundColor: theme.colors.primaryButton.replace(/bg-\w+-\d+/, '#3B82F6') }}
                    title="Primary Button"
                  />
                  <div
                    className="w-8 h-8 rounded border"
                    style={{ backgroundColor: theme.colors.secondaryButton.replace(/bg-\w+-\d+/, '#6B7280') }}
                    title="Secondary Button"
                  />
                  <div
                    className="w-8 h-8 rounded border"
                    style={{ backgroundColor: theme.colors.accentColor.replace(/bg-\w+-\d+/, '#F3F4F6') }}
                    title="Accent Color"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Create Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create Form Theme</DialogTitle>
            <DialogDescription>
              Create a new theme for form styling and colors.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Name *</Label>
                <Input
                  id="name"
                  value={newTheme.name || ""}
                  onChange={(e) => setNewTheme({ ...newTheme, name: e.target.value })}
                  placeholder="e.g., ocean-theme"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="template">Template</Label>
                <Select value={selectedTemplate} onValueChange={handleTemplateSelect}>
                  <SelectTrigger>
                    <SelectValue placeholder="Choose a template" />
                  </SelectTrigger>
                  <SelectContent>
                    {getThemeTemplates().map((template) => (
                      <SelectItem key={template.name} value={template.name}>
                        {template.titleEn}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="titleEl">Greek Title *</Label>
                <Input
                  id="titleEl"
                  value={newTheme.titleEl || ""}
                  onChange={(e) => setNewTheme({ ...newTheme, titleEl: e.target.value })}
                  placeholder="Θέμα Ωκεανού"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="titleEn">English Title *</Label>
                <Input
                  id="titleEn"
                  value={newTheme.titleEn || ""}
                  onChange={(e) => setNewTheme({ ...newTheme, titleEn: e.target.value })}
                  placeholder="Ocean Theme"
                />
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={newTheme.isActive}
                  onCheckedChange={(checked) => setNewTheme({ ...newTheme, isActive: checked })}
                />
                <Label htmlFor="isActive">Active</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="isDefault"
                  checked={newTheme.isDefault}
                  onCheckedChange={(checked) => setNewTheme({ ...newTheme, isDefault: checked })}
                />
                <Label htmlFor="isDefault">Set as Default</Label>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateTheme}>Create</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Dialog - Similar structure to Create Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Form Theme</DialogTitle>
            <DialogDescription>
              Update the theme properties and colors.
            </DialogDescription>
          </DialogHeader>
          {/* Similar content to create dialog */}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateTheme}>Update</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Preview Dialog */}
      <Dialog open={isPreviewDialogOpen} onOpenChange={setIsPreviewDialogOpen}>
        <DialogContent className="max-w-6xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Palette className="h-5 w-5" />
              Theme Preview: {previewTheme?.titleEn}
            </DialogTitle>
          </DialogHeader>
          {previewTheme && (
            <div className="space-y-6">
              <ThemePreview themeName={previewTheme.titleEn} />

              <div className="border-t pt-6">
                <h4 className="text-lg font-semibold mb-4">Color Customization</h4>
                <ColorCustomizer
                  colors={previewTheme.colors}
                  onChange={(colors) => {
                    setPreviewTheme({
                      ...previewTheme,
                      colors,
                    });
                  }}
                  onReset={() => {
                    const template = getThemeTemplates().find(t => t.name === previewTheme.name);
                    if (template) {
                      setPreviewTheme({
                        ...previewTheme,
                        colors: template.colors,
                      });
                    }
                  }}
                />
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
