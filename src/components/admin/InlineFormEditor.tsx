import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { FormFieldConfig } from "@/schemas/FormSchema";
import { fetchFormConfigs } from "@/services/formConfigService";
import { toast } from "@/components/ui/use-toast";
import { FormPreview } from "./FormPreview";
import { InlineFieldEditor } from "./InlineFieldEditor";
import { Loader2, Settings, Palette, Type, Plus } from "lucide-react";

interface InlineFormEditorProps {
  selectedFormType: "candidate" | "nannyRequest";
  onFormTypeChange: (formType: "candidate" | "nannyRequest") => void;
  onConfigsChange: () => void;
}

export function InlineFormEditor({ 
  selectedFormType, 
  onFormTypeChange, 
  onConfigsChange 
}: InlineFormEditorProps) {
  const [configs, setConfigs] = useState<FormFieldConfig[]>([]);
  const [selectedConfig, setSelectedConfig] = useState<FormFieldConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [language, setLanguage] = useState<"el" | "en">("en");
  const [activeTab, setActiveTab] = useState<"fields" | "labels" | "themes">("fields");

  // Load form configurations
  const loadConfigs = async () => {
    setLoading(true);
    try {
      const data = await fetchFormConfigs();
      const filteredConfigs = data.filter(config => 
        config.formType === selectedFormType || config.formType === "both"
      );
      setConfigs(filteredConfigs);
      
      // Select first config if none selected
      if (!selectedConfig && filteredConfigs.length > 0) {
        setSelectedConfig(filteredConfigs[0]);
      }
    } catch (error) {
      console.error("Error loading form configs:", error);
      toast({
        title: "Error",
        description: "Failed to load form configurations",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadConfigs();
  }, [selectedFormType]);

  const handleConfigUpdate = () => {
    loadConfigs();
    onConfigsChange();
  };

  const filteredConfigs = configs.filter(config => 
    config.formType === selectedFormType || config.formType === "both"
  );

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <Loader2 className="h-12 w-12 mx-auto mb-4 text-muted-foreground animate-spin" />
          <p className="text-muted-foreground">Loading form configurations...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Top Navigation Bar */}
      <Card>
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <CardTitle className="text-xl">Inline Form Editor</CardTitle>
              
              {/* Form Type Selector */}
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">Form:</span>
                <Select value={selectedFormType} onValueChange={onFormTypeChange}>
                  <SelectTrigger className="w-48">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="candidate">Candidate Form</SelectItem>
                    <SelectItem value="nannyRequest">Nanny Request Form</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Language Selector */}
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">Language:</span>
                <Select value={language} onValueChange={(value: "el" | "en") => setLanguage(value)}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en">English</SelectItem>
                    <SelectItem value="el">Greek</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Badge variant="outline">
                {filteredConfigs.length} fields
              </Badge>
              <Button size="sm" variant="outline">
                <Plus className="h-4 w-4 mr-2" />
                Add Field
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Main Content Area */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-[calc(100vh-200px)]">
        {/* Left Panel - Editor */}
        <div className="space-y-4">
          <Tabs value={activeTab} onValueChange={(value: any) => setActiveTab(value)}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="fields">
                <Settings className="h-4 w-4 mr-2" />
                Fields
              </TabsTrigger>
              <TabsTrigger value="labels">
                <Type className="h-4 w-4 mr-2" />
                Labels
              </TabsTrigger>
              <TabsTrigger value="themes">
                <Palette className="h-4 w-4 mr-2" />
                Themes
              </TabsTrigger>
            </TabsList>

            <TabsContent value="fields" className="space-y-4">
              {/* Field Selector */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">Select Field to Edit</CardTitle>
                </CardHeader>
                <CardContent>
                  <Select 
                    value={selectedConfig?.id || ""} 
                    onValueChange={(configId) => {
                      const config = filteredConfigs.find(c => c.id === configId);
                      setSelectedConfig(config || null);
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a field to edit..." />
                    </SelectTrigger>
                    <SelectContent>
                      {filteredConfigs.map((config) => (
                        <SelectItem key={config.id} value={config.id}>
                          <div className="flex items-center gap-2">
                            <span>{language === "el" ? config.titleEl : config.titleEn}</span>
                            <Badge variant="outline" className="text-xs">
                              {config.fieldType}
                            </Badge>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </CardContent>
              </Card>

              {/* Field Editor */}
              {selectedConfig && (
                <InlineFieldEditor
                  config={selectedConfig}
                  onConfigUpdate={handleConfigUpdate}
                  language={language}
                />
              )}
            </TabsContent>

            <TabsContent value="labels" className="space-y-4">
              <Card>
                <CardContent className="p-6 text-center">
                  <Type className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-semibold mb-2">Labels Editor</h3>
                  <p className="text-muted-foreground">
                    Inline label editing coming soon...
                  </p>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="themes" className="space-y-4">
              <Card>
                <CardContent className="p-6 text-center">
                  <Palette className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-semibold mb-2">Theme Editor</h3>
                  <p className="text-muted-foreground">
                    Inline theme editing coming soon...
                  </p>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Right Panel - Preview */}
        <FormPreview
          formType={selectedFormType}
          language={language}
          className="h-full"
        />
      </div>
    </div>
  );
}
