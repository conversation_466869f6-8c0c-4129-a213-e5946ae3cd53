import { <PERSON><PERSON>orm<PERSON>enderer } from "@/components/forms/AutoFormRenderer";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { FormThemeProvider } from "@/contexts/FormThemeContext";
import { formSchema, nannyRequestFormSchema } from "@/schemas/FormSchema";
import { zodResolver } from "@hookform/resolvers/zod";
import { RefreshCw } from "lucide-react";
import { useState } from "react";
import { FormProvider, useForm } from "react-hook-form";

interface FormPreviewProps {
  formType: "candidate" | "nannyRequest";
  language: "el" | "en";
  className?: string;
}

export function FormPreview({ formType, language, className }: FormPreviewProps) {
  const [refreshKey, setRefreshKey] = useState(0);

  // Get the appropriate schema and default values
  const schema = formType === "candidate" ? formSchema : nannyRequestFormSchema;

  const defaultValues = formType === "candidate"
    ? {
      candidateType: "nanny",
      firstName: "",
      lastName: "",
      email: "",
      contactNumber: "",
      city: "",
      country: "Greece",
      // Add other default values as needed
    }
    : {
      fatherName: "",
      motherName: "",
      email: "",
      contactNumber: "",
      city: "",
      country: "Greece",
      // Add other default values as needed
    };

  const form = useForm({
    resolver: zodResolver(schema),
    defaultValues,
    mode: "onChange",
  });

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
    form.reset(defaultValues);
  };

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <div className="flex items-center gap-2">
          <CardTitle className="text-lg">Form Preview</CardTitle>
          <Badge variant="secondary">
            {formType === "candidate" ? "Candidate Form" : "Nanny Request Form"}
          </Badge>
          <Badge variant="outline">
            {language === "el" ? "Greek" : "English"}
          </Badge>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefresh}
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </CardHeader>
      <CardContent className="max-h-[80vh] overflow-y-auto">
        <FormThemeProvider>
          <FormProvider {...form}>
            <div key={refreshKey} className="space-y-6">
              <AutoFormRenderer
                formType={formType}
                language={language}
                showPreview={true}
                className="space-y-4"
              />
            </div>
          </FormProvider>
        </FormThemeProvider>
      </CardContent>
    </Card>
  );
}
