import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/components/ui/use-toast";
import { useDebounce } from "@/hooks/useDebounce";
import { FormFieldConfig, FormFieldOption } from "@/schemas/FormSchema";
import { createFormOption, deleteFormOption, updateFormConfig, updateFormOption } from "@/services/formConfigService";
import { GripVertical, Plus, Save, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";
import { DragDropContext, Draggable, Droppable } from "react-beautiful-dnd";

interface InlineFieldEditorProps {
  config: FormFieldConfig;
  onConfigUpdate: () => void;
  language: "el" | "en";
}

export function InlineFieldEditor({ config, onConfigUpdate, language }: InlineFieldEditorProps) {
  const [localConfig, setLocalConfig] = useState<FormFieldConfig>(config);
  const [localOptions, setLocalOptions] = useState<FormFieldOption[]>(config.options || []);
  const [hasChanges, setHasChanges] = useState(false);

  // Debounce the config updates
  const debouncedConfig = useDebounce(localConfig, 1000);
  const debouncedOptions = useDebounce(localOptions, 1000);

  // Update config in database when debounced value changes
  useEffect(() => {
    if (hasChanges && debouncedConfig !== config && debouncedConfig.id === config.id) {
      handleUpdateConfig();
    }
  }, [debouncedConfig]);

  // Update options in database when debounced value changes
  useEffect(() => {
    if (hasChanges && debouncedOptions !== config.options && localOptions.length > 0) {
      handleUpdateOptions();
    }
  }, [debouncedOptions]);

  const handleUpdateConfig = async () => {
    try {
      const success = await updateFormConfig(localConfig);
      if (success) {
        onConfigUpdate();
        setHasChanges(false);
        toast({
          title: "Success",
          description: "Field configuration updated",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update field configuration",
        variant: "destructive",
      });
    }
  };

  const handleUpdateOptions = async () => {
    try {
      // Update existing options
      for (const option of localOptions) {
        if (option.id) {
          await updateFormOption({ ...option, fieldConfigId: localConfig.id });
        }
      }
      onConfigUpdate();
      setHasChanges(false);
      toast({
        title: "Success",
        description: "Field options updated",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update field options",
        variant: "destructive",
      });
    }
  };

  const handleConfigChange = (field: keyof FormFieldConfig, value: any) => {
    setLocalConfig(prev => ({ ...prev, [field]: value }));
    setHasChanges(true);
  };

  const handleOptionChange = (optionId: string, field: keyof FormFieldOption, value: any) => {
    setLocalOptions(prev =>
      prev.map(option =>
        option.id === optionId ? { ...option, [field]: value } : option
      )
    );
    setHasChanges(true);
  };

  const handleAddOption = async () => {
    const newOption: Omit<FormFieldOption, 'id'> = {
      labelEl: "Νέα επιλογή",
      labelEn: "New option",
      order: localOptions.length,
      isActive: true,
    };

    try {
      const optionId = await createFormOption({ ...newOption, fieldConfigId: localConfig.id });
      if (optionId) {
        setLocalOptions(prev => [...prev, { ...newOption, id: optionId }]);
        onConfigUpdate();
        toast({
          title: "Success",
          description: "New option added",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add new option",
        variant: "destructive",
      });
    }
  };

  const handleDeleteOption = async (optionId: string) => {
    try {
      const success = await deleteFormOption(optionId);
      if (success) {
        setLocalOptions(prev => prev.filter(option => option.id !== optionId));
        onConfigUpdate();
        toast({
          title: "Success",
          description: "Option deleted",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete option",
        variant: "destructive",
      });
    }
  };

  const handleDragEnd = (result: any) => {
    if (!result.destination) return;

    const items = Array.from(localOptions);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // Update order indices
    const updatedItems = items.map((item, index) => ({ ...item, order: index }));
    setLocalOptions(updatedItems);
    setHasChanges(true);
  };

  return (
    <Card className="h-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <div className="flex items-center gap-2">
          <CardTitle className="text-lg">
            {language === "el" ? localConfig.titleEl : localConfig.titleEn}
          </CardTitle>
          <Badge variant={localConfig.isActive ? "default" : "secondary"}>
            {localConfig.isActive ? "Active" : "Inactive"}
          </Badge>
          {hasChanges && (
            <Badge variant="outline" className="text-orange-600">
              Unsaved Changes
            </Badge>
          )}
        </div>
        {hasChanges && (
          <Button size="sm" onClick={handleUpdateConfig}>
            <Save className="h-4 w-4 mr-2" />
            Save Now
          </Button>
        )}
      </CardHeader>
      <CardContent className="space-y-6 max-h-[70vh] overflow-y-auto">
        {/* Basic Configuration */}
        <div className="space-y-4">
          <h3 className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">
            Basic Configuration
          </h3>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Field Name</Label>
              <Input
                id="name"
                value={localConfig.name}
                onChange={(e) => handleConfigChange('name', e.target.value)}
                placeholder="fieldName"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="fieldType">Field Type</Label>
              <Select
                value={localConfig.fieldType}
                onValueChange={(value) => handleConfigChange('fieldType', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="text">Text</SelectItem>
                  <SelectItem value="email">Email</SelectItem>
                  <SelectItem value="tel">Phone</SelectItem>
                  <SelectItem value="number">Number</SelectItem>
                  <SelectItem value="textarea">Textarea</SelectItem>
                  <SelectItem value="select">Select</SelectItem>
                  <SelectItem value="multiselect">Multi Select</SelectItem>
                  <SelectItem value="checkbox">Checkbox</SelectItem>
                  <SelectItem value="radio">Radio</SelectItem>
                  <SelectItem value="switch">Switch</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="titleEl">Greek Title</Label>
              <Input
                id="titleEl"
                value={localConfig.titleEl}
                onChange={(e) => handleConfigChange('titleEl', e.target.value)}
                placeholder="Ελληνικός τίτλος"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="titleEn">English Title</Label>
              <Input
                id="titleEn"
                value={localConfig.titleEn}
                onChange={(e) => handleConfigChange('titleEn', e.target.value)}
                placeholder="English title"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="placeholderEl">Greek Placeholder</Label>
              <Input
                id="placeholderEl"
                value={localConfig.placeholderEl || ""}
                onChange={(e) => handleConfigChange('placeholderEl', e.target.value)}
                placeholder="Ελληνικό placeholder"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="placeholderEn">English Placeholder</Label>
              <Input
                id="placeholderEn"
                value={localConfig.placeholderEn || ""}
                onChange={(e) => handleConfigChange('placeholderEn', e.target.value)}
                placeholder="English placeholder"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="descriptionEl">Greek Description</Label>
              <Textarea
                id="descriptionEl"
                value={localConfig.descriptionEl || ""}
                onChange={(e) => handleConfigChange('descriptionEl', e.target.value)}
                placeholder="Ελληνική περιγραφή"
                rows={3}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="descriptionEn">English Description</Label>
              <Textarea
                id="descriptionEn"
                value={localConfig.descriptionEn || ""}
                onChange={(e) => handleConfigChange('descriptionEn', e.target.value)}
                placeholder="English description"
                rows={3}
              />
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Switch
                id="isActive"
                checked={localConfig.isActive}
                onCheckedChange={(checked) => handleConfigChange('isActive', checked)}
              />
              <Label htmlFor="isActive">Active</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="isRequired"
                checked={localConfig.isRequired}
                onCheckedChange={(checked) => handleConfigChange('isRequired', checked)}
              />
              <Label htmlFor="isRequired">Required</Label>
            </div>
          </div>
        </div>

        {/* Options Management */}
        {(localConfig.fieldType === 'select' ||
          localConfig.fieldType === 'multiselect' ||
          localConfig.fieldType === 'radio' ||
          localConfig.fieldType === 'checkbox') && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">
                  Options ({localOptions.length})
                </h3>
                <Button size="sm" variant="outline" onClick={handleAddOption}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Option
                </Button>
              </div>

              <DragDropContext onDragEnd={handleDragEnd}>
                <Droppable droppableId="options">
                  {(provided) => (
                    <div {...provided.droppableProps} ref={provided.innerRef} className="space-y-2">
                      {localOptions.map((option, index) => (
                        <Draggable key={option.id} draggableId={option.id} index={index}>
                          {(provided) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              className="flex items-center gap-2 p-3 border rounded-lg bg-background"
                            >
                              <div {...provided.dragHandleProps}>
                                <GripVertical className="h-4 w-4 text-muted-foreground" />
                              </div>
                              <div className="flex-1 grid grid-cols-2 gap-2">
                                <Input
                                  value={option.labelEl}
                                  onChange={(e) => handleOptionChange(option.id, 'labelEl', e.target.value)}
                                  placeholder="Ελληνική ετικέτα"
                                  className="text-sm"
                                />
                                <Input
                                  value={option.labelEn}
                                  onChange={(e) => handleOptionChange(option.id, 'labelEn', e.target.value)}
                                  placeholder="English label"
                                  className="text-sm"
                                />
                              </div>
                              <Switch
                                checked={option.isActive}
                                onCheckedChange={(checked) => handleOptionChange(option.id, 'isActive', checked)}
                              />
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleDeleteOption(option.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </DragDropContext>
            </div>
          )}
      </CardContent>
    </Card>
  );
}
