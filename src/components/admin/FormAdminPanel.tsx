import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "@/components/ui/use-toast";
import { FormFieldConfig } from "@/schemas/FormSchema";
import { fetchFormConfigs } from "@/services/formConfigService";
import { Edit3, Eye, Palette, Settings, Type } from "lucide-react";
import { useEffect, useState } from "react";
import { FormConfigAdmin } from "./FormConfigAdmin";
import { FormLabelsAdmin } from "./FormLabelsAdmin";
import { FormOptionsManager } from "./FormOptionsManager";
import { FormThemeAdmin } from "./FormThemeAdmin";
import { InlineFormEditor } from "./InlineFormEditor";

type ViewMode = "traditional" | "inline";
type FormType = "candidate" | "nannyRequest";

export function FormAdminPanel() {
  const [configs, setConfigs] = useState<FormFieldConfig[]>([]);
  const [selectedConfig, setSelectedConfig] = useState<FormFieldConfig | null>(null);
  const [isOptionsDialogOpen, setIsOptionsDialogOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState<ViewMode>("inline");
  const [selectedFormType, setSelectedFormType] = useState<FormType>("candidate");

  // Load form configurations
  const loadConfigs = async () => {
    setLoading(true);
    try {
      const data = await fetchFormConfigs();
      setConfigs(data);
    } catch (error) {
      console.error("Error loading form configs:", error);
      toast({
        title: "Error",
        description: "Failed to load form configurations",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadConfigs();
  }, []);

  // Handle manage options
  const handleManageOptions = (config: FormFieldConfig) => {
    setSelectedConfig(config);
    setIsOptionsDialogOpen(true);
  };



  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Form Administration</h1>
          <p className="text-muted-foreground">
            Manage form field configurations, options, and their order
          </p>
        </div>

        {/* View Mode Toggle */}
        <div className="flex items-center gap-2">
          <Button
            variant={viewMode === "inline" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("inline")}
          >
            <Eye className="h-4 w-4 mr-2" />
            Inline Editor
          </Button>
          <Button
            variant={viewMode === "traditional" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("traditional")}
          >
            <Settings className="h-4 w-4 mr-2" />
            Traditional
          </Button>
        </div>
      </div>

      {viewMode === "inline" ? (
        <InlineFormEditor
          selectedFormType={selectedFormType}
          onFormTypeChange={setSelectedFormType}
          onConfigsChange={loadConfigs}
        />
      ) : (
        <Tabs defaultValue="configurations" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="configurations">
              <Edit3 className="h-4 w-4 mr-2" />
              Field Configurations
            </TabsTrigger>
            <TabsTrigger value="labels">
              <Type className="h-4 w-4 mr-2" />
              Form Labels
            </TabsTrigger>
            <TabsTrigger value="themes">
              <Palette className="h-4 w-4 mr-2" />
              Color Themes
            </TabsTrigger>
            <TabsTrigger value="overview">
              <Eye className="h-4 w-4 mr-2" />
              Overview & Quick Actions
            </TabsTrigger>
          </TabsList>

          <TabsContent value="configurations" className="space-y-6">
            <FormConfigAdmin />
          </TabsContent>

          <TabsContent value="labels" className="space-y-6">
            <FormLabelsAdmin />
          </TabsContent>

          <TabsContent value="themes" className="space-y-6">
            <FormThemeAdmin />
          </TabsContent>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid gap-4">
              <h2 className="text-2xl font-bold">Configuration Overview</h2>

              {loading ? (
                <Card>
                  <CardContent className="p-6 text-center">
                    <Settings className="h-12 w-12 mx-auto mb-4 text-muted-foreground animate-spin" />
                    <p className="text-muted-foreground">Loading configurations...</p>
                  </CardContent>
                </Card>
              ) : configs.length === 0 ? (
                <Card>
                  <CardContent className="p-6 text-center">
                    <Settings className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                    <h3 className="text-lg font-semibold mb-2">No Configurations Found</h3>
                    <p className="text-muted-foreground mb-4">
                      Start by creating your first form field configuration.
                    </p>
                    <Button onClick={() => window.location.reload()}>
                      Refresh
                    </Button>
                  </CardContent>
                </Card>
              ) : (
                <div className="grid gap-4">
                  {configs.map((config) => (
                    <Card key={config.id}>
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-lg">{config.titleEn}</CardTitle>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleManageOptions(config)}
                        >
                          <Settings className="h-4 w-4 mr-2" />
                          Manage Options ({config.options.length})
                        </Button>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <strong>Name:</strong> {config.name}
                          </div>
                          <div>
                            <strong>Type:</strong> {config.fieldType}
                          </div>
                          <div>
                            <strong>Options:</strong> {config.options.length}
                          </div>
                          <div>
                            <strong>Status:</strong>{" "}
                            <span className={config.isActive ? "text-green-600" : "text-red-600"}>
                              {config.isActive ? "Active" : "Inactive"}
                            </span>
                          </div>
                        </div>

                        {config.descriptionEn && (
                          <div className="mt-2 text-sm text-muted-foreground">
                            <strong>Description:</strong> {config.descriptionEn}
                          </div>
                        )}

                        {config.options.length > 0 && (
                          <div className="mt-4">
                            <strong className="text-sm">Sample Options:</strong>
                            <div className="mt-2 flex flex-wrap gap-2">
                              {config.options.slice(0, 3).map((option) => (
                                <span
                                  key={option.id}
                                  className="px-2 py-1 bg-secondary text-secondary-foreground rounded text-xs"
                                >
                                  {option.labelEn}
                                </span>
                              ))}
                              {config.options.length > 3 && (
                                <span className="px-2 py-1 bg-muted text-muted-foreground rounded text-xs">
                                  +{config.options.length - 3} more
                                </span>
                              )}
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      )}

      {/* Options Management Dialog */}
      <Dialog open={isOptionsDialogOpen} onOpenChange={setIsOptionsDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              Manage Options: {selectedConfig?.titleEn}
            </DialogTitle>
            <DialogDescription>
              Add, edit, reorder, and manage options for this form field.
            </DialogDescription>
          </DialogHeader>
          {selectedConfig && (
            <FormOptionsManager
              config={selectedConfig}
              onConfigUpdate={() => {
                loadConfigs();
                // Update the selected config with fresh data
                const updatedConfig = configs.find(c => c.id === selectedConfig.id);
                if (updatedConfig) {
                  setSelectedConfig(updatedConfig);
                }
              }}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
