import { Checkbox } from "@/components/ui/checkbox";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  getFormConfigDescription,
  getFormConfigPlaceholder,
  getFormConfigTitle,
  getFormOptionLabel,
  useFormOptions,
} from "@/hooks/useFormConfig";
import { getText } from "@/utils/form-utils";
import { Control, FieldPath, FieldValues } from "react-hook-form";

interface DynamicFormFieldProps<T extends FieldValues> {
  control: Control<T>;
  name: FieldPath<T>;
  configName: string;
  language: "el" | "en";
  className?: string;
  required?: boolean;
}

export function DynamicFormField<T extends FieldValues>({
  control,
  name,
  configName,
  language,
  className,
  required = false,
}: DynamicFormFieldProps<T>) {
  const { options, loading, error, config } = useFormOptions(configName);

  if (loading) {
    return (
      <div className={className}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
          <div className="h-10 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (error || !config) {
    return (
      <div className={className}>
        <div className="text-red-500 text-sm">
          Error loading {configName}: {error || "Configuration not found"}
        </div>
      </div>
    );
  }

  const title = getFormConfigTitle(config, language);
  const placeholder = getFormConfigPlaceholder(config, language);
  const description = getFormConfigDescription(config, language);

  return (
    <div className={className}>
      <FormField
        control={control}
        name={name}
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              {title}
              {required && <span className="text-red-500 ml-1">*</span>}
            </FormLabel>
            {description && (
              <FormDescription>
                {description}
              </FormDescription>
            )}
            <FormControl>
              {config.fieldType === "select" && (
                <Select
                  value={field.value || ""}
                  onValueChange={field.onChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={placeholder || `Select ${title}`} />
                  </SelectTrigger>
                  <SelectContent>
                    {options.map((option) => (
                      <SelectItem key={option.id} value={option.id}>
                        {getFormOptionLabel(option, language)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}

              {config.fieldType === "radio" && (
                <RadioGroup
                  value={field.value || ""}
                  onValueChange={field.onChange}
                  className="flex flex-col space-y-2"
                >
                  {options.map((option) => (
                    <FormItem
                      key={option.id}
                      className="flex items-center space-x-3 space-y-0"
                    >
                      <FormControl>
                        <RadioGroupItem value={option.id} />
                      </FormControl>
                      <FormLabel className="font-normal">
                        {getFormOptionLabel(option, language)}
                      </FormLabel>
                    </FormItem>
                  ))}
                </RadioGroup>
              )}

              {(config.fieldType === "multiselect" || config.fieldType === "checkbox") && (
                <div className="grid grid-cols-2 gap-2 mt-2">
                  {options.map((option) => (
                    <FormItem
                      key={option.id}
                      className="flex flex-row items-start space-x-3 space-y-0"
                    >
                      <FormControl>
                        <Checkbox
                          checked={field.value?.includes(option.id) || false}
                          onCheckedChange={(checked) => {
                            const currentValue = field.value || [];
                            const updatedValue = checked
                              ? [...currentValue, option.id]
                              : currentValue.filter((value: string) => value !== option.id);
                            field.onChange(updatedValue);
                          }}
                        />
                      </FormControl>
                      <FormLabel className="font-normal">
                        {getFormOptionLabel(option, language)}
                      </FormLabel>
                    </FormItem>
                  ))}
                </div>
              )}
            </FormControl>
            <FormMessage lang={language} />
          </FormItem>
        )}
      />
    </div>
  );
}

// Fallback component that uses hardcoded options if dynamic loading fails
interface FallbackFormFieldProps<T extends FieldValues> {
  control: Control<T>;
  name: FieldPath<T>;
  title: string;
  options: Array<{ id: string; label?: string; labelEl?: string; labelEn?: string }>;
  fieldType: "select" | "multiselect" | "checkbox" | "radio";
  language: "el" | "en";
  className?: string;
  required?: boolean;
  placeholder?: string;
  description?: string;
}

export function FallbackFormField<T extends FieldValues>({
  control,
  name,
  title,
  options,
  fieldType,
  language,
  className,
  required = false,
  placeholder,
  description,
}: FallbackFormFieldProps<T>) {
  return (
    <div className={className}>
      <FormField
        control={control}
        name={name}
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              {title}
              {required && <span className="text-red-500 ml-1">*</span>}
            </FormLabel>
            {description && (
              <FormDescription>
                {description}
              </FormDescription>
            )}
            <FormControl>
              {fieldType === "select" && (
                <Select
                  value={field.value || ""}
                  onValueChange={field.onChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={placeholder || `Select ${title}`} />
                  </SelectTrigger>
                  <SelectContent>
                    {options.map((option) => (
                      <SelectItem key={option.id} value={option.id}>
                        {option.label || 
                         (language === "el" ? option.labelEl : option.labelEn) || 
                         option.id}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}

              {fieldType === "radio" && (
                <RadioGroup
                  value={field.value || ""}
                  onValueChange={field.onChange}
                  className="flex flex-col space-y-2"
                >
                  {options.map((option) => (
                    <FormItem
                      key={option.id}
                      className="flex items-center space-x-3 space-y-0"
                    >
                      <FormControl>
                        <RadioGroupItem value={option.id} />
                      </FormControl>
                      <FormLabel className="font-normal">
                        {option.label || 
                         (language === "el" ? option.labelEl : option.labelEn) || 
                         option.id}
                      </FormLabel>
                    </FormItem>
                  ))}
                </RadioGroup>
              )}

              {(fieldType === "multiselect" || fieldType === "checkbox") && (
                <div className="grid grid-cols-2 gap-2 mt-2">
                  {options.map((option) => (
                    <FormItem
                      key={option.id}
                      className="flex flex-row items-start space-x-3 space-y-0"
                    >
                      <FormControl>
                        <Checkbox
                          checked={field.value?.includes(option.id) || false}
                          onCheckedChange={(checked) => {
                            const currentValue = field.value || [];
                            const updatedValue = checked
                              ? [...currentValue, option.id]
                              : currentValue.filter((value: string) => value !== option.id);
                            field.onChange(updatedValue);
                          }}
                        />
                      </FormControl>
                      <FormLabel className="font-normal">
                        {option.label || 
                         (language === "el" ? option.labelEl : option.labelEn) || 
                         option.id}
                      </FormLabel>
                    </FormItem>
                  ))}
                </div>
              )}
            </FormControl>
            <FormMessage lang={language} />
          </FormItem>
        )}
      />
    </div>
  );
}
