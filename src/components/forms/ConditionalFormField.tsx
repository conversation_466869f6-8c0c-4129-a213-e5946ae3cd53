import { useFormContext, useWatch } from "react-hook-form";
import { FormFieldConfig } from "@/schemas/FormSchema";
import { DynamicFormField } from "./DynamicFormField";
import { useEffect, useState } from "react";

interface ConditionalFormFieldProps {
  config: FormFieldConfig;
  language: "el" | "en";
  className?: string;
}

/**
 * Component that renders a form field conditionally based on other field values
 * Supports various conditional operators and validation rules
 */
export function ConditionalFormField({ config, language, className }: ConditionalFormFieldProps) {
  const form = useFormContext();
  const [isVisible, setIsVisible] = useState(true);

  // Watch the conditional field if specified
  const conditionalFieldValue = useWatch({
    control: form.control,
    name: config.conditionalFieldKey || "",
  });

  // Evaluate conditional visibility
  useEffect(() => {
    if (!config.conditionalFieldKey || !config.conditionalValue) {
      setIsVisible(true);
      return;
    }

    const shouldShow = evaluateCondition(
      conditionalFieldValue,
      config.conditionalValue,
      config.conditionalOperator || "equals"
    );

    setIsVisible(shouldShow);

    // Clear field value if hidden
    if (!shouldShow && form.getValues(config.fieldKey)) {
      form.setValue(config.fieldKey, undefined);
    }
  }, [conditionalFieldValue, config, form]);

  // Don't render if not visible
  if (!isVisible) {
    return null;
  }

  return (
    <div className={className}>
      <DynamicFormField
        control={form.control}
        name={config.fieldKey as any}
        configName={config.configName}
        language={language}
        required={config.isRequired}
        validationRules={config.validationRules}
        customValidationMessage={
          language === "el" 
            ? config.customValidationMessageEl 
            : config.customValidationMessageEn
        }
      />
    </div>
  );
}

/**
 * Evaluates a conditional expression
 */
function evaluateCondition(
  fieldValue: any,
  targetValue: string,
  operator: string
): boolean {
  if (fieldValue === undefined || fieldValue === null) {
    return false;
  }

  const fieldStr = String(fieldValue).toLowerCase();
  const targetStr = targetValue.toLowerCase();

  switch (operator) {
    case "equals":
      return fieldStr === targetStr;
    
    case "not_equals":
      return fieldStr !== targetStr;
    
    case "contains":
      return fieldStr.includes(targetStr);
    
    case "not_contains":
      return !fieldStr.includes(targetStr);
    
    case "greater_than":
      const fieldNum = parseFloat(fieldStr);
      const targetNum = parseFloat(targetStr);
      return !isNaN(fieldNum) && !isNaN(targetNum) && fieldNum > targetNum;
    
    case "less_than":
      const fieldNum2 = parseFloat(fieldStr);
      const targetNum2 = parseFloat(targetStr);
      return !isNaN(fieldNum2) && !isNaN(targetNum2) && fieldNum2 < targetNum2;
    
    case "in":
      const targetArray = targetStr.split(",").map(s => s.trim());
      return targetArray.includes(fieldStr);
    
    case "not_in":
      const targetArray2 = targetStr.split(",").map(s => s.trim());
      return !targetArray2.includes(fieldStr);
    
    default:
      return true;
  }
}

/**
 * Hook to get all available field keys for conditional field selection
 */
export function useAvailableFieldKeys(configs: FormFieldConfig[]): string[] {
  return configs
    .filter(config => config.isActive)
    .map(config => config.fieldKey)
    .sort();
}
