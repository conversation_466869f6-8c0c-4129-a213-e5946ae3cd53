import { useFormConfigs } from "@/hooks/useFormConfig";
import { useFormLabelCategories } from "@/hooks/useFormLabels";
import { FormFieldConfig, FormLabelCategory } from "@/schemas/FormSchema";
import { cn } from "@/lib/utils";
import { useFormContext } from "react-hook-form";
import { DynamicFormField } from "./DynamicFormField";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { Loader2 } from "lucide-react";

interface AutoDynamicFormProps {
  formType: "candidate" | "nannyRequest";
  language: "el" | "en";
  className?: string;
}

/**
 * Component that automatically renders all available dynamic fields and labels
 * organized by categories, without hardcoding any field names
 */
export function AutoDynamicForm({
  formType,
  language,
  className,
}: AutoDynamicFormProps) {
  const form = useFormContext();
  const { configs, loading: configsLoading, error: configsError } = useFormConfigs();
  const { categories, loading: labelsLoading, error: labelsError } = useFormLabelCategories();

  if (configsLoading || labelsLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading dynamic form content...</span>
      </div>
    );
  }

  if (configsError || labelsError) {
    return (
      <div className="p-4 border border-red-200 rounded-lg bg-red-50">
        <p className="text-red-600">
          Error loading dynamic form content: {configsError || labelsError}
        </p>
      </div>
    );
  }

  // Filter configurations for this form type
  const relevantConfigs = configs.filter(config => 
    config.isActive && 
    (config.formType === formType || config.formType === "both")
  );

  // Filter label categories for this form type
  const relevantCategories = categories.filter(category =>
    category.isActive &&
    (category.formType === formType || category.formType === "both")
  );

  // Group configurations by category
  const configsByCategory = relevantConfigs.reduce((acc, config) => {
    if (!acc[config.category]) {
      acc[config.category] = [];
    }
    acc[config.category].push(config);
    return acc;
  }, {} as Record<string, FormFieldConfig[]>);

  // Get all available categories (from both configs and labels)
  const allCategories = new Set([
    ...Object.keys(configsByCategory),
    ...relevantCategories.map(cat => cat.name)
  ]);

  return (
    <div className={cn("space-y-6", className)}>
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">
          Dynamic Form Content for {formType === "candidate" ? "Candidate" : "Nanny Request"} Form
        </h2>
        <div className="flex flex-wrap gap-2">
          <Badge variant="outline">
            {relevantConfigs.length} Dynamic Fields
          </Badge>
          <Badge variant="outline">
            {relevantCategories.length} Label Categories
          </Badge>
          <Badge variant="outline">
            {allCategories.size} Total Categories
          </Badge>
        </div>
      </div>

      {Array.from(allCategories).map(categoryName => {
        const categoryConfig = relevantCategories.find(cat => cat.name === categoryName);
        const categoryFields = configsByCategory[categoryName] || [];
        const categoryLabels = categoryConfig?.labels || [];

        return (
          <AutoDynamicSection
            key={categoryName}
            categoryName={categoryName}
            categoryConfig={categoryConfig}
            fields={categoryFields}
            labels={categoryLabels}
            formType={formType}
            language={language}
          />
        );
      })}

      {allCategories.size === 0 && (
        <Card>
          <CardContent className="pt-6">
            <p className="text-center text-muted-foreground">
              No dynamic form content available for {formType} forms.
              Add fields and labels through the admin panel to see them here.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

interface AutoDynamicSectionProps {
  categoryName: string;
  categoryConfig?: FormLabelCategory;
  fields: FormFieldConfig[];
  labels: any[];
  formType: "candidate" | "nannyRequest";
  language: "el" | "en";
}

function AutoDynamicSection({
  categoryName,
  categoryConfig,
  fields,
  labels,
  formType,
  language,
}: AutoDynamicSectionProps) {
  const form = useFormContext();

  // Get category title
  const categoryTitle = categoryConfig 
    ? (language === "el" ? categoryConfig.titleEl : categoryConfig.titleEn)
    : categoryName;

  // Sort fields by display order
  const sortedFields = [...fields].sort((a, b) => {
    const orderA = a.displayOrder ?? 999;
    const orderB = b.displayOrder ?? 999;
    return orderA - orderB;
  });

  return (
    <Card className="border-0 shadow-lg rounded-xl overflow-hidden hover-glow transition-all-medium">
      <CardHeader className="bg-gradient-to-r from-primary/10 to-accent/10">
        <CardTitle className="flex items-center justify-between">
          <span className="text-xl font-semibold text-primary">
            {categoryTitle}
          </span>
          <div className="flex gap-2">
            {fields.length > 0 && (
              <Badge variant="secondary">
                {fields.length} field{fields.length !== 1 ? 's' : ''}
              </Badge>
            )}
            {labels.length > 0 && (
              <Badge variant="outline">
                {labels.length} label{labels.length !== 1 ? 's' : ''}
              </Badge>
            )}
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-6">
        {/* Render dynamic fields */}
        {sortedFields.length > 0 && (
          <div className="space-y-4">
            <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
              Dynamic Fields
            </h4>
            <div className="grid gap-4 md:grid-cols-2">
              {sortedFields.map((fieldConfig) => (
                <div key={fieldConfig.id} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-muted-foreground">
                      {fieldConfig.fieldKey}
                    </span>
                    <Badge 
                      variant={fieldConfig.isRequired ? "destructive" : "secondary"}
                      className="text-xs"
                    >
                      {fieldConfig.fieldType}
                      {fieldConfig.isRequired && " *"}
                    </Badge>
                  </div>
                  <DynamicFormField
                    control={form.control}
                    name={fieldConfig.fieldKey as any}
                    configName={fieldConfig.configName}
                    language={language}
                    required={fieldConfig.isRequired}
                    className="border rounded-lg p-3 bg-gray-50/50"
                  />
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Show available labels for this category */}
        {labels.length > 0 && (
          <div className="space-y-4 mt-6">
            <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
              Available Labels
            </h4>
            <div className="grid gap-2 md:grid-cols-2">
              {labels.map((label) => (
                <div key={label.id} className="p-3 border rounded-lg bg-blue-50/50">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-blue-900">
                      {label.key}
                    </span>
                    <Badge variant="outline" className="text-xs">
                      {label.context}
                    </Badge>
                  </div>
                  <div className="space-y-1">
                    <div className="text-sm">
                      <span className="font-medium">EL:</span> {label.labelEl}
                    </div>
                    <div className="text-sm">
                      <span className="font-medium">EN:</span> {label.labelEn}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Show message if category has no content */}
        {fields.length === 0 && labels.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <p>No dynamic content available for this category.</p>
            <p className="text-sm mt-1">
              Add fields or labels through the admin panel to see them here.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * Component to show a preview of all available dynamic content
 */
export function DynamicContentPreview() {
  const { configs, loading: configsLoading } = useFormConfigs();
  const { categories, loading: labelsLoading } = useFormLabelCategories();

  if (configsLoading || labelsLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading dynamic content...</span>
      </div>
    );
  }

  const candidateConfigs = configs.filter(c => c.formType === "candidate" || c.formType === "both");
  const nannyRequestConfigs = configs.filter(c => c.formType === "nannyRequest" || c.formType === "both");
  const candidateCategories = categories.filter(c => c.formType === "candidate" || c.formType === "both");
  const nannyRequestCategories = categories.filter(c => c.formType === "nannyRequest" || c.formType === "both");

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Candidate Form Content</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Dynamic Fields:</span>
                <Badge>{candidateConfigs.length}</Badge>
              </div>
              <div className="flex justify-between">
                <span>Label Categories:</span>
                <Badge>{candidateCategories.length}</Badge>
              </div>
              <div className="flex justify-between">
                <span>Total Labels:</span>
                <Badge>{candidateCategories.reduce((sum, cat) => sum + cat.labels.length, 0)}</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Nanny Request Form Content</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Dynamic Fields:</span>
                <Badge>{nannyRequestConfigs.length}</Badge>
              </div>
              <div className="flex justify-between">
                <span>Label Categories:</span>
                <Badge>{nannyRequestCategories.length}</Badge>
              </div>
              <div className="flex justify-between">
                <span>Total Labels:</span>
                <Badge>{nannyRequestCategories.reduce((sum, cat) => sum + cat.labels.length, 0)}</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
