import { useFormConfigs } from "@/hooks/useFormConfig";
import { useFormLabelCategories } from "@/hooks/useFormLabels";
import { FormFieldConfig, FormLabelCategory } from "@/schemas/FormSchema";
import { cn } from "@/lib/utils";
import { useFormContext } from "react-hook-form";
import { DynamicFormField } from "./DynamicFormField";
import { Card, CardContent } from "../ui/card";
import { Badge } from "../ui/badge";
import { Loader2, ChevronDown, ChevronUp } from "lucide-react";
import { useState } from "react";
import { Button } from "../ui/button";

interface AutoFormRendererProps {
  formType: "candidate" | "nannyRequest";
  language: "el" | "en";
  className?: string;
  showPreview?: boolean;
}

/**
 * Component that automatically renders a complete form based on available
 * dynamic fields and labels, without hardcoding any field names
 */
export function AutoFormRenderer({
  formType,
  language,
  className,
  showPreview = false,
}: AutoFormRendererProps) {
  const form = useFormContext();
  const { configs, loading: configsLoading } = useFormConfigs();
  const { categories, loading: labelsLoading } = useFormLabelCategories();
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());

  if (configsLoading || labelsLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading form...</span>
      </div>
    );
  }

  // Get all active configurations for this form type
  const activeConfigs = configs.filter(config => 
    config.isActive && 
    (config.formType === formType || config.formType === "both")
  );

  // Get all active label categories for this form type
  const activeCategories = categories.filter(category =>
    category.isActive &&
    (category.formType === formType || category.formType === "both")
  );

  // Group configurations by category
  const configsByCategory = activeConfigs.reduce((acc, config) => {
    if (!acc[config.category]) {
      acc[config.category] = [];
    }
    acc[config.category].push(config);
    return acc;
  }, {} as Record<string, FormFieldConfig[]>);

  // Get all categories that have either fields or labels
  const availableCategories = new Set([
    ...Object.keys(configsByCategory),
    ...activeCategories.map(cat => cat.name)
  ]);

  // Sort categories by a logical order (you can customize this)
  const categoryOrder = [
    'personalInfo',
    'address', 
    'children',
    'jobDetails',
    'skills',
    'education',
    'experience',
    'availability',
    'socialMedia'
  ];

  const sortedCategories = Array.from(availableCategories).sort((a, b) => {
    const indexA = categoryOrder.indexOf(a);
    const indexB = categoryOrder.indexOf(b);
    
    // If both are in the order array, sort by their position
    if (indexA !== -1 && indexB !== -1) {
      return indexA - indexB;
    }
    
    // If only one is in the order array, prioritize it
    if (indexA !== -1) return -1;
    if (indexB !== -1) return 1;
    
    // If neither is in the order array, sort alphabetically
    return a.localeCompare(b);
  });

  const toggleSection = (categoryName: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(categoryName)) {
      newExpanded.delete(categoryName);
    } else {
      newExpanded.add(categoryName);
    }
    setExpandedSections(newExpanded);
  };

  if (sortedCategories.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <p className="text-lg font-medium text-muted-foreground mb-2">
              No dynamic form content available
            </p>
            <p className="text-sm text-muted-foreground">
              Add fields and labels through the admin panel to build your {formType} form.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Form header with stats */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold">
            {formType === "candidate" ? "Candidate Application" : "Nanny Request"} Form
          </h2>
          <div className="flex gap-2">
            <Badge variant="outline">
              {activeConfigs.length} Fields
            </Badge>
            <Badge variant="outline">
              {sortedCategories.length} Sections
            </Badge>
          </div>
        </div>
        
        {showPreview && (
          <div className="text-sm text-muted-foreground">
            This form is automatically generated from your admin panel configuration.
            Any changes made in the admin panel will be reflected here immediately.
          </div>
        )}
      </div>

      {/* Render each category as a section */}
      {sortedCategories.map(categoryName => {
        const categoryConfig = activeCategories.find(cat => cat.name === categoryName);
        const categoryFields = configsByCategory[categoryName] || [];
        const isExpanded = expandedSections.has(categoryName);

        return (
          <AutoFormSection
            key={categoryName}
            categoryName={categoryName}
            categoryConfig={categoryConfig}
            fields={categoryFields}
            formType={formType}
            language={language}
            isExpanded={isExpanded}
            onToggle={() => toggleSection(categoryName)}
            showPreview={showPreview}
          />
        );
      })}
    </div>
  );
}

interface AutoFormSectionProps {
  categoryName: string;
  categoryConfig?: FormLabelCategory;
  fields: FormFieldConfig[];
  formType: "candidate" | "nannyRequest";
  language: "el" | "en";
  isExpanded: boolean;
  onToggle: () => void;
  showPreview?: boolean;
}

function AutoFormSection({
  categoryName,
  categoryConfig,
  fields,
  formType,
  language,
  isExpanded,
  onToggle,
  showPreview = false,
}: AutoFormSectionProps) {
  const form = useFormContext();

  // Get category title from labels or use category name as fallback
  const categoryTitle = categoryConfig 
    ? (language === "el" ? categoryConfig.titleEl : categoryConfig.titleEn)
    : categoryName.charAt(0).toUpperCase() + categoryName.slice(1);

  // Sort fields by display order
  const sortedFields = [...fields].sort((a, b) => {
    const orderA = a.displayOrder ?? 999;
    const orderB = b.displayOrder ?? 999;
    if (orderA !== orderB) {
      return orderA - orderB;
    }
    // If same order, sort by field key
    return a.fieldKey.localeCompare(b.fieldKey);
  });

  // Don't render section if no fields
  if (sortedFields.length === 0) {
    return null;
  }

  return (
    <Card className="border-0 shadow-lg rounded-xl overflow-hidden hover-glow transition-all-medium">
      {/* Section header */}
      <div 
        className="bg-gradient-to-r from-primary/10 to-accent/10 p-4 cursor-pointer"
        onClick={onToggle}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <h3 className="text-lg font-semibold text-primary">
              {categoryTitle}
            </h3>
            <Badge variant="secondary" className="text-xs">
              {sortedFields.length} field{sortedFields.length !== 1 ? 's' : ''}
            </Badge>
          </div>
          <Button variant="ghost" size="sm">
            {isExpanded ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </div>
        
        {showPreview && (
          <div className="mt-2 text-xs text-muted-foreground">
            Category: {categoryName} | Form Type: {categoryConfig?.formType || 'both'}
          </div>
        )}
      </div>

      {/* Section content */}
      {isExpanded && (
        <CardContent className="pt-6">
          <div className="grid gap-6 md:grid-cols-2">
            {sortedFields.map((fieldConfig) => (
              <div key={fieldConfig.id} className="space-y-2">
                {showPreview && (
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <span>Key: {fieldConfig.fieldKey}</span>
                    <div className="flex gap-1">
                      <Badge variant="outline" className="text-xs">
                        {fieldConfig.fieldType}
                      </Badge>
                      {fieldConfig.isRequired && (
                        <Badge variant="destructive" className="text-xs">
                          Required
                        </Badge>
                      )}
                    </div>
                  </div>
                )}
                
                <DynamicFormField
                  control={form.control}
                  name={fieldConfig.fieldKey as any}
                  configName={fieldConfig.configName}
                  language={language}
                  required={fieldConfig.isRequired}
                />
              </div>
            ))}
          </div>
        </CardContent>
      )}
    </Card>
  );
}

/**
 * Hook to get form statistics
 */
export function useFormStats(formType: "candidate" | "nannyRequest") {
  const { configs } = useFormConfigs();
  const { categories } = useFormLabelCategories();

  const relevantConfigs = configs.filter(config => 
    config.isActive && 
    (config.formType === formType || config.formType === "both")
  );

  const relevantCategories = categories.filter(category =>
    category.isActive &&
    (category.formType === formType || category.formType === "both")
  );

  const configsByCategory = relevantConfigs.reduce((acc, config) => {
    if (!acc[config.category]) {
      acc[config.category] = [];
    }
    acc[config.category].push(config);
    return acc;
  }, {} as Record<string, FormFieldConfig[]>);

  const totalLabels = relevantCategories.reduce((sum, cat) => sum + cat.labels.length, 0);

  return {
    totalFields: relevantConfigs.length,
    totalCategories: Object.keys(configsByCategory).length,
    totalLabels,
    requiredFields: relevantConfigs.filter(c => c.isRequired).length,
    optionalFields: relevantConfigs.filter(c => !c.isRequired).length,
    fieldsByType: relevantConfigs.reduce((acc, config) => {
      acc[config.fieldType] = (acc[config.fieldType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    categoriesWithFields: Object.keys(configsByCategory),
  };
}
