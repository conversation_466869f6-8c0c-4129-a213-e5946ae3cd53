import { useFormConfigs } from "@/hooks/useFormConfig";
import { useFormLabelCategories } from "@/hooks/useFormLabels";
import { useFormStep } from "@/hooks/useFormSteps";
import { cn } from "@/lib/utils";
import { FormFieldConfig } from "@/schemas/FormSchema";
import { AlertCircle, Loader2 } from "lucide-react";
import { useState } from "react";
import { useFormContext } from "react-hook-form";
import { Badge } from "../../ui/badge";
import { Button } from "../../ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "../../ui/card";
import { ConditionalFormField } from "../ConditionalFormField";

interface DynamicFormStepProps {
  stepKey: string;
  formType: "candidate" | "nannyRequest";
  language: "el" | "en";
  className?: string;
}

/**
 * Dynamic form step that renders fields based on database configuration
 * No hardcoded field names - everything is loaded from the database
 */
export function DynamicFormStep({
  stepKey,
  formType,
  language,
  className,
}: DynamicFormStepProps) {
  const form = useFormContext();
  const { step, loading: stepLoading, error: stepError } = useFormStep(stepKey, formType);
  const { configs, loading: configsLoading, error: configsError } = useFormConfigs();
  const { categories, loading: labelsLoading, error: labelsError } = useFormLabelCategories();

  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());

  const loading = stepLoading || configsLoading || labelsLoading;
  const error = stepError || configsError || labelsError;

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading step configuration...</span>
      </div>
    );
  }

  if (error || !step) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardContent className="pt-6">
          <div className="flex items-center gap-2 text-red-600">
            <AlertCircle className="h-5 w-5" />
            <p>
              Error loading step: {error || `Step "${stepKey}" not found`}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Get step title and description
  const stepTitle = language === "el" ? step.titleEl : step.titleEn;
  const stepDescription = language === "el" ? step.descriptionEl : step.descriptionEn;

  // Get categories for this step
  const stepCategoryNames = step.categories.map(cat => cat.categoryName);

  // Filter configurations for this step's categories
  const relevantConfigs = configs.filter(config =>
    config.isActive &&
    (config.formType === formType || config.formType === "both") &&
    stepCategoryNames.includes(config.category)
  );

  // Group configurations by category
  const configsByCategory = relevantConfigs.reduce((acc, config) => {
    if (!acc[config.category]) {
      acc[config.category] = [];
    }
    acc[config.category].push(config);
    return acc;
  }, {} as Record<string, FormFieldConfig[]>);

  // Sort categories by the order defined in the step
  const sortedCategories = step.categories
    .filter(cat => cat.isActive && configsByCategory[cat.categoryName])
    .sort((a, b) => a.categoryOrder - b.categoryOrder);

  const toggleCategory = (categoryName: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(categoryName)) {
      newExpanded.delete(categoryName);
    } else {
      newExpanded.add(categoryName);
    }
    setExpandedCategories(newExpanded);
  };

  const expandAll = () => {
    setExpandedCategories(new Set(sortedCategories.map(cat => cat.categoryName)));
  };

  const collapseAll = () => {
    setExpandedCategories(new Set());
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Step Header */}
      <Card className="border-0 shadow-lg rounded-xl overflow-hidden">
        <div className="h-2 bg-gradient-to-r from-primary to-accent-foreground animate-shimmer"></div>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-2xl font-bold text-primary">
                {stepTitle}
              </CardTitle>
              {stepDescription && (
                <p className="text-muted-foreground mt-2">
                  {stepDescription}
                </p>
              )}
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="secondary">
                {relevantConfigs.length} fields
              </Badge>
              <Badge variant="outline">
                {sortedCategories.length} categories
              </Badge>
            </div>
          </div>

          {sortedCategories.length > 1 && (
            <div className="flex gap-2 mt-4">
              <Button variant="outline" size="sm" onClick={expandAll}>
                Expand All
              </Button>
              <Button variant="outline" size="sm" onClick={collapseAll}>
                Collapse All
              </Button>
            </div>
          )}
        </CardHeader>
      </Card>

      {/* Categories */}
      {sortedCategories.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <AlertCircle className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No Fields Configured</h3>
              <p className="text-muted-foreground">
                No dynamic fields are configured for this step.
              </p>
              <p className="text-sm text-muted-foreground mt-2">
                Use the admin panel to add fields to this step.
              </p>
            </div>
          </CardContent>
        </Card>
      ) : (
        sortedCategories.map(stepCategory => {
          const categoryFields = configsByCategory[stepCategory.categoryName] || [];
          const categoryConfig = categories.find(cat => cat.name === stepCategory.categoryName);
          const isExpanded = expandedCategories.has(stepCategory.categoryName) || sortedCategories.length === 1;

          // Get category title
          const categoryTitle = categoryConfig
            ? (language === "el" ? categoryConfig.titleEl : categoryConfig.titleEn)
            : stepCategory.categoryName.charAt(0).toUpperCase() + stepCategory.categoryName.slice(1);

          // Sort fields by display order
          const sortedFields = [...categoryFields].sort((a, b) => {
            const orderA = a.displayOrder ?? 999;
            const orderB = b.displayOrder ?? 999;
            if (orderA !== orderB) {
              return orderA - orderB;
            }
            return a.fieldKey.localeCompare(b.fieldKey);
          });

          return (
            <Card key={stepCategory.categoryName} className="border-0 shadow-lg rounded-xl overflow-hidden">
              {/* Category Header */}
              <div
                className="bg-gradient-to-r from-primary/10 to-accent/10 p-4 cursor-pointer hover:from-primary/15 hover:to-accent/15 transition-colors"
                onClick={() => toggleCategory(stepCategory.categoryName)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <h3 className="text-lg font-semibold text-primary">
                      {categoryTitle}
                    </h3>
                    <Badge variant="secondary">
                      {sortedFields.length} field{sortedFields.length !== 1 ? 's' : ''}
                    </Badge>
                  </div>
                  {sortedCategories.length > 1 && (
                    <Button variant="ghost" size="sm">
                      {isExpanded ? "Hide" : "Show"}
                    </Button>
                  )}
                </div>
              </div>

              {/* Category Content */}
              {isExpanded && (
                <CardContent className="pt-6">
                  {sortedFields.length > 0 ? (
                    <div className="grid gap-6 md:grid-cols-2">
                      {sortedFields.map((fieldConfig) => (
                        <div key={fieldConfig.id} className="space-y-2">
                          <ConditionalFormField
                            config={fieldConfig}
                            language={language}
                          />
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <p>No fields configured for this category.</p>
                      <p className="text-sm mt-1">
                        Add fields through the admin panel.
                      </p>
                    </div>
                  )}
                </CardContent>
              )}
            </Card>
          );
        })
      )}
    </div>
  );
}
