import React from "react";
import { useFormSteps } from "@/hooks/useFormSteps";
import { useCandidateFormContext } from "@/contexts/CandidateFormContext";
import { useNannyRequestFormContext } from "@/contexts/NannyRequestFormContext";
import { DynamicFormStep } from "./DynamicFormStep";
import { Card, CardContent } from "../../ui/card";
import { Loader2, AlertCircle } from "lucide-react";

interface DynamicStepManagerProps {
  formType: "candidate" | "nannyRequest";
}

export const DynamicStepManager: React.FC<DynamicStepManagerProps> = ({ formType }) => {
  const { steps, loading, error } = useFormSteps(formType);
  
  // Get the appropriate context based on form type
  const candidateContext = formType === "candidate" ? useCandidateFormContext() : null;
  const nannyContext = formType === "nannyRequest" ? useNannyRequestFormContext() : null;
  
  const currentStep = candidateContext?.currentStep ?? nannyContext?.currentStep ?? 0;
  const language = candidateContext?.language ?? nannyContext?.language ?? "en";

  if (loading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center p-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading form steps...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || steps.length === 0) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardContent className="pt-6">
          <div className="flex items-center gap-2 text-red-600">
            <AlertCircle className="h-5 w-5" />
            <p>
              {error || `No steps configured for ${formType} forms`}
            </p>
          </div>
          <p className="text-sm text-muted-foreground mt-2">
            Please configure form steps in the admin panel.
          </p>
        </CardContent>
      </Card>
    );
  }

  // Get the current step configuration
  const currentStepConfig = steps[Math.min(currentStep, steps.length - 1)];

  if (!currentStepConfig) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardContent className="pt-6">
          <div className="flex items-center gap-2 text-red-600">
            <AlertCircle className="h-5 w-5" />
            <p>Step configuration not found</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="mt-6">
      <DynamicFormStep
        stepKey={currentStepConfig.stepKey}
        formType={formType}
        language={language}
      />
    </div>
  );
};
