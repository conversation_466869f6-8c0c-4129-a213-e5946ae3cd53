import { useFormConfigs } from "@/hooks/useFormConfig";
import { useFormLabelCategories } from "@/hooks/useFormLabels";
import { FormFieldConfig, FormLabelCategory } from "@/schemas/FormSchema";
import { cn } from "@/lib/utils";
import { useFormContext } from "react-hook-form";
import { DynamicFormField } from "../DynamicFormField";
import { Card, CardContent, CardHeader, CardTitle } from "../../ui/card";
import { Badge } from "../../ui/badge";
import { Loader2, Eye, EyeOff, Settings, Info } from "lucide-react";
import { useState } from "react";
import { Button } from "../../ui/button";
import { Switch } from "../../ui/switch";
import { Label } from "../../ui/label";

interface AutoDetectFormStepProps {
  formType: "candidate" | "nannyRequest";
  language: "el" | "en";
  className?: string;
}

/**
 * Form step that automatically detects and renders all available dynamic fields
 * and labels without hardcoding any field names. Perfect for testing and 
 * demonstrating the dynamic form system.
 */
export function AutoDetectFormStep({
  formType,
  language,
  className,
}: AutoDetectFormStepProps) {
  const form = useFormContext();
  const { configs, loading: configsLoading, error: configsError } = useFormConfigs();
  const { categories, loading: labelsLoading, error: labelsError } = useFormLabelCategories();
  
  const [showDebugInfo, setShowDebugInfo] = useState(false);
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());

  if (configsLoading || labelsLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading dynamic form content...</span>
      </div>
    );
  }

  if (configsError || labelsError) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardContent className="pt-6">
          <p className="text-red-600">
            Error loading dynamic content: {configsError || labelsError}
          </p>
        </CardContent>
      </Card>
    );
  }

  // Filter content for this form type
  const relevantConfigs = configs.filter(config => 
    config.isActive && 
    (config.formType === formType || config.formType === "both")
  );

  const relevantCategories = categories.filter(category =>
    category.isActive &&
    (category.formType === formType || category.formType === "both")
  );

  // Group configurations by category
  const configsByCategory = relevantConfigs.reduce((acc, config) => {
    if (!acc[config.category]) {
      acc[config.category] = [];
    }
    acc[config.category].push(config);
    return acc;
  }, {} as Record<string, FormFieldConfig[]>);

  // Get all available categories
  const allCategories = Array.from(new Set([
    ...Object.keys(configsByCategory),
    ...relevantCategories.map(cat => cat.name)
  ])).sort();

  const toggleCategory = (categoryName: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(categoryName)) {
      newExpanded.delete(categoryName);
    } else {
      newExpanded.add(categoryName);
    }
    setExpandedCategories(newExpanded);
  };

  const expandAll = () => {
    setExpandedCategories(new Set(allCategories));
  };

  const collapseAll = () => {
    setExpandedCategories(new Set());
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header with controls */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Auto-Detected Form Content
              </CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                Showing all available dynamic fields and labels for {formType} forms
              </p>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="debug-mode"
                  checked={showDebugInfo}
                  onCheckedChange={setShowDebugInfo}
                />
                <Label htmlFor="debug-mode" className="text-sm">
                  Debug Info
                </Label>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" onClick={expandAll}>
                  <Eye className="h-4 w-4 mr-1" />
                  Expand All
                </Button>
                <Button variant="outline" size="sm" onClick={collapseAll}>
                  <EyeOff className="h-4 w-4 mr-1" />
                  Collapse All
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">{relevantConfigs.length}</div>
              <div className="text-sm text-muted-foreground">Dynamic Fields</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">{allCategories.length}</div>
              <div className="text-sm text-muted-foreground">Categories</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {relevantCategories.reduce((sum, cat) => sum + cat.labels.length, 0)}
              </div>
              <div className="text-sm text-muted-foreground">Labels</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {relevantConfigs.filter(c => c.isRequired).length}
              </div>
              <div className="text-sm text-muted-foreground">Required Fields</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Categories */}
      {allCategories.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <Info className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No Dynamic Content Found</h3>
              <p className="text-muted-foreground">
                No dynamic fields or labels are configured for {formType} forms.
              </p>
              <p className="text-sm text-muted-foreground mt-2">
                Use the admin panel to add fields and labels to see them here.
              </p>
            </div>
          </CardContent>
        </Card>
      ) : (
        allCategories.map(categoryName => {
          const categoryConfig = relevantCategories.find(cat => cat.name === categoryName);
          const categoryFields = configsByCategory[categoryName] || [];
          const categoryLabels = categoryConfig?.labels || [];
          const isExpanded = expandedCategories.has(categoryName);

          return (
            <AutoDetectedCategory
              key={categoryName}
              categoryName={categoryName}
              categoryConfig={categoryConfig}
              fields={categoryFields}
              labels={categoryLabels}
              formType={formType}
              language={language}
              isExpanded={isExpanded}
              onToggle={() => toggleCategory(categoryName)}
              showDebugInfo={showDebugInfo}
            />
          );
        })
      )}
    </div>
  );
}

interface AutoDetectedCategoryProps {
  categoryName: string;
  categoryConfig?: FormLabelCategory;
  fields: FormFieldConfig[];
  labels: any[];
  formType: "candidate" | "nannyRequest";
  language: "el" | "en";
  isExpanded: boolean;
  onToggle: () => void;
  showDebugInfo: boolean;
}

function AutoDetectedCategory({
  categoryName,
  categoryConfig,
  fields,
  labels,
  formType,
  language,
  isExpanded,
  onToggle,
  showDebugInfo,
}: AutoDetectedCategoryProps) {
  const form = useFormContext();

  // Get category title
  const categoryTitle = categoryConfig 
    ? (language === "el" ? categoryConfig.titleEl : categoryConfig.titleEn)
    : categoryName.charAt(0).toUpperCase() + categoryName.slice(1);

  // Sort fields by display order
  const sortedFields = [...fields].sort((a, b) => {
    const orderA = a.displayOrder ?? 999;
    const orderB = b.displayOrder ?? 999;
    if (orderA !== orderB) {
      return orderA - orderB;
    }
    return a.fieldKey.localeCompare(b.fieldKey);
  });

  const hasContent = sortedFields.length > 0 || labels.length > 0;

  if (!hasContent) {
    return null;
  }

  return (
    <Card className="border-0 shadow-lg rounded-xl overflow-hidden">
      {/* Category Header */}
      <div 
        className="bg-gradient-to-r from-primary/10 to-accent/10 p-4 cursor-pointer hover:from-primary/15 hover:to-accent/15 transition-colors"
        onClick={onToggle}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <h3 className="text-lg font-semibold text-primary">
              {categoryTitle}
            </h3>
            <div className="flex gap-2">
              {sortedFields.length > 0 && (
                <Badge variant="secondary">
                  {sortedFields.length} field{sortedFields.length !== 1 ? 's' : ''}
                </Badge>
              )}
              {labels.length > 0 && (
                <Badge variant="outline">
                  {labels.length} label{labels.length !== 1 ? 's' : ''}
                </Badge>
              )}
            </div>
          </div>
          <Button variant="ghost" size="sm">
            {isExpanded ? "Hide" : "Show"}
          </Button>
        </div>
        
        {showDebugInfo && (
          <div className="mt-2 text-xs text-muted-foreground space-y-1">
            <div>Category Key: <code className="bg-black/10 px-1 rounded">{categoryName}</code></div>
            <div>Form Type: <code className="bg-black/10 px-1 rounded">{categoryConfig?.formType || 'both'}</code></div>
            <div>Has Config: {categoryConfig ? '✅' : '❌'}</div>
          </div>
        )}
      </div>

      {/* Category Content */}
      {isExpanded && (
        <CardContent className="pt-6">
          {/* Render Dynamic Fields */}
          {sortedFields.length > 0 && (
            <div className="space-y-4 mb-6">
              <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
                Dynamic Form Fields ({sortedFields.length})
              </h4>
              <div className="grid gap-4 md:grid-cols-2">
                {sortedFields.map((fieldConfig) => (
                  <div key={fieldConfig.id} className="space-y-2">
                    {showDebugInfo && (
                      <div className="text-xs text-muted-foreground bg-gray-50 p-2 rounded">
                        <div><strong>Key:</strong> {fieldConfig.fieldKey}</div>
                        <div><strong>Type:</strong> {fieldConfig.fieldType}</div>
                        <div><strong>Required:</strong> {fieldConfig.isRequired ? 'Yes' : 'No'}</div>
                        <div><strong>Config:</strong> {fieldConfig.configName}</div>
                        {fieldConfig.options && (
                          <div><strong>Options:</strong> {fieldConfig.options.length} items</div>
                        )}
                      </div>
                    )}
                    
                    <DynamicFormField
                      control={form.control}
                      name={fieldConfig.fieldKey as any}
                      configName={fieldConfig.configName}
                      language={language}
                      required={fieldConfig.isRequired}
                    />
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Show Available Labels */}
          {labels.length > 0 && (
            <div className="space-y-4">
              <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
                Available Labels ({labels.length})
              </h4>
              <div className="grid gap-3 md:grid-cols-2">
                {labels.map((label) => (
                  <div key={label.id} className="p-3 border rounded-lg bg-blue-50/50">
                    <div className="flex items-center justify-between mb-2">
                      <code className="text-sm font-mono text-blue-900">
                        {label.key}
                      </code>
                      <Badge variant="outline" className="text-xs">
                        {label.context}
                      </Badge>
                    </div>
                    <div className="space-y-1 text-sm">
                      <div><span className="font-medium">EL:</span> {label.labelEl}</div>
                      <div><span className="font-medium">EN:</span> {label.labelEn}</div>
                    </div>
                    {showDebugInfo && (
                      <div className="mt-2 text-xs text-muted-foreground">
                        Form Type: {label.formType || 'both'}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Empty state */}
          {sortedFields.length === 0 && labels.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <p>No content available for this category.</p>
              <p className="text-sm mt-1">
                Add fields or labels through the admin panel.
              </p>
            </div>
          )}
        </CardContent>
      )}
    </Card>
  );
}
