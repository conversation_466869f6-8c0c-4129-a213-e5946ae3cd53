import { Trash2 } from "lucide-react";
import React from "react";
import {
  useFieldArray,
  useFormContext as useRHFFormContext,
} from "react-hook-form";
import { useCandidateFormContext } from "../../../../contexts/CandidateFormContext";
import {
  FormData,
  socialMediaPlatformOptions,
} from "../../../../schemas/FormSchema";
import { getText } from "../../../../utils/form-utils";
import { But<PERSON> } from "../../../ui/button";
import { Card, CardContent } from "../../../ui/card";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../../ui/form";
import { Input } from "../../../ui/input";
import { RadioGroup, RadioGroupItem } from "../../../ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../ui/select";
import { Textarea } from "../../../ui/textarea";
import { SearchableCitySelect } from "../../fields/SearchableCitySelect";

export const PersonalInfoStep: React.FC = () => {
  const { language } = useCandidateFormContext();
  const form = useRHFFormContext<FormData>();

  const {
    fields: socialMediaFields,
    append: appendSocialMedia,
    remove: removeSocialMedia,
  } = useFieldArray({
    control: form.control,
    name: "socialMedia",
  });

  const watchAllergies = form.watch("allergies.hasAllergies");

  return (
    <>
      {/* Personal Information */}
      <Card className="mb-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <CardContent className="pt-6">
          <h2 className="text-xl font-semibold mb-4">
            {getText("Προσωπικά Στοιχεία / Personal Information", language)}
          </h2>

          <div className="grid gap-6 md:grid-cols-2">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{getText("Όνομα / Name", language)}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={getText("Όνομα / Name", language)}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="surname"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {getText("Επίθετο / Surname", language)}
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder={getText("Επίθετο / Surname", language)}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>

          <div className="grid gap-6 md:grid-cols-2 mt-4">
            <FormField
              control={form.control}
              name="birthDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {getText("Ημερομηνία Γέννησης / Birth Date", language)}
                  </FormLabel>
                  <FormControl>
                    <Input type="date" {...field} />
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="contactNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {getText("Τηλέφωνο / Contact Number", language)}
                  </FormLabel>
                  <FormDescription>
                    {getText(
                      "Με κωδικό περιοχής ή χώρας / With Area or Country Code",
                      language
                    )}
                  </FormDescription>
                  <FormControl>
                    <Input
                      type="tel"
                      placeholder="+30 ************"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>

          <div className="grid gap-6 md:grid-cols-2 mt-4">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>E-mail</FormLabel>
                  <FormControl>
                    <Input placeholder="<EMAIL>" {...field} />
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="gender"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>{getText("Φύλο / Gender", language)}</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="flex flex-row space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="Male" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Άνδρας / Male", language)}
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="Female" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Γυναίκα / Female", language)}
                        </FormLabel>
                      </FormItem>

                    </RadioGroup>
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>
          <div className="mt-4">
            <FormField
              control={form.control}
              name="nationality"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {getText(
                      "Εθνικότητα-Υπηκοότητα / Nationality-Citizenship",
                      language
                    )}
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder={getText(
                        "Εθνικότητα-Υπηκοότητα / Nationality-Citizenship",
                        language
                      )}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>

          <div className="mt-4">
            <FormField
              control={form.control}
              name="workDocuments"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>
                    {getText(
                      "Υπάρχουν τα κατάλληλα έγγραφα για εργασία στην Ελλάδα; / Do you have the documents required to work in Greece?",
                      language
                    )}
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="flex flex-col space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="yes" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Ναι / Yes", language)}
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="no" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Όχι / No", language)}
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>

          {/* Social Media Fields */}
          <div className="mt-6">
            <FormLabel>
              {getText("Social Media (Προαιρετικό) / Social Media (Optional)", language)}
            </FormLabel>
            {socialMediaFields.map((item, index) => (
              <div
                key={item.id}
                className="flex flex-col md:flex-row gap-4 items-start mt-2 p-4 border rounded-md"
              >
                <FormField
                  control={form.control}
                  name={`socialMedia.${index}.platform`}
                  render={({ field }) => (
                    <FormItem className="flex-1 w-full md:w-auto">
                      <FormLabel>
                        {getText("Πλατφόρμα / Platform", language)}
                      </FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue
                              placeholder={getText(
                                "Επιλέξτε πλατφόρμα / Select platform",
                                language
                              )}
                            />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {socialMediaPlatformOptions.map((option) => (
                            <SelectItem key={option.id} value={option.id}>
                              {language === "el" ? option.labelEl : option.labelEn}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage lang={language} />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name={`socialMedia.${index}.handle`}
                  render={({ field }) => (
                    <FormItem className="flex-1 w-full md:w-auto">
                      <FormLabel>
                        {getText(
                          "Όνομα χρήστη / Handle or Link",
                          language
                        )}
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder={getText(
                            "π.χ. yourprofile / eg. yourprofile.com",
                            language
                          )}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage lang={language} />
                    </FormItem>
                  )}
                />
                <Button
                  type="button"
                  size="icon"
                  variant="ghost"
                  onClick={() => removeSocialMedia(index)}
                  className="mt-2 md:mt-6"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>

              </div>
            ))}
            <Button
              type="button"
              variant="outline"
              onClick={() => appendSocialMedia({ platform: "", handle: "" })}
              className="mt-4 ml-2"
            >
              {getText("Προσθήκη Social Media / Add Social Media", language)}
            </Button>
          </div>

          {/* Allergies Field */}
          <div className="mt-6">
            <FormField
              control={form.control}
              name="allergies.hasAllergies"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>
                    {getText(
                      "Έχετε αλλεργίες; (Προαιρετικό) / Do you have any allergies? (Optional)",
                      language
                    )}
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={(value) => {
                        field.onChange(value);
                        if (value === "no") {
                          form.setValue("allergies.allergyDetails", "");
                        }
                      }}
                      value={field.value || "no"}
                      className="flex flex-col space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="yes" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Ναι / Yes", language)}
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="no" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Όχι / No", language)}
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
            {watchAllergies === "yes" && (
              <FormField
                control={form.control}
                name="allergies.allergyDetails"
                render={({ field }) => (
                  <FormItem className="mt-4">
                    <FormLabel>
                      {getText(
                        "Παρακαλώ περιγράψτε τις αλλεργίες σας / Please describe your allergies",
                        language
                      )}
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder={getText(
                          "π.χ. Αλλεργία στη γλουτένη, φιστίκια / e.g., Gluten allergy, peanuts",
                          language
                        )}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage lang={language} />
                  </FormItem>
                )}
              />
            )}
          </div>

          {/* Passport Active Field */}
          <div className="mt-6">
            <FormField
              control={form.control}
              name="passportActive"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>
                    {getText(
                      "Έχετε ενεργό διαβατήριο; (Προαιρετικό) / Do you have an active passport? (Optional)",
                      language
                    )}
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value || "no"}
                      className="flex flex-col space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="yes" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Ναι / Yes", language)}
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="no" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Όχι / No", language)}
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>
        </CardContent>
      </Card>

      {/* Address */}
      <Card className="mt-6">
        <CardContent className="pt-6">
          <h2 className="text-xl font-semibold mb-4">
            {getText("Διεύθυνση / Address", language)}
          </h2>

          <div className="grid gap-6 md:grid-cols-2">
            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {getText("Διεύθυνση / Address", language)}
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder={getText("Οδός / Street", language)}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="addressNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{getText("Αριθμός / Number", language)}</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder={getText("Αριθμός / Number", language)}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>

          <div className="grid gap-6 md:grid-cols-2 mt-4">
            <FormField
              control={form.control}
              name="area"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{getText("Περιοχή / Area", language)}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={getText("Περιοχή / Area", language)}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="postalCode"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {getText("Ταχυδρομικός Κώδικας / Postal Code", language)}
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder={getText("Τ.Κ. / Postal Code", language)}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>

          <div className="grid gap-6 md:grid-cols-2 mt-4">
            <SearchableCitySelect
              form={form}
              name="city"
              language={language}
              required={true}
            />

            <FormField
              control={form.control}
              name="country"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{getText("Χώρα / Country", language)}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={getText("Χώρα / Country", language)}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>
        </CardContent>
      </Card>
    </>
  );
};
