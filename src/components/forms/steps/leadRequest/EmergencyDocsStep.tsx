import React, { useRef } from "react";
import { useFormContext as useRHFFormContext } from "react-hook-form";
import { useCandidateFormContext } from "../../../../contexts/CandidateFormContext";
import { FormData } from "../../../../schemas/FormSchema";
import { getText } from "../../../../utils/form-utils";
import { Card, CardContent } from "../../../ui/card";
import { Checkbox } from "../../../ui/checkbox";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../../ui/form";
import { Input } from "../../../ui/input";

export const EmergencyDocsStep: React.FC = () => {
  const { language } = useCandidateFormContext();
  const form = useRHFFormContext<FormData>();
  const profilePhotoRef = useRef<HTMLInputElement>(null);
  const introVideoRef = useRef<HTMLInputElement>(null);
  const cvRef = useRef<HTMLInputElement>(null);
  const idPassportRef = useRef<HTMLInputElement>(null);
  const educationDocumentsRef = useRef<HTMLInputElement>(null);
  const referenceLettersRef = useRef<HTMLInputElement>(null);
  const drivingLicenseDocumentRef = useRef<HTMLInputElement>(null);
  const criminalRecordDocumentRef = useRef<HTMLInputElement>(null);

  return (
    <>
      {/* Emergency Contact Section */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <h2 className="text-xl font-semibold mb-4">
            {getText("Επαφή Έκτακτης Ανάγκης / Emergency Contact", language)}
          </h2>

          <div className="grid gap-6 md:grid-cols-2">
            <FormField
              control={form.control}
              name="emergencyContactName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {getText("Ονοματεπώνυμο / Full Name", language)}
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder={getText(
                        "Ονοματεπώνυμο / Full Name",
                        language
                      )}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="emergencyContactRelation"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {getText("Είδος σχέσης / Kind of Relationship", language)}
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder={getText(
                        "π.χ. Σύζυγος, Γονέας, Αδερφός / e.g. Spouse, Parent, Sibling",
                        language
                      )}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="emergencyContactNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {getText("Τηλέφωνο / Contact Number", language)}
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="+30 ************"
                      inputMode="tel"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="emergencyContactEmail"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{getText("E-mail", language)}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="<EMAIL>"
                      inputMode="email"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>
        </CardContent>
      </Card>

      <Card className="mb-6">
        <CardContent className="pt-6">
          <h2 className="text-xl font-semibold mb-4">
            {getText("Έγγραφα & Όροι / Documents & Terms", language)}
          </h2>

          <div className="grid gap-6 md:grid-cols-2">
            <FormField
              control={form.control}
              name="profilePhoto"
              render={({ field: { onChange, ...fieldProps } }) => (
                <FormItem>
                  <FormLabel>
                    {getText("Φωτογραφία Προφίλ / Profile Photo", language)}
                  </FormLabel>
                  <FormDescription>
                    {getText("Προαιρετικό / Optional", language)}
                  </FormDescription>
                  <FormControl>
                    <Input
                      type="file"
                      accept="image/*"
                      ref={profilePhotoRef}
                      onChange={(e) => {
                        // Store the actual File object, not just the name
                        const file = e.target.files?.[0] || null;
                        onChange(file);
                      }}
                      {...fieldProps}
                      value={undefined} // React needs this to handle file inputs
                    />
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="introVideo"
              render={({ field: { onChange, ...fieldProps } }) => (
                <FormItem>
                  <FormLabel>
                    {getText(
                      "Εισαγωγικό Video μέχρι 20 δευτερόλεπτα το πολύ / Introductory Video up to 20 seconds ",
                      language
                    )}
                  </FormLabel>
                  <FormDescription>
                    {getText(
                      "Προαιρετικό , μπορείτε και αργότερα / Optional you can also upload later",
                      language
                    )}
                  </FormDescription>
                  <FormControl>
                    <Input
                      type="file"
                      accept="video/*"
                      ref={introVideoRef}
                      onChange={(e) => {
                        // Store the actual File object, not just the name
                        const file = e.target.files?.[0] || null;
                        onChange(file);
                      }}
                      {...fieldProps}
                      value={undefined}
                    />
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="nannyCv"
              render={({ field: { onChange, ...fieldProps } }) => (
                <FormItem>
                  <FormLabel>{getText("Βιογραφικό / CV", language)}</FormLabel>
                  <FormDescription>
                    {getText("Προαιρετικό / Optional", language)}
                  </FormDescription>
                  <FormControl>
                    <Input
                      type="file"
                      accept=".pdf,.doc,.docx"
                      ref={cvRef}
                      onChange={(e) => {
                        // Store the actual File object, not just the name
                        const file = e.target.files?.[0] || null;
                        onChange(file);
                      }}
                      {...fieldProps}
                      value={undefined}
                    />
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="idPassport"
              render={({ field: { onChange, ...fieldProps } }) => (
                <FormItem>
                  <FormLabel>
                    {getText(
                      "Ταυτότητα ή Διαβατήριο / ID or Passport",
                      language
                    )}
                  </FormLabel>
                  <FormDescription>
                    {getText("Προαιρετικό / Optional", language)}
                  </FormDescription>
                  <FormControl>
                    <Input
                      type="file"
                      accept="image/*,.pdf"
                      ref={idPassportRef}
                      onChange={(e) => {
                        // Store the actual File object, not just the name
                        const file = e.target.files?.[0] || null;
                        onChange(file);
                      }}
                      {...fieldProps}
                      value={undefined}
                    />
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>

          <div className="grid gap-6 md:grid-cols-2 mt-4">
            <FormField
              control={form.control}
              name="educationDocuments"
              render={({ field: { onChange, ...fieldProps } }) => (
                <FormItem>
                  <FormLabel>
                    {getText(
                      "Επισύναψη αρχείων Εκπαίδευσης,Κατάρτισης και Πρώτων Βοηθειών / Please attach all or most of your education documents (including First Aid Certification)",
                      language
                    )}
                  </FormLabel>
                  <FormDescription>
                    {getText("Προαιρετικό / Optional", language)}
                  </FormDescription>
                  <FormControl>
                    <Input
                      type="file"
                      multiple
                      accept=".pdf,.doc,.docx,image/*"
                      ref={educationDocumentsRef}
                      onChange={(e) => {
                        // Store the actual FileList object, not just the names
                        const files = e.target.files || null;
                        onChange(files);
                      }}
                      {...fieldProps}
                      value={undefined}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="referenceLetters"
              render={({ field: { onChange, ...fieldProps } }) => (
                <FormItem>
                  <FormLabel>
                    {getText(
                      "Συστατικές Επιστολές / Reference Letters",
                      language
                    )}
                  </FormLabel>
                  <FormDescription>
                    {getText("Προαιρετικό / Optional", language)}
                  </FormDescription>
                  <FormControl>
                    <Input
                      type="file"
                      multiple
                      accept=".pdf,.doc,.docx,image/*"
                      ref={referenceLettersRef}
                      onChange={(e) => {
                        // Store the actual FileList object, not just the names
                        const files = e.target.files || null;
                        onChange(files);
                      }}
                      {...fieldProps}
                      value={undefined}
                    />
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>

          <div className="grid gap-6 md:grid-cols-2 mt-4">
            <FormField
              control={form.control}
              name="drivingLicenseDocument"
              render={({ field: { onChange, ...fieldProps } }) => (
                <FormItem>
                  <FormLabel>
                    {getText("Δίπλωμα Οδήγησης / Driving License", language)}
                  </FormLabel>
                  <FormDescription>
                    {getText("Προαιρετικό / Optional", language)}
                  </FormDescription>
                  <FormControl>
                    <Input
                      type="file"
                      accept="image/*,.pdf"
                      ref={drivingLicenseDocumentRef}
                      onChange={(e) => {
                        // Store the actual File object, not just the name
                        const file = e.target.files?.[0] || null;
                        onChange(file);
                      }}
                      {...fieldProps}
                      value={undefined}
                    />
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="criminalRecordDocument"
              render={({ field: { onChange, ...fieldProps } }) => (
                <FormItem>
                  <FormLabel>
                    {getText("Ποινικό Μητρώο / Criminal Record", language)}
                  </FormLabel>
                  <FormDescription>
                    {getText("Προαιρετικό / Optional", language)}
                  </FormDescription>
                  <FormControl>
                    <Input
                      type="file"
                      accept=".pdf,image/*"
                      ref={criminalRecordDocumentRef}
                      onChange={(e) => {
                        // Store the actual File object, not just the name
                        const file = e.target.files?.[0] || null;
                        onChange(file);
                      }}
                      {...fieldProps}
                      value={undefined}
                    />
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>


          <div className="mt-6">
            <FormField
              control={form.control}
              name="termsAccepted"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>
                      {getText(
                        "Αποδέχομαι τους όρους και τις προϋποθέσεις / I accept the terms and conditions",
                        language
                      )}
                    </FormLabel>
                    <FormDescription>
                      {getText(
                        "Δηλώνω ότι έχω διαβάσει και αποδέχομαι τους όρους χρήσης και την πολιτική απορρήτου. / I declare that I have read and accept the terms of use and privacy policy.",
                        language
                      )}
                    </FormDescription>
                  </div>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>
        </CardContent>
      </Card>
    </>
  );
};
