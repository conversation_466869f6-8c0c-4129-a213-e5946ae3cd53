import React from "react";
import { useFormContext as useRHFFormContext } from "react-hook-form";
import { useCandidateFormContext } from "../../../../contexts/CandidateFormContext";
import {
  candidateTypeOptions,
  childrenAgeOptions,
  educationOptions,
  firstAidOptions,
  FormData,
  lessonFormatOptions,
  musicalInstrumentOptions,
  musicTheoryOptions,
  positionOptions,
  specializationOptions,
} from "../../../../schemas/FormSchema";
import { getText } from "../../../../utils/form-utils";
import { Button } from "../../../ui/button";
import { Card, CardContent } from "../../../ui/card";
import { Checkbox } from "../../../ui/checkbox";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../../ui/form";
import { Input } from "../../../ui/input";
import { RadioGroup, RadioGroupItem } from "../../../ui/radio-group";
import { Textarea } from "../../../ui/textarea";
import { LanguageSelect } from "../../fields/LanguageSelect";

export const CareerProfileStep: React.FC = () => {
  const {
    language,
    languages,
    setLanguages,
    workExperience,
    setWorkExperience,
    references,
    setReferences,
  } = useCandidateFormContext();

  const form = useRHFFormContext<FormData>();

  // Language handlers are now handled by the LanguageSelect component

  // Work experience handlers
  const addWorkExperience = () => {
    setWorkExperience([
      ...workExperience,
      {
        period: "",
        location: "",
        position: "",
        children: "",
        schedule: "",
        endingReason: "",
      },
    ]);
  };

  const removeWorkExperience = (index: number) => {
    const newWorkExperience = [...workExperience];
    newWorkExperience.splice(index, 1);
    setWorkExperience(newWorkExperience);
  };

  const updateWorkExperience = (
    index: number,
    field:
      | "period"
      | "location"
      | "position"
      | "children"
      | "schedule"
      | "endingReason",
    value: string
  ) => {
    const newWorkExperience = [...workExperience];
    newWorkExperience[index][field] = value;
    setWorkExperience(newWorkExperience);
  };

  // References handlers
  const addReference = () => {
    setReferences([...references, { name: "", phone: "", email: "" }]);
  };

  const removeReference = (index: number) => {
    const newReferences = [...references];
    newReferences.splice(index, 1);
    setReferences(newReferences);
  };

  const updateReference = (
    index: number,
    field: "name" | "phone" | "email",
    value: string
  ) => {
    const newReferences = [...references];
    newReferences[index][field] = value;
    setReferences(newReferences);
  };

  return (
    <>
      {/* Experience Section */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <h2 className="text-xl font-semibold mb-4">
            {getText("Εμπειρία / Experience", language)}
          </h2>

          <div className="grid gap-6 md:grid-cols-1">
            <FormField
              control={form.control}
              name="experienceWithChildren"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>
                    {getText(
                      "Έχετε τουλάχιστον 3 χρόνια εργασιακή εμπειρία με παιδιά; / Do you have at least 3 years of working experience with children?",
                      language
                    )}
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="flex flex-col space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="yes" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Ναι / Yes", language)}
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="no" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Όχι / No", language)}
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>

          <div className="grid gap-6 md:grid-cols-2 mt-4">
            <FormField
              control={form.control}
              name="experienceLearningDifficulties"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>
                    {getText(
                      "Έχετε εμπειρία με παιδιά με μαθησιακές δυσκολίες; / Do you have working experience with children with learning difficulties?",
                      language
                    )}
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="flex flex-col space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="yes" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Ναι / Yes", language)}
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="no" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Όχι / No", language)}
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="experienceSpecialNeeds"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>
                    {getText(
                      "Έχετε εμπειρία με παιδιά με ειδικές ανάγκες; / Do you have working experience with special needs children?",
                      language
                    )}
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="flex flex-col space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="yes" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Ναι / Yes", language)}
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="no" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Όχι / No", language)}
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>

          <div className="grid gap-6 md:grid-cols-1 mt-4">
            <FormField
              control={form.control}
              name="yearsExperience"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {getText(
                      "Χρόνια ενασχόλησης με το επάγγελμα: / Years of professional experience:",
                      language
                    )}
                  </FormLabel>
                  <FormControl>
                    <Input type="number" min="0" {...field} />
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>

          <div className="mt-6">
            <FormField
              control={form.control}
              name="childrenAgeExperience"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {getText(
                      "Ηλικίες παιδιών που έχετε ασχοληθεί: / What is the age range of the children you have worked with?",
                      language
                    )}
                  </FormLabel>
                  <div className="grid grid-cols-2 gap-2 mt-2">
                    {childrenAgeOptions.map((option) => (
                      <FormItem
                        key={option.id}
                        className="flex flex-row items-start space-x-3 space-y-0"
                      >
                        <FormControl>
                          <Checkbox
                            checked={field.value?.includes(option.id)}
                            onCheckedChange={(checked) => {
                              const updatedValue = checked
                                ? [...(field.value || []), option.id]
                                : (field.value || []).filter(
                                  (value) => value !== option.id
                                );
                              field.onChange(updatedValue);
                            }}
                          />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {language === "el" ? option.labelEl : option.labelEn}
                        </FormLabel>
                      </FormItem>
                    ))}
                  </div>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>
        </CardContent>
      </Card>

      {/* Work Experience Section */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <h2 className="text-xl font-semibold mb-4">
            {getText("Εργασιακή Εμπειρία / Work Experience", language)}
          </h2>

          <FormDescription className="mb-4">
            {getText(
              "Παρακαλώ προσθέστε τις προηγούμενες θέσεις εργασίας σας / Please add your previous work positions",
              language
            )}
          </FormDescription>

          {workExperience.map((exp: any, index: number) => (
            <div key={index} className="mb-6 p-4 border rounded-md">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-md font-medium">
                  {getText("Εμπειρία / Experience", language)} #{index + 1}
                </h3>
                {index > 0 && (
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    onClick={() => removeWorkExperience(index)}
                  >
                    {getText("Αφαίρεση / Remove", language)}
                  </Button>
                )}
              </div>

              <div className="grid gap-4 mb-4">
                <div>
                  <FormLabel>
                    {getText("Περίοδος / Period", language)}
                  </FormLabel>
                  <Input
                    placeholder={getText(
                      "π.χ. Ιανουάριος 2020 - Δεκέμβριος 2021 / e.g. January 2020 - December 2021",
                      language
                    )}
                    value={exp.period}
                    onChange={(e) =>
                      updateWorkExperience(index, "period", e.target.value)
                    }
                  />
                </div>

                <div>
                  <FormLabel>
                    {getText("Τοποθεσία / Location", language)}
                  </FormLabel>
                  <Input
                    placeholder={getText(
                      "π.χ. Αθήνα, Ελλάδα / e.g. Athens, Greece",
                      language
                    )}
                    value={exp.location}
                    onChange={(e) =>
                      updateWorkExperience(index, "location", e.target.value)
                    }
                  />
                </div>

                <div>
                  <FormLabel>{getText("Θέση / Position", language)}</FormLabel>
                  <Input
                    placeholder={getText(
                      "π.χ. Nanny πλήρους απασχόλησης / e.g. Full-time Nanny",
                      language
                    )}
                    value={exp.position}
                    onChange={(e) =>
                      updateWorkExperience(index, "position", e.target.value)
                    }
                  />
                </div>

                <div>
                  <FormLabel>
                    {getText("Παιδιά / Children", language)}
                  </FormLabel>
                  <Input
                    placeholder={getText(
                      "π.χ. 2 παιδιά, 3 και 5 ετών / e.g. 2 children, 3 and 5 years old",
                      language
                    )}
                    value={exp.children}
                    onChange={(e) =>
                      updateWorkExperience(index, "children", e.target.value)
                    }
                  />
                </div>

                <div>
                  <FormLabel>
                    {getText("Ωράριο / Schedule", language)}
                  </FormLabel>
                  <Input
                    placeholder={getText(
                      "π.χ. Δευτέρα-Παρασκευή, 9πμ-5μμ / e.g. Monday-Friday, 9am-5pm",
                      language
                    )}
                    value={exp.schedule}
                    onChange={(e) =>
                      updateWorkExperience(index, "schedule", e.target.value)
                    }
                  />
                </div>

                <div>
                  <FormLabel>
                    {getText("Λόγος Αποχώρησης / Reason for Leaving", language)}
                  </FormLabel>
                  <Input
                    placeholder={getText(
                      "π.χ. Μετακόμιση οικογένειας / e.g. Family relocated",
                      language
                    )}
                    value={exp.endingReason}
                    onChange={(e) =>
                      updateWorkExperience(
                        index,
                        "endingReason",
                        e.target.value
                      )
                    }
                  />
                </div>
              </div>
            </div>
          ))}

          <Button
            type="button"
            variant="outline"
            onClick={addWorkExperience}
            className="mt-2"
          >
            {getText("Προσθήκη Εμπειρίας / Add Experience", language)}
          </Button>
        </CardContent>
      </Card>

      {/* Languages Section */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <h2 className="text-xl font-semibold mb-4">
            {getText("Γλώσσες / Languages", language)}
          </h2>

          <div className="mt-4">
            <h4 className="text-md font-medium mb-2">
              {getText("Γλώσσες-Επίπεδο / Spoken Languages-Level", language)}
            </h4>
            <LanguageSelect
              language={language}
              languages={languages}
              setLanguages={setLanguages}
            />
          </div>
        </CardContent>
      </Card>

      {/* Candidate Type Section */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <h2 className="text-xl font-semibold mb-4">
            {getText("Τύπος Υποψηφίου / Candidate Type", language)}
          </h2>

          <div className="grid gap-6 md:grid-cols-1">
            <FormField
              control={form.control}
              name="candidateType"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>
                    {getText(
                      "Επιλέξτε τον τύπο υποψηφίου που σας ενδιαφέρει / Select the candidate type you are interested in",
                      language
                    )}
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="flex flex-col space-y-1"
                    >
                      {candidateTypeOptions.map((option) => (
                        <FormItem
                          key={option.id}
                          className="flex items-center space-x-3 space-y-0"
                        >
                          <FormControl>
                            <RadioGroupItem value={option.id} />
                          </FormControl>
                          <FormLabel className="font-normal">
                            {getText(option.label, language)}
                          </FormLabel>
                        </FormItem>
                      ))}
                    </RadioGroup>
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>

          {/* Conditional fields for tutor */}
          {form.watch("candidateType") === "tutor" ||
            form.watch("candidateType") === "both" ? (
            <div className="mt-6 space-y-6">
              <div>
                <FormField
                  control={form.control}
                  name="musicalInstruments"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {getText(
                          "Μουσικό Όργανο (όταν αυτό υπάρχει) / Musical Instrument (if applicable)",
                          language
                        )}
                      </FormLabel>
                      <FormDescription>
                        {getText(
                          "Επιλέξτε τα μουσικά όργανα που διδάσκετε / Select the musical instruments you teach",
                          language
                        )}
                      </FormDescription>
                      <div className="grid grid-cols-2 gap-2 mt-2">
                        {musicalInstrumentOptions.map((option) => (
                          <FormItem
                            key={option.id}
                            className="flex flex-row items-start space-x-3 space-y-0"
                          >
                            <FormControl>
                              <Checkbox
                                checked={field.value?.includes(option.id)}
                                onCheckedChange={(checked) => {
                                  const updatedValue = checked
                                    ? [...(field.value || []), option.id]
                                    : (field.value || []).filter(
                                      (value) => value !== option.id
                                    );
                                  field.onChange(updatedValue);
                                }}
                              />
                            </FormControl>
                            <FormLabel className="font-normal">
                              {getText(option.label, language)}
                            </FormLabel>
                          </FormItem>
                        ))}
                      </div>
                      <FormMessage lang={language} />
                    </FormItem>
                  )}
                />
              </div>

              <div>
                <FormField
                  control={form.control}
                  name="musicTheory"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {getText(
                          "Θεωρητικά της Μουσικής / Music Theory",
                          language
                        )}
                      </FormLabel>
                      <FormDescription>
                        {getText(
                          "Επιλέξτε τα θεωρητικά μαθήματα που διδάσκετε / Select the theoretical subjects you teach",
                          language
                        )}
                      </FormDescription>
                      <div className="grid grid-cols-2 gap-2 mt-2">
                        {musicTheoryOptions.map((option) => (
                          <FormItem
                            key={option.id}
                            className="flex flex-row items-start space-x-3 space-y-0"
                          >
                            <FormControl>
                              <Checkbox
                                checked={field.value?.includes(option.id)}
                                onCheckedChange={(checked) => {
                                  const updatedValue = checked
                                    ? [...(field.value || []), option.id]
                                    : (field.value || []).filter(
                                      (value) => value !== option.id
                                    );
                                  field.onChange(updatedValue);
                                }}
                              />
                            </FormControl>
                            <FormLabel className="font-normal">
                              {getText(option.label, language)}
                            </FormLabel>
                          </FormItem>
                        ))}
                      </div>
                      <FormMessage lang={language} />
                    </FormItem>
                  )}
                />
              </div>

              <div>
                <FormField
                  control={form.control}
                  name="lessonFormat"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {getText("Τρόπος Μαθημάτων / Lesson Format", language)}
                      </FormLabel>
                      <FormDescription>
                        {getText(
                          "Επιλέξτε τους τρόπους διδασκαλίας που προσφέρετε / Select the teaching formats you offer",
                          language
                        )}
                      </FormDescription>
                      <div className="grid grid-cols-2 gap-2 mt-2">
                        {lessonFormatOptions.map((option) => (
                          <FormItem
                            key={option.id}
                            className="flex flex-row items-start space-x-3 space-y-0"
                          >
                            <FormControl>
                              <Checkbox
                                checked={field.value?.includes(option.id)}
                                onCheckedChange={(checked) => {
                                  const updatedValue = checked
                                    ? [...(field.value || []), option.id]
                                    : (field.value || []).filter(
                                      (value) => value !== option.id
                                    );
                                  field.onChange(updatedValue);
                                }}
                              />
                            </FormControl>
                            <FormLabel className="font-normal">
                              {getText(option.label, language)}
                            </FormLabel>
                          </FormItem>
                        ))}
                      </div>
                      <FormMessage lang={language} />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          ) : null}
        </CardContent>
      </Card>

      {/* Position Interests Section */}
      {(form.watch("candidateType") === "nanny" ||
        form.watch("candidateType") === "both") ? (
        <Card className="mb-6">
          <CardContent className="pt-6">
            <h2 className="text-xl font-semibold mb-4">
              {getText("Ενδιαφέροντα Θέσεων / Position Interests", language)}
            </h2>

            <div className="grid gap-6 md:grid-cols-1">
              <FormField
                control={form.control}
                name="positionInterests"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {getText(
                        "Προτιμήσεις Θέσεων / What is your positions preferences?",
                        language
                      )}
                    </FormLabel>
                    <FormDescription>
                      {getText(
                        "Μπορούν να τσεκαριστούν παραπάνω από μία / You can always check multiple choices",
                        language
                      )}
                    </FormDescription>
                    <div className="grid grid-cols-2 gap-2 mt-2">
                      {positionOptions.map((option) => (
                        <FormItem
                          key={option.id}
                          className="flex flex-row items-start space-x-3 space-y-0"
                        >
                          <FormControl>
                            <Checkbox
                              checked={field.value?.includes(option.id)}
                              onCheckedChange={(checked) => {
                                const updatedValue = checked
                                  ? [...(field.value || []), option.id]
                                  : (field.value || []).filter(
                                    (value) => value !== option.id
                                  );
                                field.onChange(updatedValue);
                              }}
                            />
                          </FormControl>
                          <FormLabel className="font-normal">
                            {option.label}
                          </FormLabel>
                        </FormItem>
                      ))}
                    </div>
                    <FormMessage lang={language} />
                  </FormItem>
                )}
              />
            </div>
            <FormField
              control={form.control}
              name="positionType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {getText(
                      "Ενδιαφέρεστε για Live-In ή Live-Out Positions; / Are you interested in Live-In or Live-Out Positions?",
                      language
                    )}
                  </FormLabel>
                  <FormDescription>
                    {getText(
                      "Μπορούν να τσεκαριστούν και τα 2 / You can always check both",
                      language
                    )}
                  </FormDescription>
                  <div className="grid grid-cols-2 gap-2 mt-2">
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value?.includes("live-in")}
                          onCheckedChange={(checked) => {
                            const updatedValue = checked
                              ? [...(field.value || []), "live-in"]
                              : (field.value || []).filter(
                                (value) => value !== "live-in"
                              );
                            field.onChange(updatedValue);
                          }}
                        />
                      </FormControl>
                      <FormLabel className="font-normal">
                        {getText(
                          "Live-In (Μένει στο σπίτι) / Live-In (lives in the household)",
                          language
                        )}
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value?.includes("live-out")}
                          onCheckedChange={(checked) => {
                            const updatedValue = checked
                              ? [...(field.value || []), "live-out"]
                              : (field.value || []).filter(
                                (value) => value !== "live-out"
                              );
                            field.onChange(updatedValue);
                          }}
                        />
                      </FormControl>
                      <FormLabel className="font-normal">
                        {getText(
                          "Live-Out (Μένει εκτός σπιτιού) / Live-Out (lives outside the household)",
                          language
                        )}
                      </FormLabel>
                    </FormItem>
                  </div>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
            <div className="mt-6">
              <FormField
                control={form.control}
                name="scheduleInterests"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {getText(
                        "Ενδιαφέρεστε για Full-Time ή Part-Time Positions; / Are you interested in Full-Time or Part-Time Positions?",
                        language
                      )}
                    </FormLabel>
                    <FormDescription>
                      {getText(
                        "Μπορούν να τσεκαριστούν και τα 2 / You can always check both",
                        language
                      )}
                    </FormDescription>
                    <div className="grid grid-cols-2 gap-2 mt-2">
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value?.includes("full-time")}
                            onCheckedChange={(checked) => {
                              const updatedValue = checked
                                ? [...(field.value || []), "full-time"]
                                : (field.value || []).filter(
                                  (value) => value !== "full-time"
                                );
                              field.onChange(updatedValue);
                            }}
                          />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText(
                            "Full-Time (35+ ώρες την εβδομάδα) / Full-Time (35+ hours per week)",
                            language
                          )}
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value?.includes("part-time")}
                            onCheckedChange={(checked) => {
                              const updatedValue = checked
                                ? [...(field.value || []), "part-time"]
                                : (field.value || []).filter(
                                  (value) => value !== "part-time"
                                );
                              field.onChange(updatedValue);
                            }}
                          />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText(
                            "Part-Time (έως 34 ώρες την εβδομάδα) / Part-Time (up to 34 hours per week)",
                            language
                          )}
                        </FormLabel>
                      </FormItem>
                    </div>
                    <FormMessage lang={language} />
                  </FormItem>
                )}
              />
            </div>

            <div className="mt-6">
              <FormField
                control={form.control}
                name="durationInterests"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {getText(
                        "Ενδιαφέρεστε για Long-Term (μόνιμες) ή Short-Term (προσωρινές) θέσεις? / Are you interested in Long-Term(permanent) or Short-Term(temporary) Positions?",
                        language
                      )}
                    </FormLabel>
                    <FormDescription>
                      {getText(
                        "Μπορούν να τσεκαριστούν και τα 2 / You can always check both",
                        language
                      )}
                    </FormDescription>
                    <div className="grid grid-cols-2 gap-2 mt-2">
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value?.includes("long-term")}
                            onCheckedChange={(checked) => {
                              const updatedValue = checked
                                ? [...(field.value || []), "long-term"]
                                : (field.value || []).filter(
                                  (value) => value !== "long-term"
                                );
                              field.onChange(updatedValue);
                            }}
                          />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText(
                            "Long-Term (Από 7 μήνες και πάνω) / Long-Term (from 7 months)",
                            language
                          )}
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value?.includes("short-term")}
                            onCheckedChange={(checked) => {
                              const updatedValue = checked
                                ? [...(field.value || []), "short-term"]
                                : (field.value || []).filter(
                                  (value) => value !== "short-term"
                                );
                              field.onChange(updatedValue);
                            }}
                          />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText(
                            "Short-Term (Μέχρι και 6 μήνες) / Short-Term (up to 6 months)",
                            language
                          )}
                        </FormLabel>
                      </FormItem>
                    </div>
                    <FormMessage lang={language} />
                  </FormItem>
                )}
              />
            </div>
            {form.watch("durationInterests")?.includes("long-term") && (
              <div className="mt-6">
                <FormField
                  control={form.control}
                  name="startAvailability"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {getText(
                          "Διαθεσιμότητα έναρξης για τις Long-Term θέσεις / What is your start availability in Long-Term Positions?",
                          language
                        )}
                      </FormLabel>
                      <FormControl>
                        <RadioGroup
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          className="flex flex-col space-y-1"
                        >
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="immediately" />
                            </FormControl>
                            <FormLabel className="font-normal">
                              {getText("Άμεσα / Immediately", language)}
                            </FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="weeks" />
                            </FormControl>
                            <FormLabel className="font-normal">
                              {getText(
                                "Μέσα στις επόμενες εβδομάδες / In a few weeks",
                                language
                              )}
                            </FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="flexible" />
                            </FormControl>
                            <FormLabel className="font-normal">
                              {getText(
                                "Υπάρχει ευελιξία / We are flexible",
                                language
                              )}
                            </FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="other" />
                            </FormControl>
                            <FormLabel className="font-normal">
                              {getText("Άλλο / Other", language)}
                            </FormLabel>
                          </FormItem>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage lang={language} />
                    </FormItem>
                  )}
                />
              </div>
            )}

            <div className="mt-6">
              <FormField
                control={form.control}
                name="placePreferences"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {getText(
                        "Μέρη που θα θέλατε να δουλέψετε / What is your place preferences for working?",
                        language
                      )}
                    </FormLabel>
                    <FormDescription>
                      {getText(
                        "Πόλη, νησιά, χώρα, εξωτερικό κλπ. / City, Islands, Country, Abroad etc.",
                        language
                      )}
                    </FormDescription>
                    <FormControl>
                      <Input
                        placeholder={getText(
                          "π.χ. Αθήνα, Θεσσαλονίκη, Νησιά το καλοκαίρι / e.g. Athens, Thessaloniki, Islands during summer",
                          language
                        )}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage lang={language} />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>
      ) : null}

      {/* Education Section */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <h2 className="text-xl font-semibold mb-4">
            {getText("Εκπαίδευση / Education", language)}
          </h2>

          <div className="mt-4">
            <FormField
              control={form.control}
              name="education"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {getText(
                      "Εκπαίδευση,Σπουδές,Κατάρτιση / Education & Skills",
                      language
                    )}
                  </FormLabel>
                  <FormDescription>
                    {getText(
                      "Παρακαλώ κάνετε τσεκ μόνο σε όσα έχετε ολοκληρώσει / Please check only the ones you have completed",
                      language
                    )}
                  </FormDescription>
                  <div className="grid grid-cols-2 gap-2 mt-2">
                    {educationOptions.map((option) => (
                      <FormItem
                        key={option.id}
                        className="flex flex-row items-start space-x-3 space-y-0"
                      >
                        <FormControl>
                          <Checkbox
                            checked={field.value?.includes(option.id)}
                            onCheckedChange={(checked) => {
                              const updatedValue = checked
                                ? [...(field.value || []), option.id]
                                : (field.value || []).filter(
                                  (value) => value !== option.id
                                );
                              field.onChange(updatedValue);
                            }}
                          />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText(option.label, language)}
                        </FormLabel>
                      </FormItem>
                    ))}
                  </div>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>

          {/* Specialization Section */}
          <div className="mt-6">
            <FormField
              control={form.control}
              name="specialization"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>
                    {getText("Έχετε κάποια ειδίκευση; / Do you have any specialization?", language)}
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="flex flex-col space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="yes" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Ναι / Yes", language)}
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="no" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Όχι / No", language)}
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>

          {/* Specialization Types - conditional field */}
          {form.watch("specialization") === "yes" && (
            <div className="mt-4">
              <FormField
                control={form.control}
                name="specializationTypes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {getText(
                        "Αν ναι, τί από τα παρακάτω: (μπορεί να επιλέξει παραπάνω από ένα) / If the answer is yes, which of the below? (You can always check multiple choices)",
                        language
                      )}
                    </FormLabel>
                    <FormDescription>
                      {getText(
                        "Επιλέξτε όλες τις ειδικεύσεις που έχετε / Select all specializations you have",
                        language
                      )}
                    </FormDescription>
                    <div className="grid grid-cols-2 gap-2 mt-2 max-h-96 overflow-y-auto">
                      {specializationOptions.map((option) => (
                        <FormItem
                          key={option.id}
                          className="flex flex-row items-start space-x-3 space-y-0"
                        >
                          <FormControl>
                            <Checkbox
                              checked={field.value?.includes(option.id)}
                              onCheckedChange={(checked) => {
                                const updatedValue = checked
                                  ? [...(field.value || []), option.id]
                                  : (field.value || []).filter(
                                    (value) => value !== option.id
                                  );
                                field.onChange(updatedValue);
                              }}
                            />
                          </FormControl>
                          <FormLabel className="font-normal text-sm">
                            {getText(option.label, language)}
                          </FormLabel>
                        </FormItem>
                      ))}
                    </div>
                    <FormMessage lang={language} />
                  </FormItem>
                )}
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Skills Section */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <h2 className="text-xl font-semibold mb-4">
            {getText("Προσωπικό Προφίλ / Personal Profile", language)}
          </h2>

          <div className="grid gap-6 md:grid-cols-2">
            <FormField
              control={form.control}
              name="drivingLicense"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>
                    {getText("Δίπλωμα Οδήγησης / Driving License", language)}
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="flex flex-col space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="yes" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Ναι / Yes", language)}
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="no" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Όχι / No", language)}
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="vehicle"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>
                    {getText("Όχημα / Vehicle", language)}
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="flex flex-col space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="yes" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Ναι / Yes", language)}
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="no" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Όχι / No", language)}
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>

          {/* Vehicle Type - conditional field */}
          {form.watch("vehicle") === "yes" && (
            <div className="mt-4">
              <FormField
                control={form.control}
                name="vehicleType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {getText("Τύπος Οχήματος / Vehicle Type", language)}
                    </FormLabel>
                    <FormDescription>
                      {getText(
                        "Παρακαλώ αναφέρετε τον τύπο του οχήματός σας / Please specify your vehicle type",
                        language
                      )}
                    </FormDescription>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage lang={language} />
                  </FormItem>
                )}
              />
            </div>
          )}

          <div className="grid gap-6 md:grid-cols-2">
            <FormField
              control={form.control}
              name="smoker"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>
                    {getText("Καπνιστής / Smoker", language)}
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="flex flex-col space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="yes" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Ναι / Yes", language)}
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="no" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Όχι / No", language)}
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="firstAidCertification"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {getText(
                      "Πιστοποίηση Πρώτων Βοηθειών / First Aid Certification",
                      language
                    )}
                  </FormLabel>
                  <FormDescription>
                    {getText(
                      "CPR (Για βρέφη Για παιδιά  Για ενήλικες) / CPR  (Infants or Children or Adults)",
                      language
                    )}
                  </FormDescription>
                  <div className="grid grid-cols-2 gap-2 mt-2">
                    {firstAidOptions.map((option) => (
                      <FormItem
                        key={option.id}
                        className="flex flex-row items-start space-x-3 space-y-0"
                      >
                        <FormControl>
                          <Checkbox
                            checked={field.value?.includes(option.id)}
                            onCheckedChange={(checked) => {
                              const updatedValue = checked
                                ? [...(field.value || []), option.id]
                                : (field.value || []).filter(
                                  (value) => value !== option.id
                                );
                              field.onChange(updatedValue);
                            }}
                          />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {language === "el" ? option.labelEl : option.labelEn}
                        </FormLabel>
                      </FormItem>
                    ))}
                  </div>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>

          <div className="mt-4">
            <FormField
              control={form.control}
              name="firstAidUpdate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {getText(
                      "Τελευταία ενημέρωση πιστοποίησης πρώτων βοηθειών / Last update of first aid certification",
                      language
                    )}
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder={getText(
                        "π.χ. Ιανουάριος 2023 / e.g. January 2023",
                        language
                      )}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>

          <div className="grid gap-6 md:grid-cols-2 mt-4">


            <FormField
              control={form.control}
              name="criminalRecord"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {getText("Ποινικό Μητρώο / Criminal Record", language)}
                  </FormLabel>
                  <FormDescription>
                    {getText(
                      "Παρακαλώ αναφέρετε αν έχετε καθαρό ποινικό μητρώο / Please indicate if you have a clean criminal record",
                      language
                    )}
                  </FormDescription>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>
          <div className="grid gap-6 md:grid-cols-1 mt-4">
            <FormField
              control={form.control}
              name="personalProfile"
              render={({ field }) => (
                <FormItem>
                  <FormDescription>
                    {getText(
                      "Γράψτε κάτι για τον εαυτό σας, τις γενικές σας δεξιότητες με τα παιδιά, τις οικογένειες και τις πτυχές της προσωπικότητας σας που θα ενθαρρύνουν τις οικογένειες για να σας προσλάβουν. Αν έχετε ταξιδέψει, γενικότερα τις θέσεις που έχετε αναλάβει και που στοχεύετε επαγγελματικά. Όλο σε προσωπικό ύφος (είτε ποιηματάκι, είτε τραγουδάκι, είτεεπαγγελματικό, είτε γλυκό, είτε χαρούμενο, είτε απλό)  / Write some things about yourself, your general skills related to children, families and your personality's aspects that would encourage any parents to choose you for their babies.You could also mention your travelsor your work positions you have taken on and foremost the professional goals you have established. Your personal expression style may vary, for example you could write in a cute poem form or a song. Other styles you can always use in writing may be in a more formal way or something more sweet, happy or even just plain",
                      language
                    )}
                  </FormDescription>
                  <FormControl>
                    <Textarea rows={6} {...field} />
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>
          <div className="grid gap-6 md:grid-cols-2 mt-4">
            <FormField
              control={form.control}
              name="insurance"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>
                    {getText(
                      "Θα θέλατε ασφάλιση; / Would you like insurance coverage?",
                      language
                    )}
                  </FormLabel>

                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="flex flex-col space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="yes" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Ναι / Yes", language)}
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="no" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Όχι / No", language)}
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="maybe" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Ίσως / Maybe", language)}
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />

            {form.watch("insurance") !== "no" && (
              <div className="space-y-3">
                <FormField
                  control={form.control}
                  name="insuranceType"
                  render={({ field }) => (
                    <FormItem >
                      <FormLabel>
                        {getText(
                          "Κι αν ναι, τί ασφάλιση θα προτιμούσατε; / Insurance coverage type:",
                          language
                        )}
                      </FormLabel>
                      <FormControl>
                        <RadioGroup
                          onValueChange={field.onChange}
                          value={field.value}
                          className="flex flex-col space-y-1"
                        >
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="ergosimo" />
                            </FormControl>
                            <FormLabel className="font-normal">
                              {getText(
                                "Εργόσημα / Ergosimo (labor ticket in the form of a paycheck)",
                                language
                              )}
                            </FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="stamps" />
                            </FormControl>
                            <FormLabel className="font-normal">
                              {getText("Ένσημα / Stamps", language)}
                            </FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="booklet" />
                            </FormControl>
                            <FormLabel className="font-normal">
                              {getText("Μπλοκάκι / Booklet", language)}
                            </FormLabel>
                          </FormItem>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage lang={language} />
                    </FormItem>
                  )}
                />
              </div>
            )}
          </div>
        </CardContent>
      </Card>


      {/* Additional Skills Section */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <h2 className="text-xl font-semibold mb-4">
            {getText("Επιπλέον Δεξιότητες / Additional Skills", language)}
          </h2>

          <div className="grid gap-6 md:grid-cols-2">
            <FormField
              control={form.control}
              name="experiencedDriver"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>
                    {getText(
                      "Είστε οδηγός με εμπειρία, άνεση κι αυτοπεποίθηση ώστε να οδηγείτε τo αυτοκίνητo της οικογένειας με τα παιδιά μέσα; / Are you an experienced and confident driver for the purpose of driving the family's car with the children in it?",
                      language
                    )}
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="flex flex-col space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="yes" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Ναι / Yes", language)}
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="no" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Όχι / No", language)}
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="comfortableWithPets"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>
                    {getText(
                      "Είστε άνετη με τα κατοικίδια; / Are you comfortable with pets?",
                      language
                    )}
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="flex flex-col space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="yes" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Ναι / Yes", language)}
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="no" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Όχι / No", language)}
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>

          <div className="grid gap-6 md:grid-cols-2 mt-4">
            <FormField
              control={form.control}
              name="experiencedSwimmer"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>
                    {getText(
                      "Είστε έμπειρη και άνετη στην κολύμβηση; / Are you an experienced swimmer?",
                      language
                    )}
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="flex flex-col space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="yes" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Ναι / Yes", language)}
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="no" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Όχι / No", language)}
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="travelAvailability"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>
                    {getText(
                      "Διαθεσιμότητα για ταξίδια; / Are you available for travelling with the family?",
                      language
                    )}
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="flex flex-col space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="yes" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Ναι / Yes", language)}
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="no" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Όχι / No", language)}
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>

          <div className="mt-4">
            <FormField
              control={form.control}
              name="nightShiftAvailability"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>
                    {getText(
                      "Διαθεσιμότητα για διανυκτέρευση; / Are you available for night shifts?",
                      language
                    )}
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="flex flex-col space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="yes" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Ναι / Yes", language)}
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="no" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Όχι / No", language)}
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>
        </CardContent>
      </Card>

      {/* Hobbies Section */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <h2 className="text-xl font-semibold mb-4">
            {getText("Χόμπι-Ενδιαφέροντα / Hobbies-Interests", language)}
          </h2>

          <div className="grid gap-6 md:grid-cols-1">
            <FormField
              control={form.control}
              name="hobbies"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {getText(
                      "Χόμπι-Ενδιαφέροντα (μέχρι 3) / Hobbies (Name 3)",
                      language
                    )}
                  </FormLabel>
                  <FormDescription>
                    {getText(
                      "Παρακαλώ αναφέρετε μέχρι 3 χόμπι ή ενδιαφέροντα / Please mention up to 3 hobbies or interests",
                      language
                    )}
                  </FormDescription>
                  <FormControl>
                    <Textarea
                      rows={3}
                      placeholder={getText(
                        "π.χ. Μουσική, Ζωγραφική, Αθλητισμός / e.g. Music, Painting, Sports",
                        language
                      )}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>
        </CardContent>
      </Card>

      <Card className="mb-6">
        <CardContent className="pt-6">
          <h2 className="text-xl font-semibold mb-4">
            {getText("Συστάσεις / References", language)}
          </h2>

          <div className="grid gap-6 md:grid-cols-1">
            <FormField
              control={form.control}
              name="references"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>
                    {getText(
                      "Έχετε τουλάχιστον 2 πρόσφατες συστάσεις τις οποίες θα μπορούσαμε να καλέσουμε; / Do you have at least 2 recent references that we could come in contact with?",
                      language
                    )}
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="flex flex-col space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="yes" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Ναι / Yes", language)}
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="no" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Όχι / No", language)}
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>

          {form.watch("references") === "yes" && (
            <div className="mt-4">
              <h4 className="text-md font-medium mb-2">
                {getText(
                  "Στοιχεία Επικοινωνίας Συστάσεων / Reference Contact Details",
                  language
                )}
              </h4>
              {references.map((ref: any, index: number) => (
                <div key={index} className="mb-6 p-4 border rounded-md">
                  <div className="flex justify-between items-center mb-4">
                    <h4 className="text-md font-medium">
                      {getText("Σύσταση / Reference", language)} #{index + 1}
                    </h4>
                    {index > 0 && (
                      <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        onClick={() => removeReference(index)}
                      >
                        {getText("Αφαίρεση / Remove", language)}
                      </Button>
                    )}
                  </div>

                  <div className="grid gap-4 mb-4">
                    <div>
                      <FormLabel>
                        {getText("Ονοματεπώνυμο / Full Name", language)}
                      </FormLabel>
                      <Input
                        placeholder={getText(
                          "Ονοματεπώνυμο / Full Name",
                          language
                        )}
                        value={ref.name}
                        onChange={(e) =>
                          updateReference(index, "name", e.target.value)
                        }
                      />
                    </div>

                    <div>
                      <FormLabel>
                        {getText("Τηλέφωνο / Phone", language)}
                      </FormLabel>
                      <Input
                        placeholder={getText("Τηλέφωνο / Phone", language)}
                        value={ref.phone}
                        onChange={(e) =>
                          updateReference(index, "phone", e.target.value)
                        }
                      />
                    </div>

                    <div>
                      <FormLabel>{getText("Email", language)}</FormLabel>
                      <Input
                        placeholder="<EMAIL>"
                        value={ref.email}
                        onChange={(e) =>
                          updateReference(index, "email", e.target.value)
                        }
                      />
                    </div>
                  </div>
                </div>
              ))}

              <Button
                type="button"
                variant="outline"
                onClick={addReference}
                className="mt-2"
              >
                {getText("Προσθήκη Σύστασης / Add Reference", language)}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </>
  );
};
