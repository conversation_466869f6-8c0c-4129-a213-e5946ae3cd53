import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Baby, Briefcase, HomeIcon, Mail, Phone, Trash2, Users } from "lucide-react";
import React from "react";
import { useFieldArray, useFormContext as useRHFFormContext } from "react-hook-form";
import { z } from "zod";
import { useNannyRequestFormContext } from "../../../../contexts/NannyRequestFormContext";
import {
  nannyRequestFormSchema
} from "../../../../schemas/FormSchema";
import { getText } from "../../../../utils/form-utils";
import { SearchableCitySelect } from "../../../forms/fields/SearchableCitySelect";
import { Card, CardContent } from "../../../ui/card";
import { Checkbox } from "../../../ui/checkbox";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../../ui/form";
import { Input } from "../../../ui/input";
import { Textarea } from "../../../ui/textarea";
import { DynamicFormField } from "../../DynamicFormField";
import { DynamicFormLabel, DynamicTitle, useDynamicPlaceholder } from "../../DynamicLabel";
import { LanguageSelect } from "../../fields/LanguageSelect";

export const FamilyInfoStep: React.FC = () => {
  const { language, languages, setLanguages } = useNannyRequestFormContext();
  const form = useRHFFormContext<z.infer<typeof nannyRequestFormSchema>>();
  const {
    fields: socialMediaFields,
    append: appendSocialMedia,
    remove: removeSocialMedia,
  } = useFieldArray({
    control: form.control,
    name: "socialMedia",
  });
  return (
    <>
      <Card className="mb-6 border-0 shadow-lg rounded-xl overflow-hidden hover-glow transition-all-medium bg-white">
        <CardContent className="pt-6">
          <h2 className="text-xl font-semibold mb-4 flex items-center text-primary relative">
            <Users className="w-5 h-5 mr-2 text-secondary animate-pulse-soft" />
            <DynamicTitle
              titleKey="personalInfo.title"
              language={language}
              fallback={getText("Προσωπικά Στοιχεία / Personal Information", language)}
              level={2}
              className="text-xl font-semibold text-primary"
            />
          </h2>

          <div className="grid gap-6 md:grid-cols-2">
            <FormField
              control={form.control}
              name="fatherName"
              render={({ field }) => (
                <FormItem>
                  <DynamicFormLabel
                    labelKey="fatherName.label"
                    language={language}
                    fallback={getText("Ονοματεπώνυμο Πατέρα / Father's Full Name", language)}
                    className="text-primary font-medium"
                  />
                  <div className="relative">
                    <FormControl>
                      <Input
                        placeholder={useDynamicPlaceholder({
                          placeholderKey: "fatherName.placeholder",
                          language,
                          fallback: getText("Όνομα Επώνυμο / First Last Name", language)
                        })}
                        {...field}
                        className="rounded-lg pl-10 bg-light/10 border-light/30 focus:border-primary focus:ring-primary hover-border-grow"
                      />
                    </FormControl>
                    <div className="absolute top-1/2 -translate-y-1/2 left-3 text-primary pointer-events-none">
                      <Users className="h-4 w-4" />
                    </div>
                  </div>
                  <FormMessage className="text-destructive" lang={language} />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="fatherOccupation"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-primary font-medium">
                    {getText(
                      "Επάγγελμα Πατέρα / Father's Occupation",
                      language
                    )}
                  </FormLabel>
                  <div className="relative">
                    <FormControl>
                      <Input
                        placeholder={getText(
                          "Επάγγελμα / Occupation",
                          language
                        )}
                        {...field}
                        className="rounded-lg pl-10 bg-light/10 border-light/30 focus:border-primary focus:ring-primary hover-border-grow"
                      />
                    </FormControl>
                    <div className="absolute top-1/2 -translate-y-1/2 left-3 text-primary pointer-events-none">
                      <Briefcase className="h-4 w-4" />
                    </div>
                  </div>
                  <FormMessage className="text-destructive" lang={language} />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="motherName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-primary font-medium">
                    {getText(
                      "Ονοματεπώνυμο Μητέρας / Mother's Full Name",
                      language
                    )}
                  </FormLabel>
                  <div className="relative">
                    <FormControl>
                      <Input
                        placeholder={getText(
                          "Όνομα Επώνυμο / First Last Name",
                          language
                        )}
                        {...field}
                        className="rounded-lg pl-10 bg-light/10 border-light/30 focus:border-primary focus:ring-primary hover-border-grow"
                      />
                    </FormControl>
                    <div className="absolute top-1/2 -translate-y-1/2 left-3 text-primary pointer-events-none">
                      <Users className="h-4 w-4" />
                    </div>
                  </div>
                  <FormMessage className="text-destructive" lang={language} />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="motherOccupation"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-primary font-medium">
                    {getText(
                      "Επάγγελμα Μητέρας / Mother's Occupation",
                      language
                    )}
                  </FormLabel>
                  <div className="relative">
                    <FormControl>
                      <Input
                        placeholder={getText(
                          "Επάγγελμα / Occupation",
                          language
                        )}
                        {...field}
                        className="rounded-lg pl-10 bg-light/10 border-light/30 focus:border-primary focus:ring-primary hover-border-grow"
                      />
                    </FormControl>
                    <div className="absolute top-1/2 -translate-y-1/2 left-3 text-primary pointer-events-none">
                      <Briefcase className="h-4 w-4" />
                    </div>
                  </div>
                  <FormMessage className="text-destructive" lang={language} />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="parentNationality"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-primary font-medium">
                    {getText(
                      "Εθνικότητα Γονέων / Nationality of Parents",
                      language
                    )}
                  </FormLabel>
                  <div className="relative">
                    <FormControl>
                      <Input
                        type="text"

                        placeholder={getText(
                          "Εθνικότητα / Nationality",
                          language
                        )}
                        {...field}
                        className="rounded-lg pl-10 bg-light/10 border-light/30 focus:border-primary focus:ring-primary hover-border-grow"
                      />
                    </FormControl>
                    <div className="absolute top-1/2 -translate-y-1/2 left-3 text-primary pointer-events-none">
                      <Users className="h-4 w-4" />
                    </div>
                  </div>
                  <FormMessage className="text-destructive" lang={language} />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="childrenNationality"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-primary font-medium">
                    {getText(
                      "Εθνικότητα Παιδιού / Nationality of Child",
                      language
                    )}
                  </FormLabel>
                  <div className="relative">
                    <FormControl>
                      <Input
                        type="text"
                        placeholder={getText(
                          "Εθνικότητα / Nationality",
                          language
                        )}
                        {...field}
                        className="rounded-lg pl-10 bg-light/10 border-light/30 focus:border-primary focus:ring-primary hover-border-grow"
                      />
                    </FormControl>
                    <div className="absolute top-1/2 -translate-y-1/2 left-3 text-primary pointer-events-none">
                      <Users className="h-4 w-4" />
                    </div>
                  </div>
                  <FormMessage className="text-destructive" lang={language} />
                </FormItem>
              )}
            />
          </div>



          <div className="grid gap-6 md:grid-cols-2 mt-4">
            <FormField
              control={form.control}
              name="contactNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-primary font-medium">
                    {getText("Τηλέφωνο / Contact Number", language)}
                  </FormLabel>
                  <FormDescription className="text-secondary">
                    {getText(
                      "Με κωδικό περιοχής ή χώρας / With Area or Country Code",
                      language
                    )}
                  </FormDescription>
                  <div className="relative">
                    <FormControl>
                      <Input

                        type="tel"
                        inputMode="tel"
                        placeholder="+30 ************"
                        {...field}
                        className="rounded-lg pl-10 bg-light/10 border-light/30 focus:border-primary focus:ring-primary hover-border-grow"
                      />
                    </FormControl>
                    <div className="absolute top-1/2 -translate-y-1/2 left-3 text-primary pointer-events-none">
                      <Phone className="h-4 w-4" />
                    </div>
                  </div>
                  <FormMessage className="text-destructive" lang={language} />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-primary font-medium">
                    E-mail
                  </FormLabel>
                  <FormDescription className="text-secondary">
                    {getText(
                      "Απαραίτητο , εδώ θα λάβετε τα στοιχεία σας / Required, here you will receive your information",
                      language
                    )}
                  </FormDescription>
                  <div className="relative">
                    <FormControl>
                      <Input
                        placeholder="<EMAIL>"
                        {...field}
                        className="rounded-lg pl-10 bg-light/10 border-light/30 focus:border-primary focus:ring-primary hover-border-grow"
                      />
                    </FormControl>
                    <div className="absolute top-1/2 -translate-y-1/2 left-3 text-primary pointer-events-none">
                      <Mail className="h-4 w-4" />
                    </div>
                  </div>
                  <FormMessage className="text-destructive" lang={language} />
                </FormItem>
              )}
            />
          </div>
          {/* Social Media Fields */}
          <div className="mt-6">
            <FormLabel>
              {getText("Social Media (Προαιρετικό) / Social Media (Optional)", language)}
            </FormLabel>
            {socialMediaFields.map((item, index) => (
              <div
                key={item.id}
                className="flex flex-col md:flex-row gap-4 items-start mt-2 p-4 border rounded-md"
              >
                <FormField
                  control={form.control}
                  name={`socialMedia.${index}.platform`}
                  render={({ field }) => (
                    <FormItem className="flex-1 w-full md:w-auto">
                      <FormLabel>
                        {getText("Πλατφόρμα / Platform", language)}
                      </FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue
                              placeholder={getText(
                                "Επιλέξτε πλατφόρμα / Select platform",
                                language
                              )}
                            />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <DynamicFormField
                            control={form.control}
                            name={`socialMedia.${index}.platform`}
                            configName="socialMediaPlatforms"
                            language={language}
                          />
                        </SelectContent>
                      </Select>
                      <FormMessage lang={language} />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name={`socialMedia.${index}.handle`}
                  render={({ field }) => (
                    <FormItem className="flex-1 w-full md:w-auto">
                      <FormLabel>
                        {getText(
                          "Όνομα χρήστη / Handle or Link",
                          language
                        )}
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder={getText(
                            "π.χ. yourprofile / eg. yourprofile.com",
                            language
                          )}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage lang={language} />
                    </FormItem>
                  )}
                />
                <Button
                  type="button"
                  size="icon"
                  variant="ghost"
                  onClick={() => removeSocialMedia(index)}
                  className="mt-2 md:mt-6"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>

              </div>
            ))}
            <Button
              type="button"
              variant="outline"
              onClick={() => appendSocialMedia({ platform: "", handle: "" })}
              className="mt-4 ml-2"
            >
              {getText("Προσθήκη Social Media / Add Social Media", language)}
            </Button>
          </div>

        </CardContent>
      </Card>

      {/* Address */}
      <Card className="mb-6 border-0 shadow-lg rounded-xl overflow-hidden hover-glow transition-all-medium bg-card">
        <div className="h-2 bg-gradient-to-r from-primary to-accent-foreground animate-shimmer"></div>
        <CardContent className="pt-6">
          <h2 className="text-xl font-semibold mb-4 flex items-center text-primary relative">
            <HomeIcon className="w-5 h-5 mr-2  animate-pulse-soft" />
            {getText("Διεύθυνση / Address", language)}
          </h2>

          <FormField
            control={form.control}
            name="permanentAddress"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-primary font-medium">
                  {getText(
                    "Διεύθυνση Μόνιμης Κατοικίας / Address of your Permanent Residence",
                    language
                  )}
                </FormLabel>
                <FormDescription className="text-secondary">
                  {getText(
                    "Διεύθυνση, αριθμός, περιοχή και Τ.Κ. / Address, number, area, postal code etc.",
                    language
                  )}
                </FormDescription>
                <FormControl>
                  <Textarea
                    placeholder={getText(
                      "Οδός Παραδείγματος 123, Αθήνα, 12345 / Example Street 123, Athens, 12345",
                      language
                    )}
                    {...field}
                    className="rounded-lg bg-light/10 border-light/30 focus:border-primary focus:ring-primary min-h-[80px] hover-border-grow"
                  />
                </FormControl>
                <FormMessage className="text-destructive" lang={language} />
              </FormItem>
            )}
          />

          <div className="grid gap-6 md:grid-cols-2 mt-4">
            {/* City Select with Search */}
            <SearchableCitySelect form={form} name="city" language={language} />
          </div>

          <div className="mt-4">
            <FormField
              control={form.control}
              name="workingAddressSameAsPermanent"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border border-light/30 bg-light/10 p-4 hover:bg-light/20 transition-colors ">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      className="border-primary/30 text-primary focus:ring-primary"
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel className="text-primary font-medium">
                      {getText(
                        "Η ίδια με της μόνιμης κατοικίας / The same as the permanent residence",
                        language
                      )}
                    </FormLabel>
                  </div>
                </FormItem>
              )}
            />
          </div>

          {!form.watch("workingAddressSameAsPermanent") && (
            <div className="mt-4">
              <FormField
                control={form.control}
                name="workingAddress"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-primary font-medium">
                      {getText(
                        "Τοποθεσία-Περιοχή Θέσης Εργασίας / Place-Area where the suggested working positions take place",
                        language
                      )}
                    </FormLabel>
                    <FormDescription className="text-secondary">
                      {getText(
                        "Άλλη (και ποια) / Other (Please write the proper one)",
                        language
                      )}
                    </FormDescription>
                    <FormControl>
                      <Textarea
                        placeholder={getText(
                          "Οδός Εργασίας 456, Αθήνα, 12345 / Work Street 456, Athens, 12345",
                          language
                        )}
                        {...field}
                        className="rounded-lg bg-light/10 border-light/30 focus:border-primary focus:ring-primary min-h-[80px] hover-border-grow"
                      />
                    </FormControl>
                    <FormMessage className="text-destructive" lang={language} />
                  </FormItem>
                )}
              />
              <div className="grid gap-6 md:grid-cols-2 mt-4">
                {/* City Select with Search */}
                <SearchableCitySelect
                  form={form}
                  name="workingCity"
                  language={language}
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Children Information */}
      <Card className="border-0 shadow-lg rounded-xl overflow-hidden hover-glow transition-all-medium bg-gradient-to-br from-white via-white to-accent/20">
        <div className="h-2 bg-gradient-to-r from-primary to-accent-foreground animate-shimmer"></div>
        <CardContent className="pt-6">
          <h2 className="text-xl font-semibold mb-4 flex items-center text-primary relative">
            <Baby className="w-5 h-5 mr-2  animate-pulse-soft" />
            {getText("Πληροφορίες Παιδιών / Children Information", language)}
          </h2>

          <div className="grid gap-6 md:grid-cols-1">
            <FormField
              control={form.control}
              name="childrenCount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-primary font-medium">
                    {getText(
                      "Πόσα παιδιά αφορά η θέση εργασίας / How many children are you looking for a nanny for?",
                      language
                    )}
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="1"
                      {...field}
                      className="rounded-lg bg-light/10 border-light/30 focus:border-primary focus:ring-primary hover-border-grow"
                    />
                  </FormControl>
                  <FormMessage className="text-destructive" lang={language} />
                </FormItem>
              )}
            />

            <div className="mt-6">
              <FormField
                control={form.control}
                name="childrenAge"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {getText(
                        "Και ποιά είναι η ηλικία των παιδιών; / What is the age range of your children ?",
                        language
                      )}
                    </FormLabel>
                    <DynamicFormField
                      control={form.control}
                      name="childrenAge"
                      configName="childrenAgeRanges"
                      language={language}
                    />
                    <FormMessage lang={language} />
                  </FormItem>
                )}
              />
              <div className="mt-6">

                <FormField
                  control={form.control}
                  name="childrenLanguages"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {getText(
                          "Τί γλώσσα/ες μιλάνε τα παιδιά; / What languages do your children speak?",
                          language
                        )}
                      </FormLabel>
                      <div className="grid grid-cols-2 gap-2 mt-4">
                        <LanguageSelect
                          language={language}
                          languages={languages}
                          setLanguages={setLanguages}
                        />
                      </div>
                      <FormMessage lang={language} />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </div>

          <div className="mt-6">
            <FormField
              control={form.control}
              name="childSpecialInfo"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-primary font-medium">
                    {getText(
                      "Οποιαδήποτε πληροφορία σχετικά με το παιδί που θα έπρεπε να γνωρίζουμε; / Any special information about the child we should be informed of?",
                      language
                    )}
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder={getText(
                        "Παρακαλώ περιγράψτε οποιαδήποτε ειδική πληροφορία... / Please describe any special information...",
                        language
                      )}
                      {...field}
                      className="rounded-lg bg-light/10 border-light/30 focus:border-primary focus:ring-primary min-h-[100px] hover-border-grow"
                    />
                  </FormControl>
                  <FormMessage className="text-destructive" lang={language} />
                </FormItem>
              )}
            />
          </div>
        </CardContent>
      </Card>

      {/* Decorative element */}
      <div className="flex justify-center mt-8 mb-4 opacity-70">
        <div className="w-24 h-1 rounded-full bg-gradient-to-r from-primary/70 to-accent-foreground/70 animate-pulse-soft"></div>
      </div>
    </>
  );
};
