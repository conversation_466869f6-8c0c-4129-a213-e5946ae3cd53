import React from "react";
import { useFormContext as useRHFFormContext } from "react-hook-form";
import { z } from "zod";
import { useNannyRequestFormContext } from "../../../../contexts/NannyRequestFormContext";
import {
  nannyRequestFormSchema,
  positionOptions,
  specializationOptions,
} from "../../../../schemas/FormSchema";
import { getText } from "../../../../utils/form-utils";
import { Card, CardContent } from "../../../ui/card";
import { Checkbox } from "../../../ui/checkbox";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../../ui/form";
import { Input } from "../../../ui/input";
import { RadioGroup, RadioGroupItem } from "../../../ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../ui/select";

export const PositionDetailsStep: React.FC = () => {
  const { language } = useNannyRequestFormContext();
  const form = useRHFFormContext<z.infer<typeof nannyRequestFormSchema>>();

  return (
    <>
      <Card className="mb-6 border-0 shadow-lg rounded-xl overflow-hidden hover-glow transition-all-medium bg-gradient-to-br from-white via-white to-accent/20">
        <div className="h-2 bg-gradient-to-r from-primary to-accent-foreground animate-shimmer"></div>
        <CardContent className="pt-6">
          <h2 className="text-xl font-semibold mb-4">
            {getText("Λεπτομέρειες Θέσης / Position Details", language)}
          </h2>

          <div className="space-y-6">
            <FormField
              control={form.control}
              name="positionType"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>
                    {getText(
                      "Το είδος της θέσης εργασίας την οποία προσφέρετε είναι: / Which is the kind of the position you offer?",
                      language
                    )}
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="flex flex-col space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="live-in" />
                        </FormControl>
                        <FormLabel className="font-normal">Live-In</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="live-out" />
                        </FormControl>
                        <FormLabel className="font-normal">Live-Out</FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="positionDuration"
              render={({ field }) => (
                <FormItem className="space-y-3 mt-6">
                  <FormLabel>
                    {getText(
                      "Και ποια είναι η διάρκεια της θέσης εργασίας; / What is the duration of the position?",
                      language
                    )}
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="flex flex-col space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="long-term" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText(
                            "Μόνιμη (Από 7 μήνες και πάνω) / Long-Term-Permanent (From 7 months and over)",
                            language
                          )}
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="short-term" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText(
                            "Προσωρινή (Μέχρι και 6 μήνες) / Short-Term-Temporary (Up to 6 months)",
                            language
                          )}
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="scheduleType"
              render={({ field }) => (
                <FormItem className="space-y-3 mt-6">
                  <FormLabel>
                    {getText(
                      "Το γενικό ωράριο της θέσης θα είναι; / Which general schedule would you prefer?",
                      language
                    )}
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="flex flex-col space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="full-time" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText(
                            "Full-Time (35+ ώρες την εβδομάδα) / Full-Time (35+ hours per week)",
                            language
                          )}
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="part-time" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText(
                            "Part-Time (έως 34 ώρες την εβδομάδα) / Part-Time (up to 34 hours per week)",
                            language
                          )}
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />

            <div className="grid gap-6 md:grid-cols-2 mt-6">
              <FormField
                control={form.control}
                name="daysPerWeek"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {getText(
                        "Μέρες / Please be more specific about how many days per week would you like your future nanny to work?",
                        language
                      )}
                    </FormLabel>
                    <FormControl>
                      <Input type="number" min="1" max="7" {...field} />
                    </FormControl>
                    <FormMessage lang={language} />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="hoursPerDay"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {getText(
                        "Ώρες / And the ideal hours per day are?",
                        language
                      )}
                    </FormLabel>
                    <FormControl>
                      <Input type="number" min="1" max="24" {...field} />
                    </FormControl>
                    <FormMessage lang={language} />
                  </FormItem>
                )}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Salary Information */}
      <Card className="mb-6 border-0 shadow-lg rounded-xl overflow-hidden hover-glow transition-all-medium bg-gradient-to-br from-white via-white to-accent/20">
        <div className="h-2 bg-gradient-to-r from-primary to-accent-foreground animate-shimmer"></div>
        <CardContent className="pt-6">
          <h2 className="text-xl font-semibold mb-4">
            {getText("Οικονομικά Στοιχεία / Salary Information", language)}
          </h2>

          <div>
            <FormField
              control={form.control}
              name="salaryType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {getText(
                      "Προτεινόμενος Μισθός  / Perfect until now! Please inform us about the wage that you have in your mind:",
                      language
                    )}
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue
                          placeholder={getText(
                            "Επιλέξτε τύπο μισθού / Select salary type",
                            language
                          )}
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="monthly">
                        {getText("Μηνιαίος / Per month", language)}
                      </SelectItem>
                      <SelectItem value="hourly">
                        {getText("Ωριαίος / Per hour", language)}
                      </SelectItem>
                      <SelectItem value="daily">
                        {getText("Ημερήσιος / Per day", language)}
                      </SelectItem>
                      <SelectItem value="weekly">
                        {getText("Εβδομαδιαίος / Per week", language)}
                      </SelectItem>
                      <SelectItem value="package">
                        {getText("Πακέτο / Customized package", language)}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />

            <div className="grid gap-6 md:grid-cols-2 mt-4">
              <FormField
                control={form.control}
                name="salaryAmount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{getText("Ποσό / Amount", language)}</FormLabel>
                    <FormControl>
                      <Input type="number" min="0" {...field} />
                    </FormControl>
                    <FormMessage lang={language} />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="salaryCurrency"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {getText("Νόμισμα / Currency", language)}
                    </FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="EUR">EUR (€)</SelectItem>
                        <SelectItem value="USD">USD ($)</SelectItem>
                        <SelectItem value="GBP">GBP (£)</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage lang={language} />
                  </FormItem>
                )}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Position Preferences */}
      <Card className="mb-6 border-0 shadow-lg rounded-xl overflow-hidden hover-glow transition-all-medium bg-gradient-to-br from-white via-white to-accent/20">
        <div className="h-2 bg-gradient-to-r from-primary to-accent-foreground animate-shimmer"></div>
        <CardContent className="pt-6">
          <h2 className="text-xl font-semibold mb-4">
            {getText("Προτιμήσεις Θέσεων / Position Preferences", language)}
          </h2>

          <div>
            <FormField
              control={form.control}
              name="positionInterests"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {getText(
                      "Προτιμήσεις θέσεων / Which of the following Nanny Positions are you interested in?",
                      language
                    )}
                  </FormLabel>
                  <div className="grid grid-cols-2 gap-2 mt-2">
                    {positionOptions.map((option) => (
                      <FormItem
                        key={option.id}
                        className="flex flex-row items-start space-x-3 space-y-0"
                      >
                        <FormControl>
                          <Checkbox
                            checked={field.value?.includes(option.id)}
                            onCheckedChange={(checked) => {
                              const updatedValue = checked
                                ? [...(field.value || []), option.id]
                                : (field.value || []).filter(
                                  (value) => value !== option.id
                                );
                              field.onChange(updatedValue);
                            }}
                          />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {option.label}
                        </FormLabel>
                      </FormItem>
                    ))}
                  </div>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>

          <div className="mt-6">
            <FormField
              control={form.control}
              name="specializationPreferences"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {getText(
                      "Προτιμήσεις ειδίκευσης / Something more… In which of the following you would like your nanny to be specialized in?",
                      language
                    )}
                  </FormLabel>
                  <div className="grid grid-cols-2 gap-2 mt-2 max-h-60 overflow-y-auto">
                    {specializationOptions.map((option) => (
                      <FormItem
                        key={option.id}
                        className="flex flex-row items-start space-x-3 space-y-0"
                      >
                        <FormControl>
                          <Checkbox
                            checked={field.value?.includes(option.id)}
                            onCheckedChange={(checked) => {
                              const updatedValue = checked
                                ? [...(field.value || []), option.id]
                                : (field.value || []).filter(
                                  (value) => value !== option.id
                                );
                              field.onChange(updatedValue);
                            }}
                          />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText(option.label, language)}
                        </FormLabel>
                      </FormItem>
                    ))}
                  </div>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>
        </CardContent>
      </Card>

      {/* Insurance and Start Date */}
      <Card className="mb-6 border-0 shadow-lg rounded-xl overflow-hidden hover-glow transition-all-medium bg-gradient-to-br from-white via-white to-accent/20">
        <div className="h-2 bg-gradient-to-r from-primary to-accent-foreground animate-shimmer"></div>
        <CardContent className="pt-6">
          <h2 className="text-xl font-semibold mb-4">
            {getText(
              "Ασφάλιση & Διαθεσιμότητα / Insurance & Availability",
              language
            )}
          </h2>

          <div>
            <FormField
              control={form.control}
              name="insurance"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>
                    {getText(
                      "Προσφέρετε ασφάλιση; (η εταιρεία δεν αναλαμβάνει την ασφάλιση, απλά ενημερώνει και τις δύο ενδιαφερόμενες πλευρές) / Insurance coverage:",
                      language
                    )}
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="flex flex-col space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="yes" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Ναι / Yes", language)}
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="no" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Όχι / No", language)}
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="maybe" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Ίσως / Maybe", language)}
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>

          {form.watch("insurance") !== "no" && (
            <div className="mt-4">
              <FormField
                control={form.control}
                name="insuranceType"
                render={({ field }) => (
                  <FormItem className="space-y-3">
                    <FormLabel>
                      {getText(
                        "Κι αν ναι, τί ασφάλιση θα προτιμούσατε; / Insurance coverage type:",
                        language
                      )}
                    </FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex flex-col space-y-1"
                      >
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="ergosimo" />
                          </FormControl>
                          <FormLabel className="font-normal">
                            {getText(
                              "Εργόσημα / Ergosimo (labor ticket in the form of a paycheck)",
                              language
                            )}
                          </FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="stamps" />
                          </FormControl>
                          <FormLabel className="font-normal">
                            {getText("Ένσημα / Stamps", language)}
                          </FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="booklet" />
                          </FormControl>
                          <FormLabel className="font-normal">
                            {getText("Μπλοκάκι / Booklet", language)}
                          </FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage lang={language} />
                  </FormItem>
                )}
              />
            </div>
          )}

          <div className="mt-6">
            <FormField
              control={form.control}
              name="startDate"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>
                    {getText(
                      "Διαθεσιμότητα Έναρξης / Do you have an ideal start date for the collaboration?",
                      language
                    )}
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="flex flex-col space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="immediately" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Άμεσα / Immediately", language)}
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="weeks" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText(
                            "Μέσα στις επόμενες εβδομάδες / In a few weeks",
                            language
                          )}
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="flexible" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText(
                            "Υπάρχει ευελιξία / We are flexible",
                            language
                          )}
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="other" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          {getText("Άλλο / Other", language)}
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage lang={language} />
                </FormItem>
              )}
            />
          </div>

          {form.watch("startDate") === "other" && (
            <div className="mt-4">
              <FormField
                control={form.control}
                name="startDateSpecific"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {getText(
                        "Συγκεκριμένη ημερομηνία / Specific date",
                        language
                      )}
                    </FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage lang={language} />
                  </FormItem>
                )}
              />
            </div>
          )}
        </CardContent>
      </Card>
    </>
  );
};
