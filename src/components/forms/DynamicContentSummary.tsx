import { useFormConfigs } from "@/hooks/useFormConfig";
import { useFormLabelCategories } from "@/hooks/useFormLabels";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { Loader2, Database, Tag, Type, Settings } from "lucide-react";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "../ui/tabs";

/**
 * Component that provides a comprehensive summary of all available
 * dynamic form content without hardcoding any field names
 */
export function DynamicContentSummary() {
  const { configs, loading: configsLoading, error: configsError } = useFormConfigs();
  const { categories, loading: labelsLoading, error: labelsError } = useFormLabelCategories();

  if (configsLoading || labelsLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading dynamic content summary...</span>
      </div>
    );
  }

  if (configsError || labelsError) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardContent className="pt-6">
          <p className="text-red-600">
            Error loading dynamic content: {configsError || labelsError}
          </p>
        </CardContent>
      </Card>
    );
  }

  // Analyze the data
  const activeConfigs = configs.filter(c => c.isActive);
  const activeCategories = categories.filter(c => c.isActive);

  const candidateConfigs = activeConfigs.filter(c => c.formType === "candidate" || c.formType === "both");
  const nannyRequestConfigs = activeConfigs.filter(c => c.formType === "nannyRequest" || c.formType === "both");

  const candidateCategories = activeCategories.filter(c => c.formType === "candidate" || c.formType === "both");
  const nannyRequestCategories = activeCategories.filter(c => c.formType === "nannyRequest" || c.formType === "both");

  // Group by category
  const configsByCategory = activeConfigs.reduce((acc, config) => {
    if (!acc[config.category]) {
      acc[config.category] = [];
    }
    acc[config.category].push(config);
    return acc;
  }, {} as Record<string, any[]>);

  // Group by field type
  const configsByType = activeConfigs.reduce((acc, config) => {
    if (!acc[config.fieldType]) {
      acc[config.fieldType] = [];
    }
    acc[config.fieldType].push(config);
    return acc;
  }, {} as Record<string, any[]>);

  // Get all unique categories
  const allCategories = Array.from(new Set([
    ...Object.keys(configsByCategory),
    ...activeCategories.map(cat => cat.name)
  ])).sort();

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Dynamic Content Summary
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            Overview of all available dynamic fields and labels in the system
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{activeConfigs.length}</div>
              <div className="text-sm text-blue-600">Total Fields</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{allCategories.length}</div>
              <div className="text-sm text-green-600">Categories</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {activeCategories.reduce((sum, cat) => sum + cat.labels.length, 0)}
              </div>
              <div className="text-sm text-purple-600">Labels</div>
            </div>
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">
                {Object.keys(configsByType).length}
              </div>
              <div className="text-sm text-orange-600">Field Types</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="categories">Categories</TabsTrigger>
          <TabsTrigger value="field-types">Field Types</TabsTrigger>
          <TabsTrigger value="labels">Labels</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Candidate Form</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>Dynamic Fields:</span>
                    <Badge>{candidateConfigs.length}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Categories:</span>
                    <Badge>{candidateCategories.length}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Labels:</span>
                    <Badge>{candidateCategories.reduce((sum, cat) => sum + cat.labels.length, 0)}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Required Fields:</span>
                    <Badge variant="destructive">
                      {candidateConfigs.filter(c => c.isRequired).length}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Nanny Request Form</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>Dynamic Fields:</span>
                    <Badge>{nannyRequestConfigs.length}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Categories:</span>
                    <Badge>{nannyRequestCategories.length}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Labels:</span>
                    <Badge>{nannyRequestCategories.reduce((sum, cat) => sum + cat.labels.length, 0)}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Required Fields:</span>
                    <Badge variant="destructive">
                      {nannyRequestConfigs.filter(c => c.isRequired).length}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="categories" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {allCategories.map(categoryName => {
              const categoryConfig = activeCategories.find(cat => cat.name === categoryName);
              const categoryFields = configsByCategory[categoryName] || [];
              const categoryLabels = categoryConfig?.labels || [];

              return (
                <Card key={categoryName}>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base flex items-center gap-2">
                      <Tag className="h-4 w-4" />
                      {categoryConfig 
                        ? categoryConfig.titleEn 
                        : categoryName.charAt(0).toUpperCase() + categoryName.slice(1)
                      }
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Fields:</span>
                        <Badge variant="secondary">{categoryFields.length}</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span>Labels:</span>
                        <Badge variant="outline">{categoryLabels.length}</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span>Form Type:</span>
                        <Badge variant="outline" className="text-xs">
                          {categoryConfig?.formType || 'both'}
                        </Badge>
                      </div>
                      {categoryFields.length > 0 && (
                        <div className="mt-3">
                          <div className="text-xs text-muted-foreground mb-1">Field Keys:</div>
                          <div className="flex flex-wrap gap-1">
                            {categoryFields.slice(0, 3).map(field => (
                              <code key={field.id} className="text-xs bg-gray-100 px-1 rounded">
                                {field.fieldKey}
                              </code>
                            ))}
                            {categoryFields.length > 3 && (
                              <span className="text-xs text-muted-foreground">
                                +{categoryFields.length - 3} more
                              </span>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="field-types" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {Object.entries(configsByType).map(([fieldType, fields]) => (
              <Card key={fieldType}>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base flex items-center gap-2">
                    <Type className="h-4 w-4" />
                    {fieldType.charAt(0).toUpperCase() + fieldType.slice(1)}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Count:</span>
                      <Badge>{fields.length}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Required:</span>
                      <Badge variant="destructive">
                        {fields.filter(f => f.isRequired).length}
                      </Badge>
                    </div>
                    <div className="mt-3">
                      <div className="text-xs text-muted-foreground mb-1">Examples:</div>
                      <div className="space-y-1">
                        {fields.slice(0, 3).map(field => (
                          <code key={field.id} className="text-xs bg-gray-100 px-1 rounded block">
                            {field.fieldKey}
                          </code>
                        ))}
                        {fields.length > 3 && (
                          <span className="text-xs text-muted-foreground">
                            +{fields.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="labels" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            {activeCategories.map(category => (
              <Card key={category.id}>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base">
                    {category.titleEn} Labels
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">
                    {category.labels.length} labels available
                  </p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 max-h-48 overflow-y-auto">
                    {category.labels.map(label => (
                      <div key={label.id} className="p-2 bg-gray-50 rounded text-sm">
                        <div className="flex items-center justify-between mb-1">
                          <code className="text-xs font-mono">{label.key}</code>
                          <Badge variant="outline" className="text-xs">
                            {label.context}
                          </Badge>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          EN: {label.labelEn}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
