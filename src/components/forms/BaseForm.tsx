import { supabase } from "@/lib/supabase";
import { getText } from "@/utils/form-utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useRef, useState } from "react";
import { FormProvider as RHFFormProvider, useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import { z } from "zod";
import {
  CandidateFormProvider,
  useCandidateFormContext,
} from "../../contexts/CandidateFormContext";
import { FormThemeProvider } from "../../contexts/FormThemeContext";
import {
  NannyRequestFormProvider,
  useNannyRequestFormContext,
} from "../../contexts/NannyRequestFormContext";
import { FormLayout } from "../layouts/FormLayout";
import { UploadProgress } from "../ui/upload-progress";
import { useToast } from "../ui/use-toast";

interface BaseFormProps<T extends z.ZodType<any, any>> {
  formSchema: T;
  defaultValues: z.infer<T>;
  onSubmit: (
    values: z.infer<T>,
    uploadedFiles?: Record<string, string | string[]>
  ) => Promise<void>;
  children: React.ReactNode;
  title: string;
  isCandidateForm: boolean;
  formType: "candidate" | "nannyRequest";
  localStorageKey: string;
  fileFields?: string[];
  multipleFileFields?: string[];
  uploadHandler?: (
    files: Array<{ field: string; file: File | null }>,
    multipleFiles: Array<{ field: string; files: FileList | null }>,
    setUploadProgress: (progress: number) => void,
    email: string
  ) => Promise<Record<string, string | string[]>>;
}

// Wrapper component that sets up the form provider
export function BaseFormWrapper<T extends z.ZodType<any, any>>({
  formSchema,
  defaultValues,
  onSubmit,
  children,
  title,
  isCandidateForm,
  formType,
  localStorageKey,
  fileFields = [],
  multipleFileFields = [],
  uploadHandler,
}: BaseFormProps<T>) {
  // Use the appropriate form provider based on form type
  const FormProviderComponent = isCandidateForm
    ? CandidateFormProvider
    : NannyRequestFormProvider;

  return (
    <FormThemeProvider>
      <FormProviderComponent>
        <BaseFormContent
          formSchema={formSchema}
          defaultValues={defaultValues}
          onSubmit={onSubmit}
          title={title}
          isCandidateForm={isCandidateForm}
          formType={formType}
          localStorageKey={localStorageKey}
          fileFields={fileFields}
          multipleFileFields={multipleFileFields}
          uploadHandler={uploadHandler}
        >
          {children}
        </BaseFormContent>
      </FormProviderComponent>
    </FormThemeProvider>
  );
}

// Main form component
function BaseFormContent<T extends z.ZodType<any, any>>({
  formSchema,
  defaultValues,
  onSubmit,
  children,
  title,
  isCandidateForm,
  formType,
  localStorageKey,
  fileFields = [],
  multipleFileFields = [],
  uploadHandler,
}: BaseFormProps<T>) {
  // Use the appropriate context based on form type
  const useAppropriateContext = () => {
    if (isCandidateForm) {
      try {
        return useCandidateFormContext();
      } catch (error) {
        console.error("Error using CandidateFormContext:", error);
        // Fallback to the original context if needed
        return {} as any;
      }
    } else {
      try {
        return useNannyRequestFormContext();
      } catch (error) {
        console.error("Error using NannyRequestFormContext:", error);
        // Fallback to the original context if needed
        return {} as any;
      }
    }
  };

  const context = useAppropriateContext();
  const {
    language,
    languages,
    validateCurrentStep: contextValidateCurrentStep,
    setValidationAttempted,
    currentStep,
    setCurrentStep,
  } = context;

  // These properties might not exist in all contexts
  const workExperience =
    isCandidateForm && "workExperience" in context
      ? context.workExperience
      : undefined;
  const references =
    isCandidateForm && "references" in context ? context.references : undefined;

  const navigate = useNavigate();
  const { toast } = useToast();

  // State for upload/submission progress tracking
  const [isUploading, setIsUploading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const formRef = useRef<HTMLFormElement>(null);

  // Function to reset file fields in the form
  const resetFileFields = () => {
    const formValues = form.getValues();
    const updatedValues = { ...formValues };

    // Reset all file fields to null
    [...fileFields, ...multipleFileFields].forEach((field) => {
      if (field in updatedValues) {
        updatedValues[field] = null;
      }

      // Also reset the actual file input elements in the DOM
      // This ensures the visual state of the file inputs is cleared
      const fileInputs = document.querySelectorAll(`input[name="${field}"]`);
      fileInputs.forEach((input) => {
        if (input instanceof HTMLInputElement && input.type === "file") {
          input.value = "";
        }
      });
    });

    // Reset the form with updated values but don't trigger validation
    form.reset(updatedValues, {
      keepErrors: true,
      keepDirty: true,
      keepIsSubmitted: false,
      keepTouched: true,
    });
  };

  // Function to reset error state and clean up
  const resetErrorState = () => {
    setError(null);
    setIsUploading(false);
    setIsSubmitting(false);
    setUploadProgress(0);
    resetFileFields();
  };

  // No need to set form type anymore as we're using separate contexts

  // Function to get saved form data from localStorage
  const getSavedFormData = () => {
    try {
      const savedData = localStorage.getItem(localStorageKey);
      return savedData ? JSON.parse(savedData) : null;
    } catch (error) {
      console.error("Error parsing saved form data:", error);
      return null;
    }
  };

  // Get saved form data
  const savedFormData = getSavedFormData();

  // Initialize the form with zod resolver
  const form = useForm<z.infer<T>>({
    resolver: zodResolver(formSchema),
    mode: "onChange", // Validate on change, but only show errors when attempted
    reValidateMode: "onChange", // Re-validate fields on change
    // Use saved data if available, otherwise use defaults
    defaultValues: savedFormData || defaultValues,
  });

  // Override the validateCurrentStep function from context
  useEffect(() => {
    // Define a function that will validate the current step
    const validateStep = async (): Promise<boolean> => {
      setValidationAttempted(true);

      // For now, validate all fields since we're using dynamic steps
      // TODO: Implement dynamic step validation based on step categories
      const allFields = Object.keys(form.getValues());
      const currentStepFields = allFields;

      // Filter to only include fields that exist in the form
      const fieldsToValidate = currentStepFields.filter((field) =>
        Object.keys(form.getValues()).includes(field)
      );

      console.debug("Validating fields in BaseForm:", fieldsToValidate);

      // Validate each field individually
      let isValid = true;
      for (const field of fieldsToValidate) {
        const fieldResult = await form.trigger(field as any);
        if (!fieldResult) {
          isValid = false;
          console.error(`Field ${field} validation failed`);
        }
      }

      // If validation fails, scroll to the first error
      if (!isValid) {
        console.error("Form errors:", form.formState.errors);
        const firstError = document.querySelector('[aria-invalid="true"]');
        if (firstError) {
          firstError.scrollIntoView({ behavior: "smooth", block: "center" });
        }
      }

      return isValid;
    };

    // Override the context function with our implementation
    if (contextValidateCurrentStep) {
      contextValidateCurrentStep.current = validateStep;
    }
  }, [
    form,
    setValidationAttempted,
    currentStep,
    contextValidateCurrentStep,
    isCandidateForm,
  ]);

  // Save form values to localStorage whenever they change
  useEffect(() => {
    const subscription = form.watch((value) => {
      // Don't save file objects since they can't be serialized
      const valueToSave = { ...value };

      // Remove file objects before saving
      [...fileFields, ...multipleFileFields].forEach((field) => {
        if (valueToSave[field]) {
          delete valueToSave[field];
        }
      });

      localStorage.setItem(localStorageKey, JSON.stringify(valueToSave));
    });

    return () => subscription.unsubscribe();
  }, [form.watch, fileFields, multipleFileFields, localStorageKey]);

  // Form submission handler
  const handleSubmit = async (values: z.infer<T>) => {
    try {
      const existingUser = await supabase.auth.getSession();
      const activeSession = existingUser.data.session;
      if (
        activeSession &&
        activeSession.user &&
        (activeSession.user.is_anonymous == false ||
          activeSession.user.user_metadata?.is_anonymous == false)
      ) {
        await supabase.auth.signOut();
        await supabase.auth.signInAnonymously();
      } else if (
        activeSession &&
        activeSession.user &&
        (activeSession.user.is_anonymous == true ||
          activeSession.user.user_metadata?.is_anonymous == true)
      ) {
        await supabase.auth.getSession();
      } else {
        await supabase.auth.signInAnonymously();
      }
      // Include languages, work experience and references in the form data
      const formData = {
        ...values,
        languages: languages
          ? languages.filter(
            (lang: { language: string }) => lang.language.trim() !== ""
          )
          : [],
        ...(workExperience && {
          workExperience: workExperience.filter(
            (exp: { period?: string }) => exp.period?.trim() !== ""
          ),
        }),
        ...(references && {
          referencesContacts: references.filter(
            (ref: { name?: string }) => ref.name?.trim() !== ""
          ),
        }),
      };

      // Clear saved form data on successful submission
      const clearSavedData = () => {
        localStorage.removeItem(localStorageKey);

        // Clear form context state based on form type
        if (isCandidateForm) {
          localStorage.removeItem("candidateFormContextState");
        } else {
          localStorage.removeItem("nannyRequestFormContextState");
        }

        // Reset form
        form.reset({
          ...defaultValues,
        });
        formRef.current?.reset();
        form.clearErrors();

        // Also reset file input elements in the DOM
        [...fileFields, ...multipleFileFields].forEach((field) => {
          const fileInputs = document.querySelectorAll(
            `input[name="${field}"]`
          );
          fileInputs.forEach((input) => {
            if (input instanceof HTMLInputElement && input.type === "file") {
              input.value = "";
            }
          });
        });

        // Reset step manager state
        if (setCurrentStep) {
          setCurrentStep(0); // Reset to first step
        }
      };

      // Start the form submission process
      setIsSubmitting(true);

      try {
        // Prepare file data structures but don't upload yet
        let filesToUpload: { field: string; file: File }[] = [];
        let multipleFilesToUpload: { field: string; files: FileList }[] = [];
        let hasFilesToUpload = false;

        // Check if we have files to upload
        if (
          uploadHandler &&
          (fileFields.length > 0 || multipleFileFields.length > 0)
        ) {
          // Prepare single files for upload
          filesToUpload = fileFields
            .map((field) => {
              const file =
                formData[field] instanceof File
                  ? (formData[field] as File)
                  : null;
              return { field, file };
            })
            .filter((item) => item.file instanceof File);

          // Prepare multiple files for upload
          multipleFilesToUpload = multipleFileFields
            .map((field) => {
              const files =
                formData[field] instanceof FileList
                  ? (formData[field] as FileList)
                  : null;
              return { field, files };
            })
            .filter((item) => item.files instanceof FileList);

          // Check if we have any files to upload
          hasFilesToUpload =
            (filesToUpload.length > 0 || multipleFilesToUpload.length > 0) &&
            !!formData.email;
        }

        // First, submit the form data WITHOUT the actual files
        // We'll include placeholders or metadata about the files
        const formDataForSubmission = { ...formData };

        // Add placeholders for file fields that will be uploaded later
        if (hasFilesToUpload) {
          // For single files, add the filename as a placeholder
          filesToUpload.forEach(({ field, file }) => {
            formDataForSubmission[field] = file.name;
          });

          // For multiple files, add an array of filenames as placeholders
          multipleFilesToUpload.forEach(({ field, files }) => {
            formDataForSubmission[field] = Array.from(files).map(
              (file) => file.name
            );
          });
        }

        // Submit the form data to validate it first
        console.log(
          "Submitting form data for validation:",
          formDataForSubmission
        );
        if (hasFilesToUpload) {
          setIsUploading(true);
          setUploadProgress(0);

          console.log("Form validation successful, now uploading files");
          let uploadedFiles = {};

          try {
            // Upload the files
            uploadedFiles = await uploadHandler(
              filesToUpload,
              multipleFilesToUpload,
              setUploadProgress,
              formData.email as string
            );

            console.log("File upload completed successfully:", uploadedFiles);

            // Update the form data with the actual file paths
            const finalFormData = {
              ...formData,
              ...uploadedFiles,
            };

            // Make a second submission with the actual file paths
            // This is optional and depends on your backend implementation
            // If your backend doesn't need a second submission, you can skip this
            console.log("Updating submission with file paths:", finalFormData);
            setIsUploading(false);
            setUploadProgress(100);
          } catch (uploadError) {
            console.error("Error during file upload:", uploadError);

            // Reset file input fields
            resetFileFields();

            // Clear upload state
            setIsUploading(false);
            setUploadProgress(0);

            throw new Error(`File upload failed: ${uploadError.message}`);
          }
          try {
            await onSubmit(formDataForSubmission, uploadedFiles);
          } catch (error) {
            console.error("Error during creating client:", error);

            // Reset file input fields
            resetFileFields();

            // Clear upload state
            setIsUploading(false);
            setUploadProgress(0);

            throw new Error(`File upload failed: ${error.message}`);
          }
        } else {
          await onSubmit(formDataForSubmission);
        }

        // If we reach here, the form submission was successful
        // Now we can upload the files if needed
      } catch (error: any) {
        console.error("Transaction failed:", error);

        // Reset states
        setIsUploading(false);
        setIsSubmitting(false);
        setUploadProgress(0);

        // Reset file input fields
        resetFileFields();

        // Re-throw the error to be caught by the outer catch block
        throw error;
      }

      // Clear saved form data after successful submission
      clearSavedData();
      // await supabase.auth.signOut();
      toast({
        title: language === "el" ? "Επιτυχής υποβολή" : "Submission successful",
        description:
          language === "el"
            ? "Η αίτησή σας υποβλήθηκε επιτυχώς."
            : "Your application has been submitted successfully.",
        duration: 5000,
      });

      // Use window.location instead of navigate to avoid triggering route changes
      // that could cause session check loops - but only after successful submission
      setTimeout(() => {
        navigate("/", { replace: true });
      }, 5000);
    } catch (error: any) {
      console.error("Error submitting form:", error);

      // Extract error message from the response if available
      let errorMessage = "Error submitting form. Please try again.";

      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        errorMessage =
          error.response.data?.message ||
          error.response.data?.detail ||
          error.response.data?.error ||
          error.message ||
          "Server error";
        console.error("Error response data:", error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        errorMessage = "No response from server. Please check your connection.";
      } else if (error.message) {
        // Something happened in setting up the request that triggered an Error
        errorMessage = error.message;
      }

      // Set error message and reset file fields
      setError(errorMessage);

      // Reset file input fields to prevent the InvalidStateError
      resetFileFields();

      toast({
        variant: "destructive",
        title:
          language === "el"
            ? "Κάτι πήγε στραβά!"
            : "Uh oh! Something went wrong.",
        description:
          language === "el"
            ? `Υπήρξε πρόβλημα με την υποβολή της φόρμας: ${errorMessage}`
            : `There was a problem with your form submission: ${errorMessage}`,
      });
    } finally {
      // Only reset submission states if there's no error
      // If there's an error, we want to keep the error state until the user dismisses it
      if (!error) {
        setIsSubmitting(false);
        setIsUploading(false);
        setUploadProgress(0);

        // Also ensure file inputs are reset on successful submission
        // This is a safety measure in case the form is not redirected
        [...fileFields, ...multipleFileFields].forEach((field) => {
          const fileInputs = document.querySelectorAll(
            `input[name="${field}"]`
          );
          fileInputs.forEach((input) => {
            if (input instanceof HTMLInputElement && input.type === "file") {
              input.value = "";
            }
          });
        });
      }
    }
  };

  return (
    <RHFFormProvider {...form}>
      <FormLayout
        form={form}
        formRef={formRef}
        onSubmit={handleSubmit}
        isCandidateForm={isCandidateForm}
        title={getText(title, language)}
      >
        {children}

        {/* Upload/Submit Progress Overlay */}
        <UploadProgress
          handleError={resetErrorState}
          isUploading={isUploading}
          isSubmitting={isSubmitting}
          progress={uploadProgress}
          errorMessage={error}
          message={
            language === "el" ? "Μεταφόρτωση αρχείων..." : "Uploading files..."
          }
          language={language}
        />
      </FormLayout>
    </RHFFormProvider>
  );
}
