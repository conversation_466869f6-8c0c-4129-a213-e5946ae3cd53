import { useForm<PERSON>abels } from "@/hooks/useFormLabels";
import { cn } from "@/lib/utils";
import { ReactNode } from "react";

interface DynamicLabelProps {
  labelKey: string;
  language?: "el" | "en";
  fallback?: string;
  className?: string;
  children?: ReactNode;
  as?: "label" | "span" | "div" | "p" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6";
}

/**
 * Dynamic label component that loads text from the database
 */
export function DynamicLabel({
  labelKey,
  language = "en",
  fallback,
  className,
  children,
  as: Component = "span",
}: DynamicLabelProps) {
  const { getLabel, loading } = useFormLabels();

  if (loading) {
    return (
      <Component className={cn("animate-pulse bg-gray-200 rounded", className)}>
        {fallback || labelKey}
      </Component>
    );
  }

  const labelText = getLabel(labelKey, language, fallback);

  return (
    <Component className={className}>
      {labelText}
      {children}
    </Component>
  );
}

/**
 * Dynamic form label component specifically for form fields
 */
interface DynamicFormLabelProps {
  labelKey: string;
  language?: "el" | "en";
  fallback?: string;
  className?: string;
  required?: boolean;
  htmlFor?: string;
}

export function DynamicFormLabel({
  labelKey,
  language = "en",
  fallback,
  className,
  required = false,
  htmlFor,
}: DynamicFormLabelProps) {
  const { getLabel, loading } = useFormLabels();

  if (loading) {
    return (
      <label htmlFor={htmlFor} className={cn("animate-pulse bg-gray-200 rounded h-4 w-20 block", className)}>
        &nbsp;
      </label>
    );
  }

  const labelText = getLabel(labelKey, language, fallback);

  return (
    <label htmlFor={htmlFor} className={className}>
      {labelText}
      {required && <span className="text-red-500 ml-1">*</span>}
    </label>
  );
}

/**
 * Dynamic placeholder component for form inputs
 */
interface DynamicPlaceholderProps {
  placeholderKey: string;
  language?: "el" | "en";
  fallback?: string;
}

export function useDynamicPlaceholder({
  placeholderKey,
  language = "en",
  fallback,
}: DynamicPlaceholderProps): string {
  const { getLabel, loading } = useFormLabels();

  if (loading) {
    return fallback || "";
  }

  return getLabel(placeholderKey, language, fallback);
}

/**
 * Dynamic description component for form fields
 */
interface DynamicDescriptionProps {
  descriptionKey: string;
  language?: "el" | "en";
  fallback?: string;
  className?: string;
}

export function DynamicDescription({
  descriptionKey,
  language = "en",
  fallback,
  className,
}: DynamicDescriptionProps) {
  const { getLabel, loading } = useFormLabels();

  if (loading) {
    return (
      <div className={cn("animate-pulse bg-gray-200 rounded h-3 w-32", className)}>
        &nbsp;
      </div>
    );
  }

  const descriptionText = getLabel(descriptionKey, language, fallback);

  if (!descriptionText) {
    return null;
  }

  return (
    <p className={cn("text-sm text-muted-foreground", className)}>
      {descriptionText}
    </p>
  );
}

/**
 * Dynamic button component
 */
interface DynamicButtonProps {
  buttonKey: string;
  language?: "el" | "en";
  fallback?: string;
  className?: string;
  onClick?: () => void;
  type?: "button" | "submit" | "reset";
  disabled?: boolean;
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
}

export function DynamicButton({
  buttonKey,
  language = "en",
  fallback,
  className,
  onClick,
  type = "button",
  disabled = false,
}: DynamicButtonProps) {
  const { getLabel, loading } = useFormLabels();

  const buttonText = loading ? (fallback || buttonKey) : getLabel(buttonKey, language, fallback);

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled || loading}
      className={className}
    >
      {buttonText}
    </button>
  );
}

/**
 * Dynamic title component
 */
interface DynamicTitleProps {
  titleKey: string;
  language?: "el" | "en";
  fallback?: string;
  className?: string;
  level?: 1 | 2 | 3 | 4 | 5 | 6;
}

export function DynamicTitle({
  titleKey,
  language = "en",
  fallback,
  className,
  level = 2,
}: DynamicTitleProps) {
  const { getLabel, loading } = useFormLabels();

  if (loading) {
    const HeadingComponent = `h${level}` as keyof JSX.IntrinsicElements;
    return (
      <HeadingComponent className={cn("animate-pulse bg-gray-200 rounded h-6 w-48", className)}>
        &nbsp;
      </HeadingComponent>
    );
  }

  const titleText = getLabel(titleKey, language, fallback);
  const HeadingComponent = `h${level}` as keyof JSX.IntrinsicElements;

  return (
    <HeadingComponent className={className}>
      {titleText}
    </HeadingComponent>
  );
}

/**
 * Fallback label component that uses hardcoded text if dynamic loading fails
 */
interface FallbackLabelProps {
  labelKey: string;
  language?: "el" | "en";
  fallbackText: string;
  className?: string;
  as?: "label" | "span" | "div" | "p";
}

export function FallbackLabel({
  labelKey,
  language = "en",
  fallbackText,
  className,
  as: Component = "span",
}: FallbackLabelProps) {
  const { getLabel, hasLabel, loading } = useFormLabels();

  // If loading or label doesn't exist, use fallback
  if (loading || !hasLabel(labelKey)) {
    return <Component className={className}>{fallbackText}</Component>;
  }

  const labelText = getLabel(labelKey, language, fallbackText);

  return <Component className={className}>{labelText}</Component>;
}

/**
 * Hook for getting multiple labels at once
 */
export function useMultipleLabels(labelKeys: string[], language: "el" | "en" = "en") {
  const { getLabel, loading } = useFormLabels();

  const labels = labelKeys.reduce((acc, key) => {
    acc[key] = getLabel(key, language);
    return acc;
  }, {} as Record<string, string>);

  return {
    labels,
    loading,
    getLabel: (key: string, fallback?: string) => getLabel(key, language, fallback),
  };
}
