import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Form } from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { DynamicFormField, FallbackFormField } from "../DynamicFormField";

// Example form schema
const exampleFormSchema = z.object({
  candidateType: z.string().min(1, "Please select a candidate type"),
  musicalInstruments: z.array(z.string()).optional(),
  education: z.array(z.string()).optional(),
  firstAid: z.array(z.string()).optional(),
  socialMediaPlatform: z.string().optional(),
});

type ExampleFormData = z.infer<typeof exampleFormSchema>;

export function DynamicFormExample() {
  const form = useForm<ExampleFormData>({
    resolver: zodResolver(exampleFormSchema),
    defaultValues: {
      candidateType: "",
      musicalInstruments: [],
      education: [],
      firstAid: [],
      socialMediaPlatform: "",
    },
  });

  const onSubmit = (data: ExampleFormData) => {
    console.log("Form submitted:", data);
    alert("Form submitted! Check console for data.");
  };

  return (
    <Card className="max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Dynamic Form Configuration Example</CardTitle>
        <p className="text-muted-foreground">
          This form demonstrates how to use dynamic form fields that load their options from the database.
        </p>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Dynamic form fields that load from database */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <DynamicFormField
                control={form.control}
                name="candidateType"
                configName="candidateType"
                language="en"
                required
              />

              <DynamicFormField
                control={form.control}
                name="socialMediaPlatform"
                configName="socialMediaPlatform"
                language="en"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <DynamicFormField
                control={form.control}
                name="musicalInstruments"
                configName="musicalInstruments"
                language="en"
              />

              <DynamicFormField
                control={form.control}
                name="education"
                configName="education"
                language="en"
              />
            </div>

            <DynamicFormField
              control={form.control}
              name="firstAid"
              configName="firstAid"
              language="en"
            />

            {/* Example of fallback form field with hardcoded options */}
            <div className="border-t pt-6">
              <h3 className="text-lg font-semibold mb-4">Fallback Example (Hardcoded Options)</h3>
              <FallbackFormField
                control={form.control}
                name="candidateType"
                title="Candidate Type (Fallback)"
                options={[
                  { id: "nanny", labelEl: "Nanny", labelEn: "Nanny" },
                  { id: "tutor", labelEl: "Tutor", labelEn: "Tutor" },
                  { id: "both", labelEl: "Και τα δύο", labelEn: "Both" },
                ]}
                fieldType="radio"
                language="en"
                description="This field uses hardcoded options as a fallback"
              />
            </div>

            <div className="flex gap-4">
              <Button type="submit">Submit Form</Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => form.reset()}
              >
                Reset
              </Button>
            </div>

            {/* Display current form values for debugging */}
            <div className="mt-6 p-4 bg-muted rounded-lg">
              <h4 className="font-semibold mb-2">Current Form Values:</h4>
              <pre className="text-sm overflow-auto">
                {JSON.stringify(form.watch(), null, 2)}
              </pre>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}

// Example of how to integrate dynamic fields into existing forms
export function IntegrationExample() {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Integration Guide</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-semibold">1. Replace hardcoded options with dynamic fields:</h4>
            <pre className="text-sm bg-muted p-3 rounded mt-2">
{`// Before (hardcoded)
<FormField
  control={control}
  name="musicalInstruments"
  render={({ field }) => (
    <FormItem>
      <FormLabel>Musical Instruments</FormLabel>
      <div className="grid grid-cols-2 gap-2">
        {musicalInstrumentOptions.map((option) => (
          <Checkbox key={option.id} ... />
        ))}
      </div>
    </FormItem>
  )}
/>

// After (dynamic)
<DynamicFormField
  control={control}
  name="musicalInstruments"
  configName="musicalInstruments"
  language="en"
/>`}
            </pre>
          </div>

          <div>
            <h4 className="font-semibold">2. Add fallback for reliability:</h4>
            <pre className="text-sm bg-muted p-3 rounded mt-2">
{`// Use FallbackFormField as backup
<FallbackFormField
  control={control}
  name="musicalInstruments"
  title="Musical Instruments"
  options={musicalInstrumentOptions}
  fieldType="multiselect"
  language="en"
/>`}
            </pre>
          </div>

          <div>
            <h4 className="font-semibold">3. Benefits:</h4>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>Admin can modify options without code changes</li>
              <li>Consistent multilingual support</li>
              <li>Easy reordering of options</li>
              <li>Centralized configuration management</li>
              <li>Fallback support for reliability</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
