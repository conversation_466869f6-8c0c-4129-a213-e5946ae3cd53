import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Form } from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { FormThemeProvider } from "@/contexts/FormThemeContext";
import { useFormThemes, useThemeSelection } from "@/hooks/useFormTheme";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { DynamicFormField } from "../DynamicFormField";
import {
  ThemedButton,
  ThemedCard,
  ThemedFormLayout,
  ThemedText,
  ThemePreview,
} from "../ThemedFormLayout";

// Example form schema
const themedFormSchema = z.object({
  candidateType: z.string().min(1, "Please select a candidate type"),
  musicalInstruments: z.array(z.string()).optional(),
  education: z.array(z.string()).optional(),
});

type ThemedFormData = z.infer<typeof themedFormSchema>;

function ThemeSelector() {
  const { themes, loading } = useFormThemes();
  const { currentTheme, selectTheme, resetToDefault, isUsingDefault } = useThemeSelection();

  if (loading) {
    return <div className="text-sm text-muted-foreground">Loading themes...</div>;
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Select Theme</h3>
        {!isUsingDefault && (
          <Button variant="outline" size="sm" onClick={resetToDefault}>
            Reset to Default
          </Button>
        )}
      </div>
      
      <Select
        value={currentTheme?.id || ""}
        onValueChange={(value) => selectTheme(value)}
      >
        <SelectTrigger>
          <SelectValue placeholder="Choose a theme" />
        </SelectTrigger>
        <SelectContent>
          {themes.map((theme) => (
            <SelectItem key={theme.id} value={theme.id}>
              {theme.titleEn} {theme.isDefault && "(Default)"}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {currentTheme && (
        <div className="mt-4">
          <h4 className="text-sm font-medium mb-2">Current Theme Preview:</h4>
          <ThemePreview themeName={currentTheme.titleEn} className="max-w-md" />
        </div>
      )}
    </div>
  );
}

function ThemedFormContent() {
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 3;
  const stepTitles = ["Personal Info", "Skills", "Review"];

  const form = useForm<ThemedFormData>({
    resolver: zodResolver(themedFormSchema),
    defaultValues: {
      candidateType: "",
      musicalInstruments: [],
      education: [],
    },
  });

  const onSubmit = (data: ThemedFormData) => {
    console.log("Themed form submitted:", data);
    alert("Form submitted! Check console for data.");
  };

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  return (
    <ThemedFormLayout
      title="Themed Form Example"
      description="Experience how different themes change the look and feel of your forms"
      currentStep={currentStep}
      totalSteps={totalSteps}
      stepTitles={stepTitles}
      showProgress={true}
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Step 1: Personal Info */}
          {currentStep === 1 && (
            <div className="space-y-6">
              <ThemedText variant="primary" className="text-xl font-semibold">
                Step 1: Personal Information
              </ThemedText>
              
              <DynamicFormField
                control={form.control}
                name="candidateType"
                configName="candidateType"
                language="en"
                required
              />

              <div className="flex justify-end">
                <ThemedButton variant="primary" onClick={nextStep}>
                  Next Step
                </ThemedButton>
              </div>
            </div>
          )}

          {/* Step 2: Skills */}
          {currentStep === 2 && (
            <div className="space-y-6">
              <ThemedText variant="primary" className="text-xl font-semibold">
                Step 2: Skills & Education
              </ThemedText>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <DynamicFormField
                  control={form.control}
                  name="musicalInstruments"
                  configName="musicalInstruments"
                  language="en"
                />

                <DynamicFormField
                  control={form.control}
                  name="education"
                  configName="education"
                  language="en"
                />
              </div>

              <div className="flex justify-between">
                <ThemedButton variant="secondary" onClick={prevStep}>
                  Previous
                </ThemedButton>
                <ThemedButton variant="primary" onClick={nextStep}>
                  Next Step
                </ThemedButton>
              </div>
            </div>
          )}

          {/* Step 3: Review */}
          {currentStep === 3 && (
            <div className="space-y-6">
              <ThemedText variant="primary" className="text-xl font-semibold">
                Step 3: Review & Submit
              </ThemedText>

              <ThemedCard title="Form Summary">
                <div className="space-y-4">
                  <div>
                    <ThemedText variant="secondary" className="font-medium">
                      Candidate Type:
                    </ThemedText>
                    <ThemedText variant="primary">
                      {form.watch("candidateType") || "Not selected"}
                    </ThemedText>
                  </div>

                  <div>
                    <ThemedText variant="secondary" className="font-medium">
                      Musical Instruments:
                    </ThemedText>
                    <ThemedText variant="primary">
                      {form.watch("musicalInstruments")?.length || 0} selected
                    </ThemedText>
                  </div>

                  <div>
                    <ThemedText variant="secondary" className="font-medium">
                      Education:
                    </ThemedText>
                    <ThemedText variant="primary">
                      {form.watch("education")?.length || 0} selected
                    </ThemedText>
                  </div>
                </div>
              </ThemedCard>

              <div className="flex justify-between">
                <ThemedButton variant="secondary" onClick={prevStep}>
                  Previous
                </ThemedButton>
                <ThemedButton variant="primary" type="submit">
                  Submit Form
                </ThemedButton>
              </div>
            </div>
          )}

          {/* Debug Info */}
          <ThemedCard title="Debug Information" className="mt-8">
            <div className="space-y-2">
              <ThemedText variant="secondary" className="text-sm">
                Current form values:
              </ThemedText>
              <pre className="text-xs bg-muted p-2 rounded overflow-auto">
                {JSON.stringify(form.watch(), null, 2)}
              </pre>
            </div>
          </ThemedCard>
        </form>
      </Form>
    </ThemedFormLayout>
  );
}

export function ThemedFormExample() {
  return (
    <FormThemeProvider>
      <div className="space-y-8">
        {/* Theme Selector */}
        <Card className="max-w-2xl mx-auto">
          <CardHeader>
            <CardTitle>Theme Configuration</CardTitle>
          </CardHeader>
          <CardContent>
            <ThemeSelector />
          </CardContent>
        </Card>

        {/* Themed Form */}
        <ThemedFormContent />

        {/* Integration Guide */}
        <Card className="max-w-4xl mx-auto">
          <CardHeader>
            <CardTitle>Integration Guide</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-semibold">1. Wrap your app with FormThemeProvider:</h4>
              <pre className="text-sm bg-muted p-3 rounded mt-2">
{`import { FormThemeProvider } from "@/contexts/FormThemeContext";

function App() {
  return (
    <FormThemeProvider>
      <YourFormComponents />
    </FormThemeProvider>
  );
}`}
              </pre>
            </div>

            <div>
              <h4 className="font-semibold">2. Use themed components:</h4>
              <pre className="text-sm bg-muted p-3 rounded mt-2">
{`import { 
  ThemedFormLayout, 
  ThemedButton, 
  ThemedText 
} from "@/components/forms/ThemedFormLayout";

function MyForm() {
  return (
    <ThemedFormLayout
      title="My Form"
      currentStep={1}
      totalSteps={3}
      stepTitles={["Step 1", "Step 2", "Step 3"]}
    >
      <ThemedText variant="primary">Form content</ThemedText>
      <ThemedButton variant="primary">Submit</ThemedButton>
    </ThemedFormLayout>
  );
}`}
              </pre>
            </div>

            <div>
              <h4 className="font-semibold">3. Use theme hooks for custom styling:</h4>
              <pre className="text-sm bg-muted p-3 rounded mt-2">
{`import { useThemeClasses } from "@/contexts/FormThemeContext";

function CustomComponent() {
  const themeClasses = useThemeClasses();
  
  return (
    <div className={themeClasses.formBackground}>
      <button className={themeClasses.primaryButton}>
        Themed Button
      </button>
    </div>
  );
}`}
              </pre>
            </div>

            <div>
              <h4 className="font-semibold">4. Benefits:</h4>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li>Consistent theming across all forms</li>
                <li>Easy theme switching without code changes</li>
                <li>Admin-controlled color schemes</li>
                <li>Progress bar customization</li>
                <li>Responsive design support</li>
                <li>Accessibility-friendly color combinations</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </FormThemeProvider>
  );
}
