import {
  Briefcase,
  Check,
  Clock,
  FileText,
  Heart,
  Home,
  Info,
  User,
} from "lucide-react";
import React from "react";
import { useCandidateFormContext } from "../../contexts/CandidateFormContext";
import { useFormContext } from "../../contexts/FormContext";
import { useNannyRequestFormContext } from "../../contexts/NannyRequestFormContext";
import { formSteps, nannyRequestFormSteps } from "../../schemas/FormSchema";

const candidateStepTitles = [
  { el: "Προσωπικά Στοιχεία & Διεύθυνση", en: "Personal Info & Address" },
  { el: "Επαγγελματικό Προφίλ", en: "Career Profile" },
  {
    el: "Επαφή Έκτακτης Ανάγκης & Έγγραφα",
    en: "Emergency Contact & Documents",
  },
];

const nannyRequestStepTitles = [
  { el: "Στοιχεία Οικογένειας", en: "Family Information" },
  { el: "Λεπτομέρειες Θέσης", en: "Position Details" },
  { el: "Προτιμήσεις Υποψηφίου", en: "Candidate Preferences" },
];

// Icons for each step - candidate form (more formal)
const candidateStepIcons = [
  <User className="w-4 h-4" />,
  <Briefcase className="w-4 h-4" />,
  <FileText className="w-4 h-4" />,
];

// Icons for each step - family form (warm and friendly)
const familyStepIcons = [
  <Home className="w-4 h-4" />,
  <Info className="w-4 h-4" />,
  <Heart className="w-4 h-4" />,
];

interface StepNavigationProps {
  isCandidateForm?: boolean;
}

export const StepNavigation: React.FC<StepNavigationProps> = ({
  isCandidateForm = true,
}) => {
  // Determine which context to use based on form type
  const useAppropriateContext = () => {
    if (isCandidateForm) {
      try {
        return useCandidateFormContext();
      } catch (error) {
        return useFormContext(); // Fallback to the original context
      }
    } else {
      try {
        return useNannyRequestFormContext();
      } catch (error) {
        return useFormContext(); // Fallback to the original context
      }
    }
  };

  const {
    currentStep,
    setCurrentStep,
    language,
    visitedSteps,
    validateCurrentStep,
  } = useAppropriateContext();

  // Determine form type based on isCandidateForm prop
  const formType = isCandidateForm ? "candidate" : "nannyRequest";

  const stepTitles =
    formType === "nannyRequest" ? nannyRequestStepTitles : candidateStepTitles;
  const currentFormSteps =
    formType === "nannyRequest" ? nannyRequestFormSteps : formSteps;
  const stepIcons = isCandidateForm ? candidateStepIcons : familyStepIcons;

  const handleStepClick = async (index: number, validateCurrentStep: any) => {
    // If trying to go to a future step, validate the current step first
    if (index > currentStep) {
      // Get the current step schema based on form type
      const steps = isCandidateForm ? formSteps : nannyRequestFormSteps;
      const currentStepSchema =
        steps[Math.min(currentStep, steps.length - 1)].schema;

      // Extract field names from the current step's schema
      const currentStepFields = Object.keys(currentStepSchema.shape || {});

      // Validate the current step before allowing navigation
      const form = document.querySelector("form");
      if (form) {
        const isValid = await validateStep(
          currentStepFields,
          validateCurrentStep
        );
        if (!isValid) {
          return; // Don't navigate if validation fails
        }
      }
    }

    // Allow navigation only to steps that have been visited or the next step if current is valid
    if (visitedSteps.includes(index) || index === currentStep + 1) {
      setCurrentStep(index);
    }
  };

  // Helper function to validate the current step using Zod schema
  const validateStep = async (fields: string[], validateCurrentStep?: any) => {
    console.debug("Validating step with fields:", fields);

    // Use the validateCurrentStep function from the form context if available
    if (validateCurrentStep && validateCurrentStep.current) {
      try {
        // This will trigger the Zod schema validation for the current step
        const isValid = await validateCurrentStep.current();
        console.debug("Zod schema validation result:", isValid);
        return isValid;
      } catch (error) {
        console.error("Error validating step with Zod schema:", error);
      }
    }

    // Fallback to basic validation if the context validation fails
    console.debug("Falling back to basic validation");
    const form = document.querySelector("form");
    if (!form) return false;

    // Check if all required fields in the current step are filled
    let isValid = true;
    fields.forEach((field) => {
      const input = form.querySelector(`[name="${field}"]`);
      if (
        input &&
        input.hasAttribute("required") &&
        !(input as HTMLInputElement).value
      ) {
        isValid = false;
        // Add error styling
        input.classList.add("border-red-500");
      }
    });

    return isValid;
  };

  // Colors based on form type - now using consistent color scheme
  const getColors = () => {
    // Use the same color scheme for both form types for consistency
    return {
      active:
        "bg-gradient-to-r from-primary to-accent text-secondary-foreground",
      visited: "bg-accent/20 text-primary",
      inactive: "bg-gray-100 text-gray-400",
      progressBar: "bg-accent/10",
      progressFill: "bg-gradient-to-r from-primary/30 to-accent/30",
      textCurrent: "text-primary",
      textVisited: "text-primary/80",
      textInactive: "text-gray-400",
      checkColor: "text-primary",
    };
  };

  const colors = getColors();

  // Animation delays for the step bubbles
  const getAnimationDelay = (index: number) => {
    return `${index * 0.1}s`;
  };

  return (
    <div className="mb-10 relative">
      <div className="flex justify-between items-center mb-4">
        <span className={`font-medium text-primary flex items-center`}>
          <Clock className="w-4 h-4 mr-2 text-primary" />
          {language === "el" ? "Βήμα" : "Step"} {currentStep + 1}/
          {currentFormSteps.length}
        </span>
        <span className={`font-medium text-primary flex items-center`}>
          {stepTitles[currentStep][language]}
          {!isCandidateForm && (
            <Home className="w-4 h-4 ml-2 text-primary animate-pulse-soft" />
          )}
        </span>
      </div>

      <div
        className={`w-full ${colors.progressBar} rounded-full h-4 mb-8 relative overflow-hidden`}
      >
        <div
          className={`${colors.progressFill} h-4 rounded-full transition-all duration-500 ease-in-out relative`}
          style={{
            width: `${((currentStep + 1) / currentFormSteps.length) * 100}%`,
          }}
        ></div>

        {/* Animated sparkles along the progress bar for both form types */}
        <Info
          className="absolute top-0 right-1/4 transform -translate-y-1/2 h-4 w-4 text-white opacity-70 animate-float"
          style={{ animationDelay: "0.5s" }}
        />
      </div>

      <div className="flex justify-between mt-2 relative">
        {stepTitles.map((title, index) => (
          <button
            key={index}
            onClick={() => handleStepClick(index, validateCurrentStep)}
            disabled={!visitedSteps.includes(index)}
            className={`relative flex flex-col items-center group transition-all `}
            style={
              !isCandidateForm
                ? { animationDelay: getAnimationDelay(index) }
                : undefined
            }
          >
            <div
              className={`${"w-14 h-14"} flex items-center justify-center rounded-full mb-2 transition-all ${index === currentStep
                ? colors.active + " text-secondary-foreground shadow-md"
                : visitedSteps.includes(index)
                  ? colors.visited + " hover:shadow-sm cursor-pointer"
                  : colors.inactive + " cursor-not-allowed"
                }`}
            >
              {visitedSteps.includes(index) && index < currentStep ? (
                <Check
                  className={`w-5 h-5 ${isCandidateForm ? "" : colors.checkColor
                    }`}
                />
              ) : (
                <div className="flex flex-col items-center justify-center">
                  <div className="text-sm font-semibold">{index + 1}</div>
                  {stepIcons[index]}
                </div>
              )}
            </div>

            <span
              className={`text-xs font-medium transition-all duration-300 opacity-0 group-hover:opacity-100 w-32 text-center absolute -bottom-5 ${index === currentStep
                ? colors.textCurrent
                : visitedSteps.includes(index)
                  ? colors.textVisited
                  : colors.textInactive
                }`}
            >
              {title[language]}
            </span>
          </button>
        ))}
      </div>
    </div>
  );
};
