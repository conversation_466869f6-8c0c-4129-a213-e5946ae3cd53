import { FormFieldConfig } from "@/schemas/FormSchema";

/**
 * Utility functions for working with dynamic forms
 */

/**
 * Get dynamic label text with fallback
 */
export function getDynamicLabel(
  getLabel: (key: string, language: "el" | "en", fallback?: string) => string,
  labelKey: string,
  language: "el" | "en",
  fallback: string
): string {
  return getLabel(labelKey, language, fallback);
}

/**
 * Get dynamic placeholder text with fallback
 */
export function getDynamicPlaceholder(
  getLabel: (key: string, language: "el" | "en", fallback?: string) => string,
  placeholderKey: string,
  language: "el" | "en",
  fallback?: string
): string {
  return getLabel(placeholderKey, language, fallback || "");
}

/**
 * Get dynamic description text with fallback
 */
export function getDynamicDescription(
  getLabel: (key: string, language: "el" | "en", fallback?: string) => string,
  descriptionKey: string,
  language: "el" | "en",
  fallback?: string
): string {
  return getLabel(descriptionKey, language, fallback || "");
}

/**
 * Check if a field should be rendered based on its configuration
 */
export function shouldRenderField(fieldConfig: FormFieldConfig | null): boolean {
  return fieldConfig !== null && fieldConfig.isActive;
}

/**
 * Filter fields by category and form type
 */
export function filterFieldsByCategory(
  fields: FormFieldConfig[],
  category: string,
  formType: "candidate" | "nannyRequest" | "both" = "both"
): FormFieldConfig[] {
  return fields.filter(field => 
    field.category === category && 
    field.isActive && 
    (field.formType === formType || field.formType === "both")
  );
}

/**
 * Group fields by category
 */
export function groupFieldsByCategory(
  fields: FormFieldConfig[]
): Record<string, FormFieldConfig[]> {
  return fields.reduce((acc, field) => {
    if (!acc[field.category]) {
      acc[field.category] = [];
    }
    acc[field.category].push(field);
    return acc;
  }, {} as Record<string, FormFieldConfig[]>);
}

/**
 * Get field configuration by key
 */
export function getFieldConfigByKey(
  fields: FormFieldConfig[],
  fieldKey: string
): FormFieldConfig | null {
  return fields.find(field => field.fieldKey === fieldKey) || null;
}

/**
 * Check if a field is required
 */
export function isFieldRequired(fieldConfig: FormFieldConfig | null): boolean {
  return fieldConfig?.isRequired || false;
}

/**
 * Get field type for validation
 */
export function getFieldType(fieldConfig: FormFieldConfig | null): string {
  return fieldConfig?.fieldType || "text";
}

/**
 * Generate form field props for dynamic rendering
 */
export interface DynamicFieldProps {
  fieldKey: string;
  label: string;
  placeholder?: string;
  description?: string;
  required: boolean;
  fieldType: string;
  options?: Array<{ id: string; labelEl: string; labelEn: string }>;
}

export function generateFieldProps(
  fieldConfig: FormFieldConfig,
  getLabel: (key: string, language: "el" | "en", fallback?: string) => string,
  language: "el" | "en"
): DynamicFieldProps {
  return {
    fieldKey: fieldConfig.fieldKey,
    label: getDynamicLabel(getLabel, `${fieldConfig.fieldKey}.label`, language, fieldConfig.labelEn),
    placeholder: getDynamicPlaceholder(getLabel, `${fieldConfig.fieldKey}.placeholder`, language, fieldConfig.placeholderEn),
    description: getDynamicDescription(getLabel, `${fieldConfig.fieldKey}.description`, language),
    required: fieldConfig.isRequired,
    fieldType: fieldConfig.fieldType,
    options: fieldConfig.options,
  };
}

/**
 * Validate dynamic field value
 */
export function validateDynamicField(
  value: any,
  fieldConfig: FormFieldConfig
): { isValid: boolean; error?: string } {
  // Basic validation - can be extended
  if (fieldConfig.isRequired && (!value || (Array.isArray(value) && value.length === 0))) {
    return {
      isValid: false,
      error: "This field is required"
    };
  }

  if (fieldConfig.fieldType === "email" && value) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(value)) {
      return {
        isValid: false,
        error: "Please enter a valid email address"
      };
    }
  }

  if (fieldConfig.fieldType === "number" && value) {
    if (isNaN(Number(value))) {
      return {
        isValid: false,
        error: "Please enter a valid number"
      };
    }
  }

  return { isValid: true };
}

/**
 * Transform form data for submission
 */
export function transformFormData(
  formData: Record<string, any>,
  fieldConfigs: FormFieldConfig[]
): Record<string, any> {
  const transformed = { ...formData };

  fieldConfigs.forEach(config => {
    const value = transformed[config.fieldKey];
    
    // Transform based on field type
    switch (config.fieldType) {
      case "number":
        if (value !== undefined && value !== null && value !== "") {
          transformed[config.fieldKey] = Number(value);
        }
        break;
      case "multiselect":
        if (!Array.isArray(value)) {
          transformed[config.fieldKey] = value ? [value] : [];
        }
        break;
      case "checkbox":
      case "switch":
        transformed[config.fieldKey] = Boolean(value);
        break;
    }
  });

  return transformed;
}

/**
 * Get default value for a field based on its type
 */
export function getDefaultFieldValue(fieldConfig: FormFieldConfig): any {
  switch (fieldConfig.fieldType) {
    case "multiselect":
      return [];
    case "checkbox":
    case "switch":
      return false;
    case "number":
      return "";
    default:
      return "";
  }
}

/**
 * Create default values object for a form
 */
export function createDefaultValues(fieldConfigs: FormFieldConfig[]): Record<string, any> {
  const defaults: Record<string, any> = {};
  
  fieldConfigs.forEach(config => {
    if (config.isActive) {
      defaults[config.fieldKey] = getDefaultFieldValue(config);
    }
  });

  return defaults;
}

/**
 * Check if form data has changed from defaults
 */
export function hasFormDataChanged(
  formData: Record<string, any>,
  defaultValues: Record<string, any>
): boolean {
  return Object.keys(formData).some(key => {
    const current = formData[key];
    const defaultVal = defaultValues[key];
    
    if (Array.isArray(current) && Array.isArray(defaultVal)) {
      return current.length !== defaultVal.length || 
             current.some((item, index) => item !== defaultVal[index]);
    }
    
    return current !== defaultVal;
  });
}

/**
 * Get field dependencies (for conditional fields)
 */
export function getFieldDependencies(
  fieldConfig: FormFieldConfig
): string[] {
  // This can be extended to support conditional fields
  // For now, return empty array
  return [];
}

/**
 * Check if field should be visible based on dependencies
 */
export function shouldShowField(
  fieldConfig: FormFieldConfig,
  formData: Record<string, any>
): boolean {
  if (!fieldConfig.isActive) {
    return false;
  }

  // Add conditional logic here if needed
  // For now, show all active fields
  return true;
}

/**
 * Sort fields by display order
 */
export function sortFieldsByOrder(fields: FormFieldConfig[]): FormFieldConfig[] {
  return [...fields].sort((a, b) => {
    // If displayOrder is not set, use fieldKey for consistent sorting
    const orderA = a.displayOrder ?? a.fieldKey;
    const orderB = b.displayOrder ?? b.fieldKey;
    
    if (typeof orderA === "number" && typeof orderB === "number") {
      return orderA - orderB;
    }
    
    return String(orderA).localeCompare(String(orderB));
  });
}
