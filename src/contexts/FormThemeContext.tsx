import { FormThemeConfig } from "@/schemas/FormSchema";
import { fetchDefaultFormTheme } from "@/services/formThemeService";
import { createContext, ReactNode, useContext, useEffect, useState } from "react";

interface FormThemeContextType {
  currentTheme: FormThemeConfig | null;
  setTheme: (theme: FormThemeConfig) => void;
  resetToDefault: () => void;
  loading: boolean;
}

const FormThemeContext = createContext<FormThemeContextType | undefined>(undefined);

interface FormThemeProviderProps {
  children: ReactNode;
}

export function FormThemeProvider({ children }: FormThemeProviderProps) {
  const [currentTheme, setCurrentTheme] = useState<FormThemeConfig | null>(null);
  const [loading, setLoading] = useState(true);

  // Load default theme on mount
  useEffect(() => {
    loadDefaultTheme();
  }, []);

  const loadDefaultTheme = async () => {
    setLoading(true);
    try {
      const defaultTheme = await fetchDefaultFormTheme();
      if (defaultTheme) {
        setCurrentTheme(defaultTheme);
        applyThemeToDOM(defaultTheme);
      }
    } catch (error) {
      console.error("Error loading default theme:", error);
    } finally {
      setLoading(false);
    }
  };

  const setTheme = (theme: FormThemeConfig) => {
    setCurrentTheme(theme);
    applyThemeToDOM(theme);
    // Store in localStorage for persistence
    localStorage.setItem('formTheme', JSON.stringify(theme));
  };

  const resetToDefault = () => {
    loadDefaultTheme();
    localStorage.removeItem('formTheme');
  };

  // Apply theme colors to DOM via CSS custom properties
  const applyThemeToDOM = (theme: FormThemeConfig) => {
    const root = document.documentElement;
    const colors = theme.colors;

    // Convert Tailwind classes to CSS values (simplified mapping)
    const tailwindToCSS = (className: string): string => {
      // This is a simplified conversion - in a real app you might want a more robust solution
      const colorMap: Record<string, string> = {
        'bg-primary': 'var(--primary)',
        'bg-secondary': 'var(--secondary)',
        'bg-accent': 'var(--accent)',
        'text-primary': 'var(--primary)',
        'text-secondary': 'var(--secondary)',
        'bg-white': '#ffffff',
        'bg-gray-100': '#f3f4f6',
        'bg-blue-500': '#3b82f6',
        'bg-green-500': '#10b981',
        'bg-red-500': '#ef4444',
        'text-green-600': '#059669',
        'text-red-600': '#dc2626',
        'text-destructive': 'var(--destructive)',
      };

      // Extract the main color class (remove hover states, gradients, etc.)
      const mainClass = className.split(' ')[0];
      return colorMap[mainClass] || className;
    };

    // Set CSS custom properties for the theme
    root.style.setProperty('--form-progress-bg', tailwindToCSS(colors.progressBarBg));
    root.style.setProperty('--form-progress-fill', tailwindToCSS(colors.progressBarFill));
    root.style.setProperty('--form-background', tailwindToCSS(colors.formBackground));
    root.style.setProperty('--form-card-background', tailwindToCSS(colors.cardBackground));
    root.style.setProperty('--form-primary-text', tailwindToCSS(colors.primaryText));
    root.style.setProperty('--form-secondary-text', tailwindToCSS(colors.secondaryText));
    root.style.setProperty('--form-primary-button', tailwindToCSS(colors.primaryButton));
    root.style.setProperty('--form-secondary-button', tailwindToCSS(colors.secondaryButton));
    root.style.setProperty('--form-border-color', tailwindToCSS(colors.borderColor));
    root.style.setProperty('--form-accent-color', tailwindToCSS(colors.accentColor));
    root.style.setProperty('--form-success-color', tailwindToCSS(colors.successColor));
    root.style.setProperty('--form-error-color', tailwindToCSS(colors.errorColor));
    root.style.setProperty('--form-step-active', tailwindToCSS(colors.stepActiveColor));
    root.style.setProperty('--form-step-inactive', tailwindToCSS(colors.stepInactiveColor));
    root.style.setProperty('--form-step-completed', tailwindToCSS(colors.stepCompletedColor));

    // Add theme class to body for CSS targeting
    document.body.className = document.body.className.replace(/theme-\w+/g, '');
    document.body.classList.add(`theme-${theme.name}`);
  };

  // Load theme from localStorage on mount
  useEffect(() => {
    const savedTheme = localStorage.getItem('formTheme');
    if (savedTheme) {
      try {
        const theme = JSON.parse(savedTheme);
        setCurrentTheme(theme);
        applyThemeToDOM(theme);
      } catch (error) {
        console.error("Error parsing saved theme:", error);
        loadDefaultTheme();
      }
    }
  }, []);

  const value: FormThemeContextType = {
    currentTheme,
    setTheme,
    resetToDefault,
    loading,
  };

  return (
    <FormThemeContext.Provider value={value}>
      {children}
    </FormThemeContext.Provider>
  );
}

export function useFormTheme() {
  const context = useContext(FormThemeContext);
  if (context === undefined) {
    throw new Error('useFormTheme must be used within a FormThemeProvider');
  }
  return context;
}

// Hook for getting theme-aware CSS classes
export function useThemeClasses() {
  const { currentTheme } = useFormTheme();

  if (!currentTheme) {
    return {
      progressBar: "bg-primary/20",
      progressFill: "bg-gradient-to-r from-primary to-secondary",
      formBackground: "bg-gradient-to-b from-accent/20 via-background to-accent/20",
      cardBackground: "bg-white",
      primaryText: "text-primary",
      secondaryText: "text-secondary",
      primaryButton: "bg-primary hover:bg-primary-hover",
      secondaryButton: "bg-secondary hover:bg-secondary-hover",
      stepActive: "bg-primary",
      stepInactive: "bg-muted",
      stepCompleted: "bg-green-500",
    };
  }

  const colors = currentTheme.colors;
  return {
    progressBar: colors.progressBarBg,
    progressFill: colors.progressBarFill,
    formBackground: colors.formBackground,
    cardBackground: colors.cardBackground,
    primaryText: colors.primaryText,
    secondaryText: colors.secondaryText,
    primaryButton: colors.primaryButton,
    secondaryButton: colors.secondaryButton,
    stepActive: colors.stepActiveColor,
    stepInactive: colors.stepInactiveColor,
    stepCompleted: colors.stepCompletedColor,
    borderColor: colors.borderColor,
    accentColor: colors.accentColor,
    successColor: colors.successColor,
    errorColor: colors.errorColor,
  };
}

// Utility function to generate CSS for theme
export function generateThemeCSS(theme: FormThemeConfig): string {
  const colors = theme.colors;
  
  return `
    .theme-${theme.name} {
      --form-progress-bg: ${colors.progressBarBg};
      --form-progress-fill: ${colors.progressBarFill};
      --form-background: ${colors.formBackground};
      --form-card-background: ${colors.cardBackground};
      --form-primary-text: ${colors.primaryText};
      --form-secondary-text: ${colors.secondaryText};
      --form-primary-button: ${colors.primaryButton};
      --form-secondary-button: ${colors.secondaryButton};
      --form-border-color: ${colors.borderColor};
      --form-accent-color: ${colors.accentColor};
      --form-success-color: ${colors.successColor};
      --form-error-color: ${colors.errorColor};
      --form-step-active: ${colors.stepActiveColor};
      --form-step-inactive: ${colors.stepInactiveColor};
      --form-step-completed: ${colors.stepCompletedColor};
    }

    .theme-${theme.name} .form-progress-bar {
      background: var(--form-progress-bg);
    }

    .theme-${theme.name} .form-progress-fill {
      background: var(--form-progress-fill);
    }

    .theme-${theme.name} .form-background {
      background: var(--form-background);
    }

    .theme-${theme.name} .form-card {
      background: var(--form-card-background);
    }

    .theme-${theme.name} .form-primary-text {
      color: var(--form-primary-text);
    }

    .theme-${theme.name} .form-secondary-text {
      color: var(--form-secondary-text);
    }

    .theme-${theme.name} .form-primary-button {
      background: var(--form-primary-button);
    }

    .theme-${theme.name} .form-secondary-button {
      background: var(--form-secondary-button);
    }

    .theme-${theme.name} .form-step-active {
      background: var(--form-step-active);
    }

    .theme-${theme.name} .form-step-inactive {
      background: var(--form-step-inactive);
    }

    .theme-${theme.name} .form-step-completed {
      background: var(--form-step-completed);
    }
  `;
}
