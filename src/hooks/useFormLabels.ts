import { FormLabelCategory, FormLabelConfig } from "@/schemas/FormSchema";
import {
  fetchFormLabelByKey,
  fetchFormLabelCategories,
  fetchFormLabelsByCategory,
} from "@/services/formLabelService";
import { useEffect, useState } from "react";

/**
 * Hook to fetch and manage form label categories
 */
export function useFormLabelCategories() {
  const [categories, setCategories] = useState<FormLabelCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadCategories = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await fetchFormLabelCategories();
      setCategories(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load form label categories");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadCategories();
  }, []);

  return {
    categories,
    loading,
    error,
    refetch: loadCategories,
  };
}

/**
 * Hook to fetch form labels by category
 */
export function useFormLabelsByCategory(category: string) {
  const [labels, setLabels] = useState<FormLabelConfig[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadLabels = async () => {
    if (!category) return;
    
    setLoading(true);
    setError(null);
    try {
      const data = await fetchFormLabelsByCategory(category);
      setLabels(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load form labels");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadLabels();
  }, [category]);

  return {
    labels,
    loading,
    error,
    refetch: loadLabels,
  };
}

/**
 * Hook to fetch a specific form label by key
 */
export function useFormLabel(key: string) {
  const [label, setLabel] = useState<FormLabelConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadLabel = async () => {
    if (!key) return;
    
    setLoading(true);
    setError(null);
    try {
      const data = await fetchFormLabelByKey(key);
      setLabel(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load form label");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadLabel();
  }, [key]);

  return {
    label,
    loading,
    error,
    refetch: loadLabel,
  };
}

/**
 * Hook for managing form labels with caching
 */
export function useFormLabels() {
  const [labelsCache, setLabelsCache] = useState<Record<string, FormLabelConfig>>({});
  const [loading, setLoading] = useState(false);

  // Load all labels into cache
  const loadAllLabels = async () => {
    setLoading(true);
    try {
      const categories = await fetchFormLabelCategories();
      const allLabels: Record<string, FormLabelConfig> = {};
      
      categories.forEach(category => {
        category.labels.forEach(label => {
          allLabels[label.key] = label;
        });
      });
      
      setLabelsCache(allLabels);
    } catch (error) {
      console.error("Error loading form labels:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAllLabels();
  }, []);

  // Get label by key with fallback
  const getLabel = (key: string, language: "el" | "en" = "en", fallback?: string): string => {
    const label = labelsCache[key];
    if (!label) {
      return fallback || key;
    }
    return language === "el" ? label.labelEl : label.labelEn;
  };

  // Get label object by key
  const getLabelConfig = (key: string): FormLabelConfig | null => {
    return labelsCache[key] || null;
  };

  // Check if label exists
  const hasLabel = (key: string): boolean => {
    return key in labelsCache;
  };

  return {
    getLabel,
    getLabelConfig,
    hasLabel,
    loading,
    refetch: loadAllLabels,
    labelsCache,
  };
}

/**
 * Utility function to get localized label text
 */
export function getFormLabelText(
  label: FormLabelConfig | null, 
  language: "el" | "en" = "en", 
  fallback?: string
): string {
  if (!label) {
    return fallback || "";
  }
  return language === "el" ? label.labelEl : label.labelEn;
}

/**
 * Hook for form-specific labels (candidate or nannyRequest)
 */
export function useFormTypeLabels(formType: "candidate" | "nannyRequest" | "both" = "both") {
  const { categories, loading, error } = useFormLabelCategories();

  // Filter labels by form type
  const filteredCategories = categories.map(category => ({
    ...category,
    labels: category.labels.filter(label => 
      !label.formType || label.formType === "both" || label.formType === formType
    ),
  })).filter(category => 
    category.formType === "both" || category.formType === formType
  );

  // Create a flat map of all labels for easy access
  const labelsMap = filteredCategories.reduce((acc, category) => {
    category.labels.forEach(label => {
      acc[label.key] = label;
    });
    return acc;
  }, {} as Record<string, FormLabelConfig>);

  // Get label function
  const getLabel = (key: string, language: "el" | "en" = "en", fallback?: string): string => {
    const label = labelsMap[key];
    if (!label) {
      return fallback || key;
    }
    return language === "el" ? label.labelEl : label.labelEn;
  };

  return {
    categories: filteredCategories,
    labelsMap,
    getLabel,
    loading,
    error,
  };
}

/**
 * Context-specific label hook (for labels, placeholders, descriptions, etc.)
 */
export function useFormLabelsByContext(context: string, category?: string) {
  const { categories, loading, error } = useFormLabelCategories();

  const filteredLabels = categories
    .filter(cat => !category || cat.name === category)
    .flatMap(cat => cat.labels)
    .filter(label => label.context === context);

  const labelsMap = filteredLabels.reduce((acc, label) => {
    acc[label.key] = label;
    return acc;
  }, {} as Record<string, FormLabelConfig>);

  const getLabel = (key: string, language: "el" | "en" = "en", fallback?: string): string => {
    const label = labelsMap[key];
    if (!label) {
      return fallback || key;
    }
    return language === "el" ? label.labelEl : label.labelEn;
  };

  return {
    labels: filteredLabels,
    labelsMap,
    getLabel,
    loading,
    error,
  };
}
