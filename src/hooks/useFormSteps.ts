import { useEffect, useState } from 'react';
import { FormStep, fetchFormSteps } from '@/services/formStepService';

interface UseFormStepsReturn {
  steps: FormStep[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

/**
 * Hook to fetch and manage form steps
 * @param formType The form type to fetch steps for
 * @returns Object containing steps, loading state, error, and refetch function
 */
export function useFormSteps(formType: 'candidate' | 'nannyRequest' | 'both' = 'both'): UseFormStepsReturn {
  const [steps, setSteps] = useState<FormStep[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSteps = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await fetchFormSteps(formType);
      setSteps(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch form steps');
      console.error('Error fetching form steps:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSteps();
  }, [formType]);

  return {
    steps,
    loading,
    error,
    refetch: fetchSteps,
  };
}

/**
 * Hook to get a specific form step by key
 * @param stepKey The step key to find
 * @param formType The form type to search in
 * @returns The form step or null if not found
 */
export function useFormStep(stepKey: string, formType: 'candidate' | 'nannyRequest' | 'both' = 'both') {
  const { steps, loading, error } = useFormSteps(formType);
  
  const step = steps.find(s => s.stepKey === stepKey) || null;
  
  return {
    step,
    loading,
    error,
  };
}
