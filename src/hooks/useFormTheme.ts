import { FormThemeConfig } from "@/schemas/FormSchema";
import {
  fetchDefaultFormTheme,
  fetchFormThemeById,
  fetchFormThemes,
} from "@/services/formThemeService";
import { useEffect, useState } from "react";

/**
 * Hook to fetch and manage form themes
 */
export function useFormThemes() {
  const [themes, setThemes] = useState<FormThemeConfig[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadThemes = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await fetchFormThemes();
      setThemes(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load form themes");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadThemes();
  }, []);

  return {
    themes,
    loading,
    error,
    refetch: loadThemes,
  };
}

/**
 * Hook to fetch the default form theme
 */
export function useDefaultFormTheme() {
  const [theme, setTheme] = useState<FormThemeConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadDefaultTheme = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await fetchDefaultFormTheme();
      setTheme(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load default form theme");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDefaultTheme();
  }, []);

  return {
    theme,
    loading,
    error,
    refetch: loadDefaultTheme,
  };
}

/**
 * Hook to fetch a specific form theme by ID
 */
export function useFormThemeById(themeId: string | null) {
  const [theme, setTheme] = useState<FormThemeConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadTheme = async () => {
    if (!themeId) {
      setTheme(null);
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);
    try {
      const data = await fetchFormThemeById(themeId);
      setTheme(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load form theme");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadTheme();
  }, [themeId]);

  return {
    theme,
    loading,
    error,
    refetch: loadTheme,
  };
}

/**
 * Hook for managing theme selection and persistence
 */
export function useThemeSelection() {
  const [selectedThemeId, setSelectedThemeId] = useState<string | null>(null);
  const { theme: selectedTheme, loading: themeLoading } = useFormThemeById(selectedThemeId);
  const { theme: defaultTheme, loading: defaultLoading } = useDefaultFormTheme();

  // Load saved theme from localStorage on mount
  useEffect(() => {
    const savedThemeId = localStorage.getItem('selectedFormThemeId');
    if (savedThemeId) {
      setSelectedThemeId(savedThemeId);
    }
  }, []);

  // Save theme selection to localStorage
  const selectTheme = (themeId: string) => {
    setSelectedThemeId(themeId);
    localStorage.setItem('selectedFormThemeId', themeId);
  };

  // Reset to default theme
  const resetToDefault = () => {
    setSelectedThemeId(null);
    localStorage.removeItem('selectedFormThemeId');
  };

  // Get the current active theme (selected or default)
  const currentTheme = selectedTheme || defaultTheme;
  const loading = themeLoading || defaultLoading;

  return {
    currentTheme,
    selectedThemeId,
    selectTheme,
    resetToDefault,
    loading,
    isUsingDefault: !selectedThemeId,
  };
}

/**
 * Hook for applying theme classes to components
 */
export function useThemeStyles(themeId?: string) {
  const { currentTheme } = useThemeSelection();
  const { theme: specificTheme } = useFormThemeById(themeId || null);
  
  const theme = specificTheme || currentTheme;

  if (!theme) {
    // Return default styles if no theme is available
    return {
      progressBar: "bg-primary/20",
      progressFill: "bg-gradient-to-r from-primary to-secondary",
      formBackground: "bg-gradient-to-b from-accent/20 via-background to-accent/20",
      cardBackground: "bg-white",
      primaryText: "text-primary",
      secondaryText: "text-secondary",
      primaryButton: "bg-primary hover:bg-primary-hover",
      secondaryButton: "bg-secondary hover:bg-secondary-hover",
      stepActive: "bg-primary",
      stepInactive: "bg-muted",
      stepCompleted: "bg-green-500",
      borderColor: "border-accent/20",
      accentColor: "bg-accent",
      successColor: "text-green-600",
      errorColor: "text-destructive",
    };
  }

  const colors = theme.colors;
  return {
    progressBar: colors.progressBarBg,
    progressFill: colors.progressBarFill,
    formBackground: colors.formBackground,
    cardBackground: colors.cardBackground,
    stepBackground: colors.stepBackground,
    primaryText: colors.primaryText,
    secondaryText: colors.secondaryText,
    accentText: colors.accentText,
    primaryButton: colors.primaryButton,
    primaryButtonHover: colors.primaryButtonHover,
    secondaryButton: colors.secondaryButton,
    secondaryButtonHover: colors.secondaryButtonHover,
    stepActive: colors.stepActiveColor,
    stepInactive: colors.stepInactiveColor,
    stepCompleted: colors.stepCompletedColor,
    borderColor: colors.borderColor,
    accentColor: colors.accentColor,
    successColor: colors.successColor,
    errorColor: colors.errorColor,
  };
}

/**
 * Utility function to convert theme colors to CSS custom properties
 */
export function themeToCSS(theme: FormThemeConfig): Record<string, string> {
  const colors = theme.colors;
  
  // Simple mapping from Tailwind classes to CSS values
  const tailwindToCSS = (className: string): string => {
    // This is a simplified conversion - you might want to expand this
    const colorMap: Record<string, string> = {
      'bg-primary': 'var(--primary)',
      'bg-secondary': 'var(--secondary)',
      'bg-accent': 'var(--accent)',
      'text-primary': 'var(--primary)',
      'text-secondary': 'var(--secondary)',
      'bg-white': '#ffffff',
      'bg-gray-100': '#f3f4f6',
      'bg-blue-500': '#3b82f6',
      'bg-green-500': '#10b981',
      'bg-red-500': '#ef4444',
      'text-green-600': '#059669',
      'text-red-600': '#dc2626',
      'text-destructive': 'var(--destructive)',
    };

    // Extract the main color class (remove hover states, gradients, etc.)
    const mainClass = className.split(' ')[0];
    return colorMap[mainClass] || className;
  };

  return {
    '--form-progress-bg': tailwindToCSS(colors.progressBarBg),
    '--form-progress-fill': tailwindToCSS(colors.progressBarFill),
    '--form-background': tailwindToCSS(colors.formBackground),
    '--form-card-background': tailwindToCSS(colors.cardBackground),
    '--form-step-background': tailwindToCSS(colors.stepBackground),
    '--form-primary-text': tailwindToCSS(colors.primaryText),
    '--form-secondary-text': tailwindToCSS(colors.secondaryText),
    '--form-accent-text': tailwindToCSS(colors.accentText),
    '--form-primary-button': tailwindToCSS(colors.primaryButton),
    '--form-secondary-button': tailwindToCSS(colors.secondaryButton),
    '--form-border-color': tailwindToCSS(colors.borderColor),
    '--form-accent-color': tailwindToCSS(colors.accentColor),
    '--form-success-color': tailwindToCSS(colors.successColor),
    '--form-error-color': tailwindToCSS(colors.errorColor),
    '--form-step-active': tailwindToCSS(colors.stepActiveColor),
    '--form-step-inactive': tailwindToCSS(colors.stepInactiveColor),
    '--form-step-completed': tailwindToCSS(colors.stepCompletedColor),
  };
}

/**
 * Hook for theme preview functionality
 */
export function useThemePreview() {
  const [previewTheme, setPreviewTheme] = useState<FormThemeConfig | null>(null);
  const [isPreviewMode, setIsPreviewMode] = useState(false);

  const startPreview = (theme: FormThemeConfig) => {
    setPreviewTheme(theme);
    setIsPreviewMode(true);
  };

  const stopPreview = () => {
    setPreviewTheme(null);
    setIsPreviewMode(false);
  };

  const updatePreviewTheme = (theme: FormThemeConfig) => {
    if (isPreviewMode) {
      setPreviewTheme(theme);
    }
  };

  return {
    previewTheme,
    isPreviewMode,
    startPreview,
    stopPreview,
    updatePreviewTheme,
  };
}
