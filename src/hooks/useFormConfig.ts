import { FormFieldConfig } from "@/schemas/FormSchema";
import { fetchFormConfigByName, fetchFormConfigs } from "@/services/formConfigService";
import { useEffect, useState } from "react";

/**
 * Hook to fetch and manage form configurations
 */
export function useFormConfigs() {
  const [configs, setConfigs] = useState<FormFieldConfig[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadConfigs = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await fetchFormConfigs();
      setConfigs(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load form configurations");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadConfigs();
  }, []);

  // Get fields by category and form type
  const getFieldsByCategory = (
    category: string,
    formType: "candidate" | "nannyRequest" | "both" = "both"
  ): FormFieldConfig[] => {
    return configs
      .filter(config =>
        config.isActive &&
        config.category === category &&
        (config.formType === formType || config.formType === "both")
      )
      .sort((a, b) => {
        const orderA = a.displayOrder ?? 999;
        const orderB = b.displayOrder ?? 999;
        if (orderA !== orderB) {
          return orderA - orderB;
        }
        return a.fieldKey.localeCompare(b.fieldKey);
      });
  };

  // Get field configuration by key
  const getFieldConfig = (fieldKey: string): FormFieldConfig | null => {
    return configs.find(config => config.fieldKey === fieldKey && config.isActive) || null;
  };

  // Get all available categories for a form type
  const getCategoriesForFormType = (formType: "candidate" | "nannyRequest" | "both" = "both"): string[] => {
    const categories = new Set<string>();
    configs
      .filter(config =>
        config.isActive &&
        (config.formType === formType || config.formType === "both")
      )
      .forEach(config => categories.add(config.category));
    return Array.from(categories).sort();
  };

  // Get fields grouped by category
  const getFieldsGroupedByCategory = (
    formType: "candidate" | "nannyRequest" | "both" = "both"
  ): Record<string, FormFieldConfig[]> => {
    const grouped: Record<string, FormFieldConfig[]> = {};

    configs
      .filter(config =>
        config.isActive &&
        (config.formType === formType || config.formType === "both")
      )
      .forEach(config => {
        if (!grouped[config.category]) {
          grouped[config.category] = [];
        }
        grouped[config.category].push(config);
      });

    // Sort fields within each category
    Object.keys(grouped).forEach(category => {
      grouped[category].sort((a, b) => {
        const orderA = a.displayOrder ?? 999;
        const orderB = b.displayOrder ?? 999;
        if (orderA !== orderB) {
          return orderA - orderB;
        }
        return a.fieldKey.localeCompare(b.fieldKey);
      });
    });

    return grouped;
  };

  return {
    configs,
    loading,
    error,
    refetch: loadConfigs,
    getFieldsByCategory,
    getFieldConfig,
    getCategoriesForFormType,
    getFieldsGroupedByCategory,
  };
}

/**
 * Hook to fetch a specific form configuration by name
 */
export function useFormConfig(name: string) {
  const [config, setConfig] = useState<FormFieldConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadConfig = async () => {
    if (!name) return;
    
    setLoading(true);
    setError(null);
    try {
      const data = await fetchFormConfigByName(name);
      setConfig(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load form configuration");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadConfig();
  }, [name]);

  return {
    config,
    loading,
    error,
    refetch: loadConfig,
  };
}

/**
 * Hook to get form options in the format expected by existing components
 */
export function useFormOptions(configName: string) {
  const { config, loading, error } = useFormConfig(configName);

  // Transform config options to the format expected by existing components
  const options = config?.options.map(option => ({
    id: option.id,
    label: option.labelEn,
    labelEl: option.labelEl,
    labelEn: option.labelEn,
  })) || [];

  return {
    options,
    loading,
    error,
    config,
  };
}

/**
 * Utility function to get localized label from form option
 */
export function getFormOptionLabel(option: { labelEl: string; labelEn: string }, language: "el" | "en" = "en"): string {
  return language === "el" ? option.labelEl : option.labelEn;
}

/**
 * Utility function to get form configuration title
 */
export function getFormConfigTitle(config: FormFieldConfig | null, language: "el" | "en" = "en"): string {
  if (!config) return "";
  return language === "el" ? config.titleEl : config.titleEn;
}

/**
 * Utility function to get form configuration placeholder
 */
export function getFormConfigPlaceholder(config: FormFieldConfig | null, language: "el" | "en" = "en"): string {
  if (!config) return "";
  const placeholder = language === "el" ? config.placeholderEl : config.placeholderEn;
  return placeholder || "";
}

/**
 * Utility function to get form configuration description
 */
export function getFormConfigDescription(config: FormFieldConfig | null, language: "el" | "en" = "en"): string {
  if (!config) return "";
  const description = language === "el" ? config.descriptionEl : config.descriptionEn;
  return description || "";
}
