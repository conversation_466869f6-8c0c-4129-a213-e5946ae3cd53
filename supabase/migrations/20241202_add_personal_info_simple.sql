-- Simple migration to add Personal Info fields to existing form_field_configs table
-- This works with the current database structure

-- Insert Personal Information fields using existing columns only
INSERT INTO form_field_configs (name, title_el, title_en, field_type, is_active) VALUES

-- Basic Personal Information
('firstName', 'Όνομα', 'First Name', 'text', true),
('lastName', 'Επίθετο', 'Last Name', 'text', true),
('birthDate', 'Ημερομηνία Γέννησης', 'Birth Date', 'text', true),
('contactNumber', 'Τηλέφωνο', 'Contact Number', 'text', true),
('email', 'E-mail', 'E-mail', 'text', true),
('gender', 'Φύλο', 'Gender', 'radio', true),
('nationality', 'Εθνικότητα-Υπηκοότητα', 'Nationality-Citizenship', 'text', true),
('workDocuments', 'Έγγραφα Εργασίας', 'Work Documents', 'radio', true),

-- Address Information
('address', 'Διεύθυνση', 'Address', 'text', true),
('addressNumber', 'Αριθμός', 'Number', 'text', true),
('area', 'Περιοχή', 'Area', 'text', true),
('postalCode', 'Ταχυδρομικός Κώδικας', 'Postal Code', 'text', true),
('city', 'Πόλη', 'City', 'select', true),
('country', 'Χώρα', 'Country', 'text', true),

-- Optional Personal Information
('hasAllergies', 'Αλλεργίες', 'Allergies', 'radio', true),
('allergyDetails', 'Λεπτομέρειες Αλλεργιών', 'Allergy Details', 'textarea', true),
('socialMediaPlatform', 'Πλατφόρμα Social Media', 'Social Media Platform', 'select', true),
('socialMediaHandle', 'Όνομα Χρήστη Social Media', 'Social Media Handle', 'text', true),
('passportActive', 'Ενεργό Διαβατήριο', 'Active Passport', 'radio', true);

-- Insert options for radio button fields
INSERT INTO form_field_options (field_config_id, label_el, label_en, order_index, is_active) VALUES

-- Gender options
((SELECT id FROM form_field_configs WHERE name = 'gender'), 'Άνδρας', 'Male', 0, true),
((SELECT id FROM form_field_configs WHERE name = 'gender'), 'Γυναίκα', 'Female', 1, true),

-- Work Documents options
((SELECT id FROM form_field_configs WHERE name = 'workDocuments'), 'Ναι', 'Yes', 0, true),
((SELECT id FROM form_field_configs WHERE name = 'workDocuments'), 'Όχι', 'No', 1, true),

-- Allergies options
((SELECT id FROM form_field_configs WHERE name = 'hasAllergies'), 'Ναι', 'Yes', 0, true),
((SELECT id FROM form_field_configs WHERE name = 'hasAllergies'), 'Όχι', 'No', 1, true),

-- Passport options
((SELECT id FROM form_field_configs WHERE name = 'passportActive'), 'Ναι', 'Yes', 0, true),
((SELECT id FROM form_field_configs WHERE name = 'passportActive'), 'Όχι', 'No', 1, true),

-- Social Media Platform options (basic set)
((SELECT id FROM form_field_configs WHERE name = 'socialMediaPlatform'), 'Facebook', 'Facebook', 0, true),
((SELECT id FROM form_field_configs WHERE name = 'socialMediaPlatform'), 'Instagram', 'Instagram', 1, true),
((SELECT id FROM form_field_configs WHERE name = 'socialMediaPlatform'), 'LinkedIn', 'LinkedIn', 2, true),
((SELECT id FROM form_field_configs WHERE name = 'socialMediaPlatform'), 'TikTok', 'TikTok', 3, true),
((SELECT id FROM form_field_configs WHERE name = 'socialMediaPlatform'), 'YouTube', 'YouTube', 4, true),
((SELECT id FROM form_field_configs WHERE name = 'socialMediaPlatform'), 'Twitter/X', 'Twitter/X', 5, true);

-- Add some basic city options (you can add more later)
INSERT INTO form_field_options (field_config_id, label_el, label_en, order_index, is_active) VALUES
((SELECT id FROM form_field_configs WHERE name = 'city'), 'Αθήνα', 'Athens', 0, true),
((SELECT id FROM form_field_configs WHERE name = 'city'), 'Θεσσαλονίκη', 'Thessaloniki', 1, true),
((SELECT id FROM form_field_configs WHERE name = 'city'), 'Πάτρα', 'Patras', 2, true),
((SELECT id FROM form_field_configs WHERE name = 'city'), 'Ηράκλειο', 'Heraklion', 3, true),
((SELECT id FROM form_field_configs WHERE name = 'city'), 'Λάρισα', 'Larissa', 4, true),
((SELECT id FROM form_field_configs WHERE name = 'city'), 'Βόλος', 'Volos', 5, true),
((SELECT id FROM form_field_configs WHERE name = 'city'), 'Ιωάννινα', 'Ioannina', 6, true),
((SELECT id FROM form_field_configs WHERE name = 'city'), 'Καβάλα', 'Kavala', 7, true),
((SELECT id FROM form_field_configs WHERE name = 'city'), 'Χανιά', 'Chania', 8, true),
((SELECT id FROM form_field_configs WHERE name = 'city'), 'Άλλη', 'Other', 99, true);
