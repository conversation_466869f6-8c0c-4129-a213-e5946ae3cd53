-- Migration to add Personal Info Step fields to dynamic form configuration
-- This adds all fields from PersonalInfoStep.tsx to the form_field_configs table

-- First, ensure all required columns exist (run main migration first if needed)
ALTER TABLE form_field_configs
ADD COLUMN IF NOT EXISTS config_name <PERSON><PERSON><PERSON><PERSON>(255),
ADD COLUMN IF NOT EXISTS field_key VARCHAR(255),
ADD COLUMN IF NOT EXISTS category VARCHAR(100),
ADD COLUMN IF NOT EXISTS label_el TEXT,
ADD COLUMN IF NOT EXISTS label_en TEXT,
ADD COLUMN IF NOT EXISTS conditional_field_key VARCHAR(255),
ADD COLUMN IF NOT EXISTS conditional_value TEXT,
ADD COLUMN IF NOT EXISTS conditional_operator VARCHAR(20) DEFAULT 'equals',
ADD COLUMN IF NOT EXISTS validation_rules JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS custom_validation_message_el TEXT,
ADD COLUMN IF NOT EXISTS custom_validation_message_en TEXT;

-- Update existing records to have required field values if missing
UPDATE form_field_configs
SET
    config_name = COALESCE(config_name, name),
    field_key = COALESCE(field_key, name),
    label_el = COALESCE(label_el, title_el),
    label_en = COALESCE(label_en, title_en)
WHERE config_name IS NULL OR field_key IS NULL OR label_el IS NULL OR label_en IS NULL;

-- Insert Personal Information fields
INSERT INTO form_field_configs (
    name, config_name, field_key, category,
    title_el, title_en, label_el, label_en,
    placeholder_el, placeholder_en, description_el, description_en,
    field_type, is_active, is_required, form_type, display_order
) VALUES

-- Basic Personal Information
('firstName', 'firstName', 'name', 'personalInfo',
 'Όνομα', 'First Name', 'Όνομα', 'Name',
 'Όνομα', 'Name', '', '',
 'text', true, true, 'candidate', 1),

('lastName', 'lastName', 'surname', 'personalInfo', 
 'Επίθετο', 'Last Name', 'Επίθετο', 'Surname',
 'Επίθετο', 'Surname', '', '',
 'text', true, true, 'candidate', 2),

('birthDate', 'birthDate', 'birthDate', 'personalInfo',
 'Ημερομηνία Γέννησης', 'Birth Date', 'Ημερομηνία Γέννησης', 'Birth Date',
 '', '', '', '',
 'date', true, true, 'candidate', 3),

('contactNumber', 'contactNumber', 'contactNumber', 'personalInfo',
 'Τηλέφωνο', 'Contact Number', 'Τηλέφωνο', 'Contact Number',
 '+30 ************', '+30 ************', 'Με κωδικό περιοχής ή χώρας', 'With Area or Country Code',
 'tel', true, true, 'candidate', 4),

('email', 'email', 'email', 'personalInfo',
 'E-mail', 'E-mail', 'E-mail', 'E-mail',
 '<EMAIL>', '<EMAIL>', '', '',
 'email', true, true, 'candidate', 5),

('nationality', 'nationality', 'nationality', 'personalInfo',
 'Εθνικότητα-Υπηκοότητα', 'Nationality-Citizenship', 'Εθνικότητα-Υπηκοότητα', 'Nationality-Citizenship',
 'Εθνικότητα-Υπηκοότητα', 'Nationality-Citizenship', '', '',
 'text', true, true, 'candidate', 6),

-- Address Information
('address', 'address', 'address', 'address',
 'Διεύθυνση', 'Address', 'Διεύθυνση', 'Address',
 'Οδός', 'Street', '', '',
 'text', true, true, 'candidate', 10),

('addressNumber', 'addressNumber', 'addressNumber', 'address',
 'Αριθμός', 'Number', 'Αριθμός', 'Number',
 'Αριθμός', 'Number', '', '',
 'number', true, true, 'candidate', 11),

('area', 'area', 'area', 'address',
 'Περιοχή', 'Area', 'Περιοχή', 'Area',
 'Περιοχή', 'Area', '', '',
 'text', true, true, 'candidate', 12),

('postalCode', 'postalCode', 'postalCode', 'address',
 'Ταχυδρομικός Κώδικας', 'Postal Code', 'Ταχυδρομικός Κώδικας', 'Postal Code',
 'Τ.Κ.', 'Postal Code', '', '',
 'text', true, true, 'candidate', 13),

('city', 'city', 'city', 'address',
 'Πόλη', 'City', 'Πόλη', 'City',
 'Επιλέξτε πόλη', 'Select city', '', '',
 'select', true, true, 'candidate', 14),

('country', 'country', 'country', 'address',
 'Χώρα', 'Country', 'Χώρα', 'Country',
 'Χώρα', 'Country', '', '',
 'text', true, true, 'candidate', 15),

-- Optional Personal Information
('allergyDetails', 'allergyDetails', 'allergies.allergyDetails', 'personalInfo',
 'Λεπτομέρειες Αλλεργιών', 'Allergy Details', 'Παρακαλώ περιγράψτε τις αλλεργίες σας', 'Please describe your allergies',
 'π.χ. Αλλεργία στη γλουτένη, φιστίκια', 'e.g., Gluten allergy, peanuts', '', '',
 'textarea', true, false, 'candidate', 20),

('socialMediaPlatform', 'socialMediaPlatform', 'socialMedia.platform', 'contact',
 'Πλατφόρμα Social Media', 'Social Media Platform', 'Πλατφόρμα', 'Platform',
 'Επιλέξτε πλατφόρμα', 'Select platform', 'Προαιρετικό', 'Optional',
 'select', true, false, 'candidate', 21),

('socialMediaHandle', 'socialMediaHandle', 'socialMedia.handle', 'contact',
 'Όνομα Χρήστη Social Media', 'Social Media Handle', 'Όνομα χρήστη', 'Handle or Link',
 'π.χ. yourprofile', 'eg. yourprofile.com', 'Προαιρετικό', 'Optional',
 'text', true, false, 'candidate', 22);

-- Insert Radio Button Options for Gender
INSERT INTO form_field_configs (
    name, config_name, field_key, category,
    title_el, title_en, label_el, label_en,
    field_type, is_active, is_required, form_type, display_order
) VALUES
('gender', 'gender', 'gender', 'personalInfo',
 'Φύλο', 'Gender', 'Φύλο', 'Gender',
 'radio', true, true, 'candidate', 7);

-- Insert Radio Button Options for Work Documents
INSERT INTO form_field_configs (
    name, config_name, field_key, category,
    title_el, title_en, label_el, label_en,
    field_type, is_active, is_required, form_type, display_order
) VALUES
('workDocuments', 'workDocuments', 'workDocuments', 'personalInfo',
 'Έγγραφα Εργασίας', 'Work Documents', 'Υπάρχουν τα κατάλληλα έγγραφα για εργασία στην Ελλάδα;', 'Do you have the documents required to work in Greece?',
 'radio', true, true, 'candidate', 8);

-- Insert Radio Button Options for Allergies
INSERT INTO form_field_configs (
    name, config_name, field_key, category,
    title_el, title_en, label_el, label_en,
    field_type, is_active, is_required, form_type, display_order,
    conditional_field_key, conditional_value, conditional_operator
) VALUES
('hasAllergies', 'hasAllergies', 'allergies.hasAllergies', 'personalInfo',
 'Αλλεργίες', 'Allergies', 'Έχετε αλλεργίες;', 'Do you have any allergies?',
 'radio', true, false, 'candidate', 19, '', '', 'equals');

-- Insert Radio Button Options for Passport
INSERT INTO form_field_configs (
    name, config_name, field_key, category,
    title_el, title_en, label_el, label_en,
    field_type, is_active, is_required, form_type, display_order
) VALUES
('passportActive', 'passportActive', 'passportActive', 'personalInfo',
 'Ενεργό Διαβατήριο', 'Active Passport', 'Έχετε ενεργό διαβατήριο;', 'Do you have an active passport?',
 'radio', true, false, 'candidate', 23);

-- Now insert the options for radio button fields
INSERT INTO form_field_options (field_config_id, label_el, label_en, order_index, is_active) VALUES

-- Gender options
((SELECT id FROM form_field_configs WHERE name = 'gender'), 'Άνδρας', 'Male', 0, true),
((SELECT id FROM form_field_configs WHERE name = 'gender'), 'Γυναίκα', 'Female', 1, true),

-- Work Documents options
((SELECT id FROM form_field_configs WHERE name = 'workDocuments'), 'Ναι', 'Yes', 0, true),
((SELECT id FROM form_field_configs WHERE name = 'workDocuments'), 'Όχι', 'No', 1, true),

-- Allergies options
((SELECT id FROM form_field_configs WHERE name = 'hasAllergies'), 'Ναι', 'Yes', 0, true),
((SELECT id FROM form_field_configs WHERE name = 'hasAllergies'), 'Όχι', 'No', 1, true),

-- Passport options
((SELECT id FROM form_field_configs WHERE name = 'passportActive'), 'Ναι', 'Yes', 0, true),
((SELECT id FROM form_field_configs WHERE name = 'passportActive'), 'Όχι', 'No', 1, true);

-- Update the allergy details field to be conditional on hasAllergies = 'yes'
UPDATE form_field_configs 
SET 
    conditional_field_key = 'allergies.hasAllergies',
    conditional_value = 'yes',
    conditional_operator = 'equals',
    is_required = true
WHERE name = 'allergyDetails';

-- Add validation rules for email field
UPDATE form_field_configs 
SET validation_rules = '{"email": true, "required": true}'
WHERE name = 'email';

-- Add validation rules for contact number
UPDATE form_field_configs 
SET validation_rules = '{"pattern": "^[+]?[0-9\\s\\-\\(\\)]+$", "minLength": 10}'
WHERE name = 'contactNumber';

-- Add validation rules for postal code
UPDATE form_field_configs 
SET validation_rules = '{"pattern": "^[0-9]{5}$", "minLength": 5, "maxLength": 5}'
WHERE name = 'postalCode';

-- Add validation rules for address number
UPDATE form_field_configs 
SET validation_rules = '{"min": 1, "max": 9999}'
WHERE name = 'addressNumber';
