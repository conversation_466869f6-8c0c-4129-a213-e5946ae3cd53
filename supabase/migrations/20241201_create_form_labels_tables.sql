-- Create form_label_categories table
CREATE TABLE IF NOT EXISTS form_label_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    title_el TEXT NOT NULL,
    title_en TEXT NOT NULL,
    form_type VARCHAR(50) NOT NULL CHECK (form_type IN ('candidate', 'nannyRequest', 'both')),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create form_labels table
CREATE TABLE IF NOT EXISTS form_labels (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    key VARCHAR(255) NOT NULL UNIQUE,
    category VARCHAR(255) NOT NULL,
    context VARCHAR(100) NOT NULL CHECK (context IN ('label', 'placeholder', 'description', 'title', 'button', 'message')),
    label_el TEXT NOT NULL,
    label_en TEXT NOT NULL,
    is_active BOOLEAN DEFAULT true,
    form_type VARCHAR(50) CHECK (form_type IN ('candidate', 'nannyRequest', 'both')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_form_label_categories_name ON form_label_categories(name);
CREATE INDEX IF NOT EXISTS idx_form_label_categories_form_type ON form_label_categories(form_type);
CREATE INDEX IF NOT EXISTS idx_form_label_categories_is_active ON form_label_categories(is_active);
CREATE INDEX IF NOT EXISTS idx_form_labels_key ON form_labels(key);
CREATE INDEX IF NOT EXISTS idx_form_labels_category ON form_labels(category);
CREATE INDEX IF NOT EXISTS idx_form_labels_context ON form_labels(context);
CREATE INDEX IF NOT EXISTS idx_form_labels_form_type ON form_labels(form_type);
CREATE INDEX IF NOT EXISTS idx_form_labels_is_active ON form_labels(is_active);

-- Create triggers to update updated_at timestamp
CREATE TRIGGER update_form_label_categories_updated_at 
    BEFORE UPDATE ON form_label_categories 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_form_labels_updated_at 
    BEFORE UPDATE ON form_labels 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert form label categories
INSERT INTO form_label_categories (name, title_el, title_en, form_type, is_active) VALUES
('personalInfo', 'Προσωπικά Στοιχεία', 'Personal Information', 'both', true),
('address', 'Διεύθυνση', 'Address', 'both', true),
('children', 'Πληροφορίες Παιδιών', 'Children Information', 'nannyRequest', true),
('jobDetails', 'Λεπτομέρειες Εργασίας', 'Job Details', 'nannyRequest', true),
('skills', 'Δεξιότητες', 'Skills', 'candidate', true),
('education', 'Εκπαίδευση', 'Education', 'candidate', true),
('experience', 'Εμπειρία', 'Experience', 'candidate', true),
('availability', 'Διαθεσιμότητα', 'Availability', 'candidate', true),
('socialMedia', 'Κοινωνικά Δίκτυα', 'Social Media', 'both', true),
('navigation', 'Πλοήγηση', 'Navigation', 'both', true);

-- Insert form labels for Personal Information
INSERT INTO form_labels (key, category, context, label_el, label_en, is_active, form_type) VALUES
-- Personal Info Labels
('personalInfo.title', 'personalInfo', 'title', 'Προσωπικά Στοιχεία', 'Personal Information', true, 'both'),
('fatherName.label', 'personalInfo', 'label', 'Ονοματεπώνυμο Πατέρα', 'Father''s Full Name', true, 'nannyRequest'),
('fatherName.placeholder', 'personalInfo', 'placeholder', 'Όνομα Επώνυμο', 'First Last Name', true, 'nannyRequest'),
('fatherOccupation.label', 'personalInfo', 'label', 'Επάγγελμα Πατέρα', 'Father''s Occupation', true, 'nannyRequest'),
('fatherOccupation.placeholder', 'personalInfo', 'placeholder', 'Επάγγελμα', 'Occupation', true, 'nannyRequest'),
('motherName.label', 'personalInfo', 'label', 'Ονοματεπώνυμο Μητέρας', 'Mother''s Full Name', true, 'nannyRequest'),
('motherName.placeholder', 'personalInfo', 'placeholder', 'Όνομα Επώνυμο', 'First Last Name', true, 'nannyRequest'),
('motherOccupation.label', 'personalInfo', 'label', 'Επάγγελμα Μητέρας', 'Mother''s Occupation', true, 'nannyRequest'),
('motherOccupation.placeholder', 'personalInfo', 'placeholder', 'Επάγγελμα', 'Occupation', true, 'nannyRequest'),
('parentNationality.label', 'personalInfo', 'label', 'Εθνικότητα Γονέων', 'Nationality of Parents', true, 'nannyRequest'),
('parentNationality.placeholder', 'personalInfo', 'placeholder', 'Εθνικότητα', 'Nationality', true, 'nannyRequest'),
('childrenNationality.label', 'personalInfo', 'label', 'Εθνικότητα Παιδιού', 'Nationality of Child', true, 'nannyRequest'),
('childrenNationality.placeholder', 'personalInfo', 'placeholder', 'Εθνικότητα', 'Nationality', true, 'nannyRequest'),
('contactNumber.label', 'personalInfo', 'label', 'Τηλέφωνο', 'Contact Number', true, 'both'),
('contactNumber.description', 'personalInfo', 'description', 'Με κωδικό περιοχής ή χώρας', 'With Area or Country Code', true, 'both'),
('email.label', 'personalInfo', 'label', 'E-mail', 'E-mail', true, 'both'),
('email.description', 'personalInfo', 'description', 'Απαραίτητο , εδώ θα λάβετε τα στοιχεία σας', 'Required, here you will receive your information', true, 'both'),

-- Address Labels
('address.title', 'address', 'title', 'Διεύθυνση', 'Address', true, 'both'),
('permanentAddress.label', 'address', 'label', 'Διεύθυνση Μόνιμης Κατοικίας', 'Address of your Permanent Residence', true, 'both'),
('permanentAddress.description', 'address', 'description', 'Διεύθυνση, αριθμός, περιοχή και Τ.Κ.', 'Address, number, area, postal code etc.', true, 'both'),
('permanentAddress.placeholder', 'address', 'placeholder', 'Οδός Παραδείγματος 123, Αθήνα, 12345', 'Example Street 123, Athens, 12345', true, 'both'),
('workingAddressSameAsPermanent.label', 'address', 'label', 'Η ίδια με της μόνιμης κατοικίας', 'The same as the permanent residence', true, 'nannyRequest'),
('workingAddress.label', 'address', 'label', 'Τοποθεσία-Περιοχή Θέσης Εργασίας', 'Place-Area where the suggested working positions take place', true, 'nannyRequest'),
('workingAddress.description', 'address', 'description', 'Άλλη (και ποια)', 'Other (Please write the proper one)', true, 'nannyRequest'),
('workingAddress.placeholder', 'address', 'placeholder', 'Οδός Εργασίας 456, Αθήνα, 12345', 'Work Street 456, Athens, 12345', true, 'nannyRequest'),

-- Children Information Labels
('children.title', 'children', 'title', 'Πληροφορίες Παιδιών', 'Children Information', true, 'nannyRequest'),
('childrenCount.label', 'children', 'label', 'Πόσα παιδιά αφορά η θέση εργασίας', 'How many children are you looking for a nanny for?', true, 'nannyRequest'),
('childrenAge.label', 'children', 'label', 'Και ποιά είναι η ηλικία των παιδιών;', 'What is the age range of your children ?', true, 'nannyRequest'),
('childrenLanguages.label', 'children', 'label', 'Τί γλώσσα/ες μιλάνε τα παιδιά;', 'What languages do your children speak?', true, 'nannyRequest'),
('childSpecialInfo.label', 'children', 'label', 'Οποιαδήποτε πληροφορία σχετικά με το παιδί που θα έπρεπε να γνωρίζουμε;', 'Any special information about the child we should be informed of?', true, 'nannyRequest'),
('childSpecialInfo.placeholder', 'children', 'placeholder', 'Παρακαλώ περιγράψτε οποιαδήποτε ειδική πληροφορία...', 'Please describe any special information...', true, 'nannyRequest'),

-- Social Media Labels
('socialMedia.title', 'socialMedia', 'title', 'Social Media (Προαιρετικό)', 'Social Media (Optional)', true, 'both'),
('socialMedia.platform.label', 'socialMedia', 'label', 'Πλατφόρμα', 'Platform', true, 'both'),
('socialMedia.platform.placeholder', 'socialMedia', 'placeholder', 'Επιλέξτε πλατφόρμα', 'Select platform', true, 'both'),
('socialMedia.handle.label', 'socialMedia', 'label', 'Όνομα χρήστη', 'Handle or Link', true, 'both'),
('socialMedia.handle.placeholder', 'socialMedia', 'placeholder', 'π.χ. yourprofile', 'eg. yourprofile.com', true, 'both'),
('socialMedia.add.button', 'socialMedia', 'button', 'Προσθήκη Social Media', 'Add Social Media', true, 'both'),

-- Navigation Labels
('navigation.next', 'navigation', 'button', 'Επόμενο', 'Next', true, 'both'),
('navigation.previous', 'navigation', 'button', 'Προηγούμενο', 'Previous', true, 'both'),
('navigation.submit', 'navigation', 'button', 'Υποβολή', 'Submit', true, 'both'),
('navigation.save', 'navigation', 'button', 'Αποθήκευση', 'Save', true, 'both'),
('navigation.cancel', 'navigation', 'button', 'Ακύρωση', 'Cancel', true, 'both'),

-- Candidate Form Specific Labels
('candidateName.label', 'personalInfo', 'label', 'Ονοματεπώνυμο', 'Full Name', true, 'candidate'),
('candidateName.placeholder', 'personalInfo', 'placeholder', 'Όνομα Επώνυμο', 'First Last Name', true, 'candidate'),
('candidateType.label', 'personalInfo', 'label', 'Τύπος Υποψηφίου', 'Candidate Type', true, 'candidate'),
('musicalInstruments.label', 'skills', 'label', 'Μουσικά Όργανα', 'Musical Instruments', true, 'candidate'),
('musicTheory.label', 'skills', 'label', 'Θεωρία Μουσικής', 'Music Theory', true, 'candidate'),
('education.label', 'education', 'label', 'Εκπαίδευση', 'Education', true, 'candidate'),
('specialization.label', 'skills', 'label', 'Ειδικότητες', 'Specializations', true, 'candidate'),
('firstAid.label', 'skills', 'label', 'Πρώτες Βοήθειες', 'First Aid', true, 'candidate'),

-- Job Details Labels (for nanny request)
('position.label', 'jobDetails', 'label', 'Θέσεις Εργασίας', 'Job Positions', true, 'nannyRequest'),
('schedule.label', 'jobDetails', 'label', 'Ωράρια', 'Schedules', true, 'nannyRequest'),
('duration.label', 'jobDetails', 'label', 'Διάρκεια', 'Duration', true, 'nannyRequest'),
('startDate.label', 'jobDetails', 'label', 'Ημερομηνία Έναρξης', 'Start Date', true, 'nannyRequest');

-- Enable Row Level Security (RLS)
ALTER TABLE form_label_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE form_labels ENABLE ROW LEVEL SECURITY;

-- Create policies for form_label_categories
CREATE POLICY "Allow read access to form_label_categories" ON form_label_categories
    FOR SELECT USING (true);

CREATE POLICY "Allow insert access to form_label_categories for authenticated users" ON form_label_categories
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Allow update access to form_label_categories for authenticated users" ON form_label_categories
    FOR UPDATE USING (auth.role() = 'authenticated');

CREATE POLICY "Allow delete access to form_label_categories for authenticated users" ON form_label_categories
    FOR DELETE USING (auth.role() = 'authenticated');

-- Create policies for form_labels
CREATE POLICY "Allow read access to form_labels" ON form_labels
    FOR SELECT USING (true);

CREATE POLICY "Allow insert access to form_labels for authenticated users" ON form_labels
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Allow update access to form_labels for authenticated users" ON form_labels
    FOR UPDATE USING (auth.role() = 'authenticated');

CREATE POLICY "Allow delete access to form_labels for authenticated users" ON form_labels
    FOR DELETE USING (auth.role() = 'authenticated');
